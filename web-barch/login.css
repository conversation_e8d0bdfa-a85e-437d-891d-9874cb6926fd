* {
    margin: 0;
    padding: 0;
    outline: none;
}

html,
body {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
}
body {
    background-image: url(/static/img/login/img_beijing_c_s.png);
}
input {
    background-color: transparent;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    background-image: none;
    -webkit-transition-delay: 99999s;
    transition: color 99999s ease-out, background-color 99999s ease-out;
}
input:-webkit-autofill {
    /* -webkit-box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0.85) inset; */
}

.layui-unselect {
    display: none;
}

.none {
    display: none;
}

.clear {
    clear: both;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    color: #999;
    font-size: 14px;
}

input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
    color: #999;
    font-size: 14px;
}

input::-moz-input-placeholder,
textarea::-moz-input-placeholder {
    color: #999;
    font-size: 14px;
}
.alink {
    color: #e0e0e0;
    text-decoration: none;
}
.login__flex {
    width: 100%;
    height: 100%;
    /* display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    -ms-flex-direction: column; */
}

.login__box {
    width: 800px;
    min-height: 608px;
    height: 100%;
    margin: 0 auto;
    position: relative;
}

.logo {
    height: 72px;
    width: 288px;
    position: absolute;
    left: 50%;
    margin-left: -144px;
    top: 50%;
    margin-top: -303px;
}
.logo img {
    height: 100%;
    width: 100%;
    display: block;
}
.login__box .box {
    /* opacity: 0.85; */
    width: 360px;
    height: 400px;
    margin-top: 20px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -180px;
    margin-top: -200px;
    /* background-color: #fff; */
    border-radius: 14px;
    background-color: rgba(255, 255, 255, 0.85);
    /* padding-left: 400px;
    background: url(/static/img/login/img_chahua.png) no-repeat center center/cover; */
}

.title {
    text-align: center;
    font-size: 24px;
    color: #464646;
    padding-top: 64px;
    line-height: 1;
    margin-bottom: 25px;
    font-weight: 600;
}

.input__box {
    /* width: 280px; */
    width: 100%;
    height: 23px;
    margin: 0 auto;
    border-bottom: 1px #a2a2a2 solid;
    margin-top: 16px;
    padding: 13px 0;
}

.input__box .icon {
    width: 23px;
    height: 23px;
    float: left;
    line-height: 23px;
    padding: 0 8px 0 8px;
}

.input__box .input,
.input__box .input input {
    width: calc(100% - 40px);
    height: 23px;
    float: left;
    position: relative;
}
.input__box .input .changeStatus {
    position: absolute;
    top: 0;
    height: 24px;
    width: 24px;
    cursor: pointer;
    color: #999;
    line-height: 24px;
    font-size: 18px;
    text-align: center;
}
.input__box .input.show .changeStatus {
    color: #13acf2;
}
.input__box .inputall {
    width: 100%;
}

.input__box .input input {
    border: 0;
    font-size: 14px;
}

.memorypsd {
    /* width: 274px; */
    margin: 0 auto;
    /* padding: 30px 3px; */
    padding-top: 24px;
    color: #4f4f4f;
    font-size: 14px;
    padding-left: 8px;
}

.memorypsd a {
    text-decoration: none;
    color: #13acf2;
}

.memorypsd input {
    opacity: 0.85;
    border: 1px #ccc solid;
    float: left;
    margin-top: 3px;
    margin-right: 5px;
}

.loginbtn__box {
    overflow: hidden;
}

.loginbtn {
    width: 100%;
    height: 40px;
    display: block;
    margin: 28px auto 0;
    background-color: #5260ff;
    background: linear-gradient(270deg, #1daaed 0%, #167ae9 100%);
    color: #fff;
    text-align: center;
    line-height: 40px;
    border-radius: 20px;
    box-shadow: 0px 4px 8px 0px rgba(102, 255, 207, 0.35);
    border: 0;
    font-size: 16px;
    cursor: pointer;
}

.coopyright {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 14px;
    color: #000;
    line-height: 18px;
    font-size: 12px;
    text-align: center;
}
.coopyright .alink {
    color: #000;
    /* color: #fff; */
}
.wrapper {
    /* width: 400px; */
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}
.support {
    padding-top: 12px;
    font-size: 12px;
    color: #333;
    line-height: 17px;
    text-align: center;
}
.support a {
    color: #333;
    font-size: 0;
    line-height: 17px;
    text-decoration: none;
}
.support span {
    font-size: 12px;
    padding-right: 10px;
}
.support img {
    vertical-align: top;
    display: inline-block;
    font-size: 0;
    margin-top: 2px;
}
.browerSupport {
    position: absolute;
    bottom: 0;
    text-align: center;
}
.steps {
    width: 2200px;
    margin-left: 0px;
}

.step {
    float: left;
    width: 240px;
    padding: 0 60px;
    height: 100%;
}
