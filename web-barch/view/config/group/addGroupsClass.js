"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'layedit'], function () {

            var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload,
                layer = layui.layer, element = layui.element, zTreeSearch = layui.zTreeSearch,
                layedit = layui.layedit;

            var groupClass = layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['500px', '300px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#evaGroupsClassBaseAddForm'), opt.data);
                        $('#id').val(opt.data.id);
                        $('#GroupsClassName').val(opt.data.className);
                    }
                    form.render();
                }
            });

            //关闭 closeSys_evaGroupsClass
            $('#closeSys_evaGroupsClass').funs('click', function () {
                layer.close(groupClass);
            });

            // 保存
            form.on('submit(sys_evaGroupsClassSubmitCofirm)', function (data) {
                var className = $('#GroupsClassName').val();
                var d = data.field;
                d.className = className;
                d.classType = 0;
                var url;
                if (d.id) {
                    url = '/ts-basics-bottom/employee/orgGroupClass/update';
                } else {
                    url = '/ts-basics-bottom/employee/orgGroupClass/save';
                }
                if (!d.id) {
                    delete d.id;
                }

                $.loadings();
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    dateType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.close(groupClass)
                            trasen.info('操作成功');
                            opt.ref();
                        }
                        else
                        {
                        layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });
            });
        })
    };
});