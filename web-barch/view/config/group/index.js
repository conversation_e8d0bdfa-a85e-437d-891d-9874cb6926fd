'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;

            var SysGropuTable = new $.trasenTable('index_SysGropuTable', {
                url: common.url + '/ts-basics-bottom/employee/orgGroup/list',
                pager: '#index_SysGropuPage',
                mtype: 'post',
                shrinkToFit: true,
                sortname: 'GROUP_ORDER',
                sortorder: 'ASC',
                postData: {
                    groupType: 0,
                }, //系统群组
                colModel: [
                    {
                        label: '组名称',
                        sortable: false,
                        name: 'groupName',
                        width: 130,
                        align: 'left',
                        formatter: function (cellvalue, options, rowObject) {
                            return cellvalue ? cellvalue : '';
                        },
                    },
                    {
                        label: '组名称',
                        sortable: false,
                        name: 'groupName',
                        width: 130,
                        align: 'left',
                        hidden: true,
                    },
                    {
                        label: '组用户',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '组用户',
                        name: 'groupUserNames',
                        width: 130,
                        align: 'left',
                        sortable: false,
                        hidden: true,
                    },
                    {
                        label: '排序',
                        sortable: false,
                        name: 'groupOrder',
                        index: 'GROUP_ORDER',
                        width: 80,
                        align: 'left',
                    },
                    {
                        label: '创建人',
                        sortable: false,
                        name: 'createUserName',
                        width: 130,
                        align: 'left',
                    },
                    {
                        label: '创建日期',
                        sortable: false,
                        name: 'createDate',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '群组分类ID',
                        sortable: false,
                        name: 'groupClassId',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '群组分类名称',
                        sortable: false,
                        name: 'groupClassName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人范围名称',
                        sortable: false,
                        name: 'rangeName',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '使用人ID',
                        sortable: false,
                        name: 'rangeEmp',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '组用户ID',
                        sortable: false,
                        name: 'groupUserString',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: '分组排序',
                        sortable: false,
                        name: 'groupOrder',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: 'groupId',
                        sortable: false,
                        name: 'groupId',
                        width: 100,
                        hidden: true,
                    },
                    {
                        label: 'rangeOrg',
                        sortable: false,
                        name: 'rangeOrg',
                        width: 100,
                        hidden: true,
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  " id="edit_sysCustomGroups" title="编辑" row-id="' + options.rowId + '"><i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i> 编辑 </button>';
                            btns += '<button class="layui-btn  " id="del_sysCustomGroups" title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                            // btns += '<button type="button" class="tableMoreBtn" id="edit_sysCustomGroups" row-id="' + options.rowId + '"> 编辑 </button>'
                            // btns += '<button type="button" class="tableGridBtn" id="del_sysCustomGroups" row-id="' + options.rowId + '"> 删除 </button>'
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#sys_GroupForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 刷新
            function refresh() {
                SysGropuTable.refresh();
            }

            //搜索
            $('#EvaBaseSearch_SysCustomGroups').funs('click', function () {
                refresh();
            });

            //重置
            $('#sysCustomGroupsReset').funs('click', function () {
                $('#sys_GroupName').val('');
                refresh();
            });

            //群组分类
            $('#Sys_CustomGroupsClass').funs('click', function () {
                $.quoteFun('/config/group/groupsClassIndex', {
                    trasen: SysGropuTable,
                    title: '群组分类',
                });
            });

            // 新增
            $('#add_SysCustomGroups').funs('click', function () {
                $.quoteFun('/config/group/addCustomGroups', {
                    trasen: SysGropuTable,
                    title: '新增系统群组',
                    ref: refresh,
                });
            });

            // 修改
            $('#edit_sysCustomGroups').funs('click', function () {
                var data = SysGropuTable.getSelectRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/config/group/addCustomGroups', {
                    trasen: SysGropuTable,
                    title: '编辑系统群组',
                    data: data,
                    ref: refresh,
                });
            });

            // 删除
            $('#del_sysCustomGroups').funs('click', function () {
                var data = SysGropuTable.getSelectRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    groupId: data.groupId,
                };
                layer.confirm(
                    '确定删除该数据?',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-basics-bottom/employee/orgGroup/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    refresh();
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });
        });
    };
});
