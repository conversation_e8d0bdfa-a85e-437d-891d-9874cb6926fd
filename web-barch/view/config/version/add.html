<style>
    .layui-col-xs11 {
        margin-bottom: 10px;
    }

    #container .layui-laydate-main {
        width: 1000px;
    }

    #container .layui-laydate-content td,
    #container .layui-laydate-content th {
        width: 87px;
        height: 50px;
    }

    #LAY_layedit_1 {
        height: 320px;
    }
</style>
<form id="versionAddForm" class="layui-form">
    <input type="hidden" name="id" value="" id="id" />
    <div class="layui-content-box">
        <div class="layui-col-xs6">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                版本号
            </label>
            <div class="shell-layer-input-box">
                <input type="text" name="version" autocomplete="off" placeholder="请输入版本号" lay-verify="required"
                    class="layui-input" id="version" />
            </div>
        </div>

        <div class="layui-col-xs6">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                版本日期
            </label>
            <div class="shell-layer-input-box">
                <input type="text" style="width: 100% !important" placeholder="请选择版本日期" name="versionDate"
                    class="layui-input shell-form-input laydates" id="versionDate" autocomplete="off"
                    lay-verify="required" readonly="" />
            </div>
        </div>

        <div class="layui-col-xs6">
            <label class="shell-layui-form-label">
                <span class="required">*</span>
                弹窗提醒：
            </label>
            <div class="shell-layer-input-box">
                <input type="checkbox" value="1" name="isPush" lay-verify="required" lay-skin="switch"
                    lay-text="" />
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-col-xs12">
                <label class="shell-layui-form-label">
                    <span class="required">*</span>
                    升级内容
                </label>
                <div class="shell-layer-input-box">
                    <textarea id="versionContent" lay-verify="content" style="display: none" name="content"></textarea>
                </div>
            </div>
        </div>

        <div class="layui-col-xs7">
            <label class="shell-layui-form-label">附件</label>
            <div class="shell-layer-input-box">
                <span id="versionFileUploadBtn"
                    style="background: #5260ff; position: relative; top: 5px; color: #fff; border-radius: 2px; font-size: 12px; padding: 5px 10px; margin-top: 5px; cursor: pointer">选择附件</span>
            </div>
        </div>

        <div class="layui-upload">
            <div class="layui-col-xs11" style="padding-left: 110px; box-sizing: border-box">
                <table class="layui-table" id="versionFileList" style="margin-top: 0; display: none">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="versionFileDataList"></tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" lay-submit=""
            lay-filter="versionSubmitCofirm">确定</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">取消</button>
    </div>
</form>