"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'layedit'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layer = layui.layer,
                layedit = layui.layedit;

            var fileArray = new Array();
            var oldFileArray = new Array();



            var editIndex;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        $('#id').val(opt.data.id);
                        trasen.setNamesVal($('#versionAddForm'), opt.data);
                        versionFiles(opt.data.id); //初始化附件信息
                    }
                    editIndex = layedit.build('versionContent');
                    //编辑器初始化
                    layedit.set({
                        tool: [
                            'fontfamily', 'fontSize', 'strong', 'italic', 'underline', 'del', 'addhr', '|', 'fontFomatt', 'fontBackColor', 'colorpicker', 'face', '|', 'left', 'center', 'right', '|', 'link', 'unlink', 'anchors', 'image_alt', 'video', 'images', //'attachment',
                            , '|', 'table', 'fullScreen'
                        ],
                        uploadImage: {
                            url: common.url + '/ts-basics-bottom/fileAttachment/upload?moduleName=version&fillupf=1',
                            accept: 'image',
                            acceptMime: 'image/*',
                            exts: 'jpg|png|gif|bmp|jpeg',
                            size: 1024 * 10,
                            done: function (data) {
                            }
                        },
                        uploadVideo: {
                            url: common.url + '/ts-basics-bottom/fileAttachment/upload?moduleName=version',
                            accept: 'video',
                            acceptMime: 'video/*',
                            exts: 'mp4|flv|avi|rm|rmvb',
                            size: 1024 * 10 * 2,
                            done: function (data) {
                            }
                        },
                        uploadFiles: {
                            url: common.url + '/ts-basics-bottom/fileAttachment/upload?moduleName=version',
                            accept: 'file',
                            acceptMime: 'file/*',
                            size: '20480',
                            done: function (data) {
                            }
                        },
                        calldel: {
                            url: common.url + '/ts-basics-bottom/fileAttachment/upload?moduleName=version',
                            done: function (data) {
                            }
                        },
                        facePath: 'http://knifez.gitee.io/kz.layedit/Content/Layui-KnifeZ/',
                        devmode: true,
                        //, height: '680px'
                    });

                    form.render();
                }
            });

            laydate.render({
                elem: '#versionDate',
                showBottom: true,
                trigger: 'click'
            });

            //初始化附件信息
            function versionFiles(versionId) {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-basics-bottom/version/selectVersionAccessoryByVersionId?versionId=' + versionId,
                    success: function (res) {
                        if (res.success) {
                            if (res.object.length > 0) {
                                $("#versionFileList").show();
                                $.each(res.object, function (index, item) {
                                    if (item.id && item.name) {
                                        var trStr = "<tr id='upload-" + item.id + "'><td>" + item.name + "</td>";
                                        trStr += "<td>" + (item.fileSize / 1024).toFixed(1) + "kb</td><td><span style='color: #5FB878;'>上传成功</span></td>"
                                        trStr += '<td><button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button></td></tr>'
                                        $('#versionFileDataList').append(trStr);
                                        oldFileArray.push(item.id);
                                    }
                                });

                                $("body").off("click", ".demo-delete").on("click", ".demo-delete", function () {
                                    var idStr = $(this).parent().parent('tr').attr("id");
                                    var idArray = idStr.split("-");
                                    oldFileArray.splice($.inArray(idArray[1], oldFileArray), 1);
                                    $(this).parent().parent('tr').remove();
                                })
                            }
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });
            }

            //附件上传
            var versionFileDataList = $('#versionFileDataList'),
                uploadListIns = upload.render({
                    elem: '#versionFileUploadBtn',
                    url: common.url + '/ts-basics-bottom/fileAttachment/upload?moduleName=version',
                    accept: 'file',
                    multiple: true,
                    auto: true,
                    choose: function (obj) {
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        $("#versionFileList").show();
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">',
                                '<td>' + file.name + '</td>',
                                '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>',
                                '<td>等待上传</td>',
                                '<td>',
                                '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>',
                                '</td>',
                                '</tr>'
                            ].join(''));

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                //删除数组中指定元素
                                fileArray = $.grep(fileArray, function (n, i) {
                                    return n['index'] != index;
                                });
                            });

                            versionFileDataList.append(tr);
                        });
                    },
                    done: function (res, index, upload) {
                        if (res.success == true) { //上传成功
                            var tr = versionFileDataList.find('tr#upload-' + index),
                                tds = tr.children();
                            tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                            res.object[0].index = index;
                            fileArray.push(res.object[0]);
                            return delete this.files[index]; //删除文件队列已经上传成功的文件
                        }
                        this.error(index, upload);
                    },
                    error: function (index, upload) {
                        var tr = versionFileDataList.find('tr#upload-' + index),
                            tds = tr.children();
                        tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                    }
                });

            //内容验证
            form.verify({
                content: function (value) {
                    if ('' == layedit.getText(editIndex)) {
                        return "内容不能为空";
                    } else {
                        return layedit.sync(editIndex);
                    }
                }
            });
            // 保存
            form.on('submit(versionSubmitCofirm)', function (data) {
                var d = data.field;
                d.isPush = d.isPush || '0';
                d.content = layedit.getContent(editIndex); //富文本内容
                var uploadedFile = "";
                $.each(fileArray, function (index, obj) {
                    uploadedFile += obj.fileId + ",";
                });
                $.each(oldFileArray, function (index, obj) {
                    uploadedFile += obj + ",";
                });

                d.uploadedFile = uploadedFile.substring(0, uploadedFile.length - 1);

                var url;
                if (d.id) {
                    url = '/ts-basics-bottom/version/update';
                } else {
                    url = '/ts-basics-bottom/version/save';
                }
                if (!d.id) {
                    delete d.id;
                }
                $.loadings();
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    contentType: "application/json;charset=UTF-8",
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        } else {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });
            });
        })
    };
});