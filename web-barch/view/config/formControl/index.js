"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        /*if(opt.elm.html() == ''){
            opt.elm.html(html);
            common.limits($.cookie('jurisdiction'), opt.mid);
        }*/
        layui.use(['form', 'laytpl', 'tree', 'layedit', 'laydate', 'trasen', 'workflow'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                layer = layui.layer,
                workflow = layui.workflow,
                trasen = layui.trasen;

            form.render();
            $('.onlySystemPageMenuBox li').eq(3).addClass('n_active').siblings().removeClass('n_active');
            $('.onlySystemPageMenuBox li').click(
                function () {
                    $('.onlySystemPageMenuBox li').removeClass('menuOn');
                    $(this).addClass('menuOn');
                });
            var id = '',
                pid = '',
                level = '',
                menutype = '',
                menuId = '',
                syscode = '';
            var opr = 'resourcesMenuAdd';
            var addOrEditform = null;

            var grid = $("#index_formControlTable");
            var subGridLoad;

            var data;
            var subgrid_table_id, subgrid_pager_id;

            // 列表
            var formControl_Table = new $.trasenTable("index_formControlTable", {
                url: common.url + '/ts-form/formControl/list',
                pager: '#index_formControlPage',
                mtype: "post",
                /*datatype: "json",
                sortname: "menulevel",
                prmNames: { page: "page", rows: "rows" },*/
                shrinkToFit: true,
                colModel: [ 
                    {label: '控件名称',  sortable: false, name: 'componentName', width: 120, align: 'center'},
                    {label: '控件类型',  sortable: false, name: 'componentType', width: 200, align: 'center'},
                    {label: '请求方式',  sortable: false, name: 'requestMethod', width: 200, align: 'center'},
                    {label: '请求地址',  sortable: false, name: 'url', width: 200, align: 'center'},
                    {label: '取值属性',  sortable: false, name: 'valueField', width: 200, align: 'center'},
                    {label: '状态',  sortable: false, name: 'isEnable', width: 130, align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            return cellvalue == '1' ? '启用' : '禁用';
                        }
                    },
                    {label: '创建人',  sortable: false, name: 'createUserName', width: 150, align: 'center'},
                    {label: '创建时间',  sortable: false, name: 'createDate', width: 150, align: 'center'},
                    {label: '更新时间',  sortable: false, name: 'updateDate', width: 150, align: 'center'},
                    {label: 'id',  sortable: false, name: 'id', width: 130, hidden: true},
                    {label: 'valueField',  sortable: false, name: 'valueField', width: 130, hidden: true},
                    {label: 'seq',  sortable: false, name: 'seq', width: 130, hidden: true}
                ],
                viewrecords: true,
                height: "auto",
                rowNum: 15,
                rownumbers: true,
                forceFit: false,
                gridview: true,
                altRows: true, //隔行变色
                queryFormId: 'formControlForm'
            });

            // 刷新
            function refresh() {
                formControl_Table.refresh();
            }

            //搜索
            form.on('submit(formControlBaseSearch)', function (data) {
                refresh();
            });

            //重置
            $('#formControlBaseReset').funs('click', function () {
                $('#formControlcomponentName').val("");
                $('#formControlisEnable').val("");
                refresh();
            })

            // 新增
            $('#formControlAdd').funs('click', function () {
                $.quoteFun('/config/formControl/add', {
                    trasen: formControl_Table,
                    title: '新增表单控件',
                    ref: refresh
                });
            })

            // 修改
            $('body').off('click', "#formControlUpdate").on('click', '#formControlUpdate', function () {
                id = grid.getGridParam('selrow');
                if (id === null) {
                    trasen.info('请先选择一条数据记录进行修改!');
                } else {
                    var data = formControl_Table.getSelectRowData(); // 选中行数据
                    $.quoteFun('/config/formControl/add', {
                        trasen: formControl_Table,
                        title: '编辑表单控件',
                        data: data,
                        ref: refresh,
                    });
                }
            });

            // 删除
            $('body').off('click', "#formControlDel").on('click', '#formControlDel', function () {
                id = grid.getGridParam('selrow');
                if (id === null) {
                    trasen.info('请先选择一条数据记录进行删除!');
                } else {
                    var data = formControl_Table.getSelectRowData(); // 选中行数据
                    var d = {
                        id: data.id
                    }
                    layer.confirm(
                        '确定删除该数据?', 
                        {
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0
                        }, 
                        function (index) {
                            $.ajax({
                                type: "post",
                                url: common.url + '/ts-form/formControl/deletedById/'+id,
                                dateType: "json",
                                contentType: 'application/json',
                                data: JSON.stringify(d),
                                success: function (res) {
                                    $.closeloadings();
                                    if (res.success) {
                                        layer.closeAll();
                                        layer.msg('操作成功');
                                        refresh();
                                    } else {
                                        layer.closeAll();
                                        layer.msg(res.message);
                                    }
                                },
                                error: function (res) {
                                    res = JSON.parse(res.responseText);
                                    layer.msg(res.message);
                                }
                            });
                    });
                }
            });


        });
    };

})