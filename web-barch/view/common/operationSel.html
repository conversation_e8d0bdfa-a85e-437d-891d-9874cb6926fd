<style>
  #chooseOperationForm .select-box {
      padding: 0 8px;
      margin: 8px 0;
  }
  #chooseOperationForm .select-box .select-left-box,
  #chooseOperationForm .select-box .select-right-box {
      height: 420px;
      position: relative;
      padding: 8px;
      border: 1px solid #eee;
      border-radius: 8px;
      box-sizing: border-box;
  }
  #chooseOperationForm .select-box .select-left-box {
      flex: 1;
      display: flex;
      flex-direction: column;
  }
  #chooseOperationForm .select-box .select-right-box {
      margin-left: 8px;
      width: 220px;
  }
  #chooseOperationForm .userDeptTreeBox {
      overflow: auto;
      width: 100%;
      flex-grow: 1;
  }
  #chooseOperationForm .oa-nav-search {
      border: 0;
  }
  #chooseOperationForm .userDeptTreeBox .ztree {
      height: 95%;
      top: 0;
  }
  #chooseOperationForm .layui-tree-branch {
      display: none;
  }
  #chooseOperationForm .layui-form-checkbox {
      display: none;
  }
  #chooseOperationForm .value-box {
      padding: 0 8px;
      margin: 8px 0;
  }
  #chooseOperationForm .selectUserNameBox {
      width: 100%;
      box-sizing: border-box;
      height: 380px;
      border: 1px solid #eee;
      padding: 8px;
      box-sizing: border-box;
      overflow: auto;
  }
  #chooseOperationForm .selectUserNameBox .user-item {
      display: inline-block;
      position: relative;
      padding: 4px 10px;
      margin-right: 8px;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
      border-radius: 14px;
      cursor: pointer;
      background: #edefff;
  }
  #chooseOperationForm .selectUserNameBox .user-item:hover {
      background-color: #5260FF;
      color: #fff;
  }
  #chooseOperationForm .selectUserNameBox .user-item .delete-btn{
      position: absolute;
      right: -6px;
      color: red;
      background-color: #fff;
      border-radius: 100%;
      display: none;
      top: -2px;
  }
  #chooseOperationForm .selectUserNameBox .user-item:hover .delete-btn {
      display: inline-block;
  }

  #chooseOperationForm .bottom-box{
      width: 100%;
      box-sizing: border-box;
      padding: 8px 15px;
      text-align: right;
      border-top: 1px solid #eee;
      background-color: #fff;
  }

</style>
<div id="chooseOperationForm" class="layui-form">
  <div class="flex select-box">
      <div class="select-left-box">
          <div class="oa-nav-search">
              <form method="" lay-filter="component-form-element" id="resourcesForm" class="layui-form areaButtonBoxL">
                  <div class="layui-inline fl">
                      <input type="text" placeholder="请输入编码/名称" name="itemCode" search-input="common-user" autocomplete="off" class="layui-input edi-layui-input" />
                  </div>
                  <div class="layui-inline fl mgl10">
                    <select id="reportedDataEmployeeStatus" name="authLvHosp" lay-filter="reportedDataEmployeeStatusFilter" lay-search></select>
                  </div>
                  <div class="layui-inline fl mgl10">
                    <input type="text" name="quaAuthTypeSearch" autocomplete="off" id="quaAuthTypeSearch" class="layui-input fl" placeholder="请选择手术类型" search-input="search" />
                    <input type="text" name="quaAuthType" class="none" />
                  </div>
                  <div class="pubBtnBox fl mgl10">
                      <button type="button" class="layui-btn" lay-submit="" lay-filter="systemManageOrgDepUserScreenSearch" search-btn="common-user">搜索</button>
                      <button type="button" class="layui-btn oa-btn-reset" id="common_userSel_screenReset"><i class="layui-icon layui-icon-refresh"></i></button>
                  </div>
              </form>
          </div>
          <div class="trasen-con-box">
              <div class="table-box">
                  <!-- 表单 -->
                  <table id="operationTable"></table>
                  <!-- 分页 -->
                  <div id="operationPager"></div>
              </div>
          </div>
      </div>
      <div class="select-right-box">
        <div class="value-box">
          <div class="selectUserNameBox" id="select_UserNameBox"></div>
        </div>
      </div>
  </div>
  <div class="layer-btn bottom-box">
      <div style="float: left;">
          <div style="float: left;">
              已选手术:
              <span id="allUserNum">0</span>
          </div>
      </div>
      <button type="button" class="layui-btn layui-btn-normal" lay-submit="" lay-filter="chooseUserSubmitCofirm">保存</button>
      <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="chooseUserSubmitEmpty">清空</button>
      <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="closeChooseUser">关闭</button>
  </div>
</div>
