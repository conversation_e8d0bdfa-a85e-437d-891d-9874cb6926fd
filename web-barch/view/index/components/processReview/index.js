"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    opt.$dom.html(html);
    common.overlayScrollbarsSet(" .scrollbar-box");
    let reviewList = [];

    let definitionId = opt.config.elementChannel;
    let elementName = opt.config.elementName;

    $("#processReview .document-nav-item").text(elementName || "流程查阅");
    $.ajax({
      url: `/ts-workflow/workflow/wfInst/info/definitionId/${definitionId}`,
      async: false,
      success: function (res) {
        reviewList = [];
        $("#processReview .document-ul-box").html("");
        if (res.statusCode === 200 && res.success && res.object.length > 0) {
          $("#processReview .document-ul-box")
            .closest(".content-bg-con")
            .addClass("noBg");

          let lis = ``;
          res.object.forEach((item) => {
            reviewList.push(item);

            let nowYear = new Date().format("yyyy");
            let infoYear = new Date(item.createDate).format("yyyy");
            let infoDate = "";
            if (nowYear == infoYear) {
              infoDate = new Date(item.createDate).format("MM-dd");
            } else {
              infoDate = new Date(item.createDate).format("yyyy-MM-dd");
            }
            let workflowTitle = item.workflowTitle;
            if ("csjkyy" == common.globalSetting.orgCode) {
              workflowTitle = workflowTitle.split("-")[0];
            }
            lis +=
              `
                  <li id=${item.wfInstanceId}>
                    <p class="li-info-titlt one-line">` +
              workflowTitle +
              `</p>
                    <p class="date padding-left-8">${infoDate}</p>
                  </li>
            `;
          });

          $("#indexIFrame #processReview .document-ul-box").html(lis);
        } else {
          $("#processReview .document-ul-box")
            .closest(".content-bg-con")
            .removeClass("noBg");
        }
      },
    });

    $("body")
      .off("click", "#indexIFrame #processReview li")
      .on("click", "#indexIFrame #processReview li", function (e) {
        var id = $(this).attr("id");
        const find = reviewList.find((item) => item.wfInstanceId === id);
        var son = common.processDeal.dealSee(find, 2, 1);
        common.openedWindow.push(son);
        e.stopPropagation();
        return false;
      });
  };
});
