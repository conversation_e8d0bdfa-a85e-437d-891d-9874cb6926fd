'use strict';
define(function (require, exports, module) {
    var util = require('util');
    exports.init = function (opt, html) {
        opt.$dom.html(html);
        var uuid = util.guid();
        if (opt.config.elementColumn == 2 || opt.config.elementColumn == 1) {
            opt.$dom.find('#index-info-temp-many').removeClass('none').attr('identification', uuid);
            opt.$dom.find('#index-info-temp-list').remove();
        } else {
            opt.$dom.find('#index-info-temp-list').removeClass('none').attr('identification', uuid);
            opt.$dom.find('#index-info-temp-many').remove();
        }
        common.overlayScrollbarsSet(' .scrollbar-box');

        // 多栏目 点击跳转至详情页面 默认高亮
        let jumpInfoNavMoreTimer = null;
        let jumpInfoNavMoreTimerSwitch = false;
        $('#index-info-temp-many .info-nav-more').unbind('click').click(function () {
            const columnName = $(this).closest('#index-info-temp-many').find('.info-nav-item.active').text();
            location.href = '#/information/messageRead';

            jumpInfoNavMoreTimer = setInterval(() => {
                $('#messageRead .msg-channel-list .infoChannelItem').removeClass('infoChannelItemPress')
                $('#messageRead .msg-channel-list .infoChannelItem').each((index, item) => {
                    if ($(item).text() === columnName) {
                        $(item).trigger('click');
                        // (初次未加载信息查阅页面 click无效 猜测click方法未加载完)  延时修改阀门 
                        setTimeout(() => {
                            jumpInfoNavMoreTimerSwitch = true;
                        }, 500) 
                    }
                })

                if (jumpInfoNavMoreTimerSwitch) {
                    clearInterval(jumpInfoNavMoreTimer);
                }
            }, 500);
        });

        // 单栏目 点击跳转至详情页面 默认高亮
        let jumpMoreTimer = null;
        let jumpMoreTimerSwtich = false;
        $('#index-info-temp-list .more').unbind('click').click(function () {
            const columnName = $(this).closest('#index-info-temp-list').find('.column-name').text();
            location.href = '#/information/messageRead';

            jumpMoreTimer = setInterval(() => {
                $('#messageRead .msg-channel-list .infoChannelItem').removeClass('infoChannelItemPress')
                $('#messageRead .msg-channel-list .infoChannelItem').each((index, item) => {
                    if ($(item).text() === columnName) {
                        $(item).trigger('click');
                        // (初次未加载信息查阅页面 click无效 猜测click方法未加载完)  延时修改阀门 
                        setTimeout(() => {
                            jumpMoreTimerSwtich = true;
                        }, 500) 
                    }
                })

                if (jumpMoreTimerSwtich) {
                    clearInterval(jumpMoreTimer);
                }
            }, 500);
        });

        var navIndex = 0;
        var timer;
        if (opt.config.elementColumn == 2 || opt.config.elementColumn == 1) {
            getInfoNav();
        } else {
            getInfoList();
        }
        //单列
        function getInfoList() {
            $.ajax({
                url: '/ts-information/informationChannel/list',
                data: {
                    pageSize: 100,
                    pageNo: 1,
                    status: 1,
                },
                success: function (res) {
                    var data = res.rows || [];
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].id == opt.config.elementChannel) {
                            opt.$dom.find('.title_content i').next().text(data[i].channelName);

                            if (data[i].noread > 0) {
                                opt.$dom.find('.title_content').append('<span class="red-icon"></span>')
                            }
                            break;
                        }
                    }
                },
            });
            $.ajax({
                url: '/ts-information/information/list',
                data: {
                    informationStatus: 1,
                    index: 5,
                    pageSize: 10,
                    pageNo: 1,
                    sidx: 'INFO.SHOW_SIGN DESC,INFO.CREATE_DATE',
                    sord: 'desc',
                    channelId: opt.config.elementChannel,
                },
                success: function (res) {
                    $('#index-info-temp-list[identification="' + uuid + '"] .info-content').html('');
                    if (res.rows && res.rows.length) {
                        $('#index-info-temp-list[identification="' + uuid + '"] .info-content').addClass('noBg');
                        new common.simpleEvent({
                            el: '#index-info-temp-list[identification="' + uuid + '"] .info-content',
                            list: res.rows,
                            classes: 'box',
                            event: 'click',
                            print: function (item) {
                                var topHtml = '';
                                var importantHtml = '';
                                var isRead = '';
                                if (item.bid == null) {
                                    isRead = 'read';
                                    topHtml += "<span class='new'>new</span>";
                                }
                                if (1 == item.titleColor) {
                                    importantHtml += "<span class='important'>重</span>";
                                }
                                if (1 == item.showSign) {
                                    topHtml += '<span class="top">顶</span>';
                                }
                                if (1 == item.isMarrow) {
                                    importantHtml += '<span class="refine">精</span>';
                                }
                                var contentText = '';
                                if (1 == item.titleColor) {
                                    contentText += '<span class="one-line" style="color:#D50D0D; flex: 1;">' + item.informationTitle + '</span>';
                                } else {
                                    contentText += '<span class="one-line" style="flex: 1;">' + item.informationTitle + '</span>';
                                }
                                /*var textMain = '';
                                if (item.informationContent) {
                                    if (opt.config.elementShow) {
                                        textMain = '<div><p class="first-title one-line text-main">' + (item.informationContent ? $(item.informationContent).text() : '') + '</p></div>';
                                    } else {
                                        textMain = '<div><p class="first-title  two-line text-main">' + (item.informationContent ? $(item.informationContent).text() : '') + '</p></div>';
                                    }
                                }*/
                                if (opt.config.elementShow == 1) {
                                    var nowYear = new Date().format("yyyy");
                                    var infoYear = new Date(item.releaseDate).format("yyyy");
                                    var infoDate = "";
                                    if (nowYear == infoYear) {
                                        infoDate = new Date(item.releaseDate).format("MM-dd");
                                    } else {
                                        infoDate = new Date(item.releaseDate).format("yyyy-MM-dd");
                                    }
                                    return (
                                        '<div class="info-content-item ' +
                                        isRead +
                                        '">' +
                                        '<div class="flex-box first-title">' +
                                        '<p class="flex-align-center one-line flex-box-1">' +
                                        topHtml +
                                        importantHtml +
                                        contentText +
                                        '</p> <p class="infoDate">' + infoDate + '</p> </div>'



                                        /*+
                                        '<div class="flex-box second-title">' +
                                        '<p class="one-line flex-box-1">' +
                                        '<span class="pd-lf-10 "><i class="fa fa-eye" aria-hidden="true"></i> ' +
                                        item.informationKits +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createDeptName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createUserName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        new Date(item.createDate).format('yyyy-MM-dd hh:mm') +
                                        '</span>' +
                                        '</p>' +
                                        '</div>' +
                                        '</div>'*/
                                    );
                                } else {
                                    var photo = item.infoPhotoUrl ? '/ts-document/attachment/' + item.infoPhotoUrl : '/static/img/other/def_img.png';
                                    // return (
                                    //     '<div class="info-content-item ' +
                                    //     isRead +
                                    //     '"><div class="info-img fl" style="height:30px;width:30px"><img src="' +
                                    //     photo +
                                    //     '" alt="信息图片"></div><div class="overflow-hide">' +
                                    //     '<div class="flex-box first-title">' +
                                    //     '<p class="one-line flex-box-1">' +
                                    //     topHtml +
                                    //     importantHtml +
                                    //     contentText +
                                    //     '</p>  </div>' +
                                    //     '<div class="flex-box second-title">' +
                                    //     '<p class="one-line flex-box-1">' +
                                    //     '<span class="pd-lf-10 "><i class="fa fa-eye" aria-hidden="true"></i> ' +
                                    //     item.informationKits +
                                    //     '</span>' +
                                    //     '<span class="pd-lf-10">' +
                                    //     item.createDeptName +
                                    //     '</span>' +
                                    //     '<span class="pd-lf-10">' +
                                    //     item.createUserName +
                                    //     '</span>' +
                                    //     '<span class="pd-lf-10">' +
                                    //     new Date(item.createDate).format('yyyy-MM-dd hh:mm') +
                                    //     '</span>' +
                                    //     '</p>' +
                                    //     '</div></div>' +
                                    //     '</div>'
                                    // );
                                    const newYear = new Date().getFullYear()
                                    const resYear = item.releaseDate.slice(0, 4)
                                    const showTime = newYear == resYear ? new Date(item.releaseDate).format('MM-dd') : new Date(item.releaseDate).format('yyyy-MM-dd')
                                    return (
                                        '<div class="info-content-item ' +
                                        isRead +
                                        '"><div class="info-img fl" style="height:30px;width:30px"><img src="' +
                                        photo +
                                        '" alt="信息图片"></div><div class="overflow-hide">' +
                                        '<div class="flex-box first-title">' +
                                        '<p class="flex-align-center one-line flex-box-1">' +
                                        topHtml +
                                        importantHtml +
                                        contentText +
                                        '</p>' +
                                        '<span class="date">' + showTime + "</span>" +
                                        '</div>'
                                    );
                                }
                            },
                            func: function (item) {
                                openInfoDetails(item.id);
                                clearTimeout(timer);
                                timer = setTimeout(function () {
                                    getInfoList();
                                }, 500);
                            },
                        });
                    } else {
                        $('#index-info-temp-list[identification="' + uuid + '"] .info-content').removeClass('noBg');
                    }
                },
            });
        }
        //多列
        function getInfoNav() {
            $.ajax({
                url: '/ts-information/information/selectNoreadByChannel',
                type: 'get',
                success: function (res) {
                    let navBar = $('#indexIFrame [identification="' + uuid + '"]  .info-nav');
                    navBar.html('');

                    if (res.success && res.object && res.object.length) {
                        var list = [];
                        if (!opt.config.elementChannel) {
                            list = res.object;
                        } else {
                            var arr = opt.config.elementChannel.split(',') || [];
                            for (var i = 0; i < arr.length; i++) {
                                for (var j = 0; j < res.object.length; j++) {
                                    if (arr[i] == res.object[j].id) {
                                        list.push(res.object[j]);
                                    }
                                }
                            }
                        }
                        list.forEach((item, index) => {
                            item.navIndex = index;
                        });
                        let timer = null;
                        window.addEventListener('resize', function() {
                            timer && this.clearTimeout(timer);
                            timer = this.setTimeout(() => {
                                const navWidth = navBar[0].clientWidth;
                                if (navWidth === 0) {
                                    return false;
                                }
                                navBar.children().remove();
                                $(
                                    '#indexIFrame [identification="' + uuid + '"] .dropdown-box'
                                ).remove();
                                new common.simpleEvent({
                                    el: '#indexIFrame [identification="' + uuid + '"]  .info-nav',
                                    list: list,
                                    event: 'click',
                                    print: function(item, index) {
                                        var cl = '';
                                        let redIcon = '';
                                        if (item.navIndex == navIndex) {
                                            cl = 'active';
                                            getInfoContent(item);
                                        }
                                        if (item.noread > 0) {
                                            redIcon = '<span class="red-icon"></span>'
                                        }
                                        /*var span = item.noread == 0 ? '' : '<span class="data-num">' + item.noread + '</span>';*/
                                        /* return '<div class="nav ' + cl + '"><i class="fa fa-angle-right fr" aria-hidden="true"></i> <i class="oaicon oa-icon-icon_lanmu" style="font-size: 18px;display: inline-block; vertical-align: bottom;" aria-hidden="true"></i>' + item.channelName + ' ' + span + '</div>';*/
                                        return (
                                        '<div class="info-nav-item ' +
                                        cl +
                                        '" data-refTable="0">' +
                                        redIcon
                                        +
                                        item.channelName +
                                        '</div>'
                                        );
                                    },
                                    func: function(item, index) {
                                        navIndex = item.navIndex;
                                        $(
                                        '#indexIFrame [identification="' + uuid + '"] .info-nav-item'
                                        ).removeClass('active');
                                        $(
                                        '#indexIFrame [identification="' +
                                            uuid +
                                            '"] .info-nav .info-nav-item'
                                        )
                                        .eq(index)
                                        .addClass('active');
                                        getInfoContent(item);
                                    }
                                });
                    
                                let navBarItem = $('.info-nav-item', navBar),
                                    showWidth = 0,
                                    maxIndex = 0,
                                    html = `<div class="info-nav-item" name="dropdownBox" data-refTable="0" style="text-indent: 0;"> <i class="down-select-icon fa fa-angle-down"></i> </div>`;
                                for (let index = 0; index < navBarItem.length; index++) {
                                    let dom = navBarItem[index],
                                        computWidth = showWidth + dom.clientWidth;
                        
                                    index < navBarItem.length - 1 && (computWidth += 50);
                                    if (computWidth > navWidth) {
                                        maxIndex = index - 1;
                                        break;
                                    } else {
                                        showWidth += dom.clientWidth;
                                        maxIndex = index;
                                    }
                                }
                                if (maxIndex >= navBarItem.length - 1) {
                                    return;
                                }
                    
                                navBarItem.each((index, dom) => {
                                    if (index > maxIndex) {
                                        dom.remove();
                                    }
                                });
                    
                                $('#indexIFrame [identification="' + uuid + '"]  .info-nav').append(
                                    html
                                );
                    
                                let dropdownList = list.slice(maxIndex + 1),
                                    informBox = $('#indexIFrame [identification="' + uuid + '"]'),
                                    dropdownBox = $('[name="dropdownBox"]', navBar);
                    
                                /**@desc 计算所需 */
                                let contentRight =
                                    informBox[0].clientWidth -
                                    dropdownBox[0].offsetLeft -
                                    dropdownBox[0].clientWidth,
                                    maxHeight = informBox[0].clientHeight - navBar[0].clientHeight - 50,
                                    dropdownContent = `
                                                            <div
                                                            class="dropdown-box"
                                                            style="right: ${contentRight}px;"
                                                            >
                                                            <div
                                                                class="angle-icon"
                                                                style="right: ${dropdownBox[0]
                                                                    .clientWidth /
                                                                    2 +
                                                                    3}px;"
                                                            ></div>
                                                            <div style="padding: 8px; background: #FFF; border-radius: 4px; margin-top: 8px;">
                                                                <div class="dropdown-content" style="max-height: ${maxHeight}px;"></div>
                                                            </div>
                                                            </div>`;
                    
                                informBox.append(dropdownContent);
                    
                                new common.simpleEvent({
                                    el:
                                        '#indexIFrame [identification="' + uuid + '"]  .dropdown-content',
                                    list: dropdownList,
                                    event: 'click',
                                    print: function(item, index) {
                                        var cl = '';
                                        let redIcon = '';
                                        if (item.navIndex == navIndex) {
                                            cl = 'active';
                                            getInfoContent(item);
                                        }
                                        if (item.noread > 0) {
                                            redIcon = '<span class="red-icon"></span>'
                                        }
                                        return (
                                        '<div class="info-nav-item dropdown-nav-item' +
                                        cl +
                                        '" data-refTable="0">' +
                                        redIcon
                                        +
                                        item.channelName +
                                        '</div>'
                                        );
                                    },
                                    func: function(item, index) {
                                        navIndex = item.navIndex;
                                        $(
                                        '#indexIFrame [identification="' + uuid + '"] .info-nav-item'
                                        ).removeClass('active');
                                        $(
                                        '#indexIFrame [identification="' +
                                            uuid +
                                            '"] .dropdown-nav-item'
                                        )
                                        .eq(index)
                                        .addClass('active');
                                        getInfoContent(item);
                                    }
                                });
                    
                                dropdownBox.off('click').on('click', function(e) {
                                    $(
                                        '#indexIFrame [identification="' + uuid + '"] .dropdown-box'
                                    ).toggleClass('active');
                                });
                            }, 200);
                        });
                        window.dispatchEvent(new CustomEvent('resize'));
                        window.addEventListener('click', function(e) {
                            let dropdownBox = $(
                                    '#indexIFrame [identification="' + uuid + '"] .dropdown-box'
                                )[0],
                                dropDownIcon = $(
                                    '#indexIFrame [identification="' + uuid + '"] [name="dropdownBox"]'
                                )[0];
                    
                            if (!dropdownBox) {
                                return;
                            }
                    
                            if (
                                !dropdownBox.contains(e.target) &&
                                !dropDownIcon.contains(e.target)
                            ) {
                                dropdownBox.classList.remove('active');
                            }
                        });
                    }
                },
            });
        }

        function getInfoContent(item) {
            var nav = item;
            $.ajax({
                type: 'get',
                url: common.url + '/ts-information/information/list',
                data: {
                    informationStatus: 1,
                    index: 5,
                    pageSize: 15,
                    pageNo: 1,
                    // sidx: 'RELEASE_DATE', 2024/07/11 石门医院信息管理展示顺序不对修改
                    sidx: 'INFO.SHOW_SIGN DESC,INFO.CREATE_DATE',
                    sord: 'desc',
                    channelId: item.id
                },
                success: function (res) {
                    $('#indexIFrame [identification="' + uuid + '"] .info-tab-content').html('');
                    if (res.rows && res.rows.length) {
                        $('#indexIFrame [identification="' + uuid + '"]  .info-tab-content')
                            .closest('.content-bg-con')
                            .removeClass('two-col-home-page');
                        // 图文模式
                        if (opt.config.elementShow == 2) {
                            var contentText = '';
                            $.each(res.rows, function (index, item) {
                                if (index > 6) {
                                    return false
                                }
                                var topHtml = '';
                                var importantHtml = '';
                                var isRead = '';
                                if (item.bid == null) {
                                    isRead = 'read';
                                    topHtml += "<span class='new'>new</span>";
                                }
                                if (1 == item.titleColor) {
                                    importantHtml += "<span  class='important'>重</span>";
                                }
                                if (1 == item.showSign) {
                                    topHtml += '<span class="top">顶</span>';
                                }
                                if (1 == item.isMarrow) {
                                    importantHtml += '<span class="refine">精</span>';
                                }
                                if (0 == index) {
                                    var photo = item.infoPhotoUrl ? '/ts-document/attachment/' + item.infoPhotoUrl : '/static/img/other/def_img.png';
                                    var textMain = '';
                                    if (null != item.informationContent) {
                                        textMain = item.informationContent.replace(/<\/?.+?>/g, "");
                                    } else {
                                        textMain = '无内容';
                                    }
                                    contentText += '<div class="info-content-left" attr-id=' + item.id + '>';
                                    contentText += '<img src="' + photo + '" alt="信息图片">';
                                    contentText += '<div class="left-info-content-box">';
                                    if(1 == item.titleColor) {
                                        // <p class="firstTitle one-line"
                                        contentText += '<p class="firstTitle" style="color:#D50D0D;">' + topHtml + importantHtml + item.informationTitle + '</p>';
                                    } else {
                                        contentText += '<p class="firstTitle">' + topHtml + importantHtml + item.informationTitle + '</p>';
                                    }
                                    // contentText += '<p class="firstContent">' + textMain.replace(/ /g, "").replace(/&nbsp;/g, '') + '</p>';
                                    contentText += '<div class="date">' + new Date(item.releaseDate).format('yyyy-MM-dd') + '</div></div></div>';
                                } else {
                                    if (index == 1) {
                                        contentText += '<div class="info-content-right"><ul class="content-right-list">';;
                                    }
                                    let oneLineSpan = '';
                                    if(1 == item.titleColor) {
                                        oneLineSpan = `<span style="flex: 1; color:#D50D0D;" class="one-line">${item.informationTitle}</span>`
                                    } else {
                                        oneLineSpan = `<span style="flex: 1;" class="one-line">${item.informationTitle}</span>`
                                    }
                                    contentText += '<li><p class="info-title one-line" attr-id=' + item.id + '>' + topHtml + importantHtml + oneLineSpan
                                    const newYear = new Date().getFullYear()
                                    const resYear = item.releaseDate.slice(0, 4)
                                    const showTime = newYear == resYear ? new Date(item.releaseDate).format('MM-dd') : new Date(item.releaseDate).format('yyyy-MM-dd')
                                    contentText += '</p><p class="date padding-left-8">' + showTime + '</p>';
                                    // if (!item.bid) {
                                    // } else {
                                    //     contentText += '</li>';
                                    // }
                                    // contentText += '<span class="yuan"></span></li>';
                                    contentText += '</li>';
                                    if (res.rows.length == index) {
                                        contentText += '</ul></div>';
                                    }
                                }
                            })
                            // 数据只有一条  左侧只展示图片信息一条 右侧显示无数据图片
                            if(res.rows.length === 1) {
                                contentText += `<div class="info-content-right content-bg-con home-page"></div>`
                            }
                            $('#indexIFrame [identification="' + uuid + '"] .info-tab-content').html(contentText);
                        } else {
                        // 列表模式
                        var contentText = '';
                        $.each(res.rows, function (index, item) {
                            if (index > 5) {
                                return false
                            }
                            var topHtml = '';
                            var importantHtml = '';
                            var isRead = '';
                            if (item.bid == null) {
                                isRead = 'read';
                                topHtml += "<span class='new'>new</span>";
                            }
                            if (1 == item.titleColor) {
                                importantHtml += "<span  class='important'>重</span>";
                            }
                            if (1 == item.showSign) {
                                topHtml += '<span class="top">顶</span>';
                            }
                            if (1 == item.isMarrow) {
                                importantHtml += '<span class="refine">精</span>';
                            }
                            contentText += '<div class="info-content-right"><ul class="content-right-list">';;
                            let oneLineSpan = '';
                            if(1 == item.titleColor) {
                                oneLineSpan = `<span style="flex: 1; color:#D50D0D;" class="one-line">${item.informationTitle}</span>`
                            } else {
                                oneLineSpan = `<span style="flex: 1;" class="one-line">${item.informationTitle}</span>`
                            }
                            contentText += '<li><p class="info-title one-line" attr-id=' + item.id + '>' + topHtml + importantHtml + oneLineSpan
                            const newYear = new Date().getFullYear()
                            const resYear = item.releaseDate.slice(0, 4)
                            const showTime = newYear == resYear ? new Date(item.releaseDate).format('MM-dd') : new Date(item.releaseDate).format('yyyy-MM-dd')
                            contentText += '</p><p class="date padding-left-8">' + showTime + '</p>';
                            contentText += '</li>';
                            if (res.rows.length == index) {
                                contentText += '</ul></div>';
                            }
                        })
                        $('#indexIFrame [identification="' + uuid + '"] .info-tab-content').html(contentText);
                        }
                        /*new common.simpleEvent({
                            el: '#indexIFrame [identification="' + uuid + '"]  .info-tab-content',
                            list: res.rows,
                            classes: 'box',
                            event: 'click',
                            print: function (item, index) {
                                var topHtml = '';
                                var importantHtml = '';
                                var isRead = '';
                                if (1 == item.titleColor) {
                                    importantHtml += "<span  class='important'>重</span>";
                                }
                                if (1 == item.showSign) {
                                    topHtml += '<span class="top">顶</span>';
                                }
                                if (1 == item.isMarrow) {
                                    importantHtml += '<span class="refine">精</span>';
                                }

                                if (item.bid) {
                                    isRead = 'read';
                                }
                                
                                var contentText = '';
                                if (1 == item.titleColor) {
                                    contentText += "<span style='color:#D50D0D;'>" + item.informationTitle + '</span>';
                                } else {
                                    contentText += '<span  >' + item.informationTitle + '</span>';
                                }
                                var textMain = '';
                                if (item.informationContent) {
                                    if (opt.config.elementShow == 1) {
                                        textMain = '<div><p class="first-title one-line text-main">' + (item.informationContent ? $(item.informationContent).text() : '') + '</p></div>';
                                    } else {
                                        textMain = '<div><p class="first-title  two-line text-main">' + (item.informationContent ? $(item.informationContent).text() : '') + '</p></div>';
                                    }
                                }
                               if (opt.config.elementShow == 1) {
                                    return (
                                        '<div class="info-content-item ' +
                                        isRead +
                                        '">' +
                                        '<div class="flex-box first-title">' +
                                        '<p class="one-line flex-box-1">' +
                                        topHtml +
                                        importantHtml +
                                        contentText +
                                        '</p>  </div>' +
                                        '<div class="flex-box second-title">' +
                                        '<p class="one-line flex-box-1">' +
                                        '<span class="pd-lf-10 "><i class="fa fa-eye" aria-hidden="true"></i> ' +
                                        item.informationKits +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createDeptName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createUserName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        new Date(item.createDate).format('yyyy-MM-dd hh:mm') +
                                        '</span>' +
                                        '</p>' +
                                        '</div>' +
                                        textMain +
                                        '</div>'
                                    );
                                } else {
                                    var photo = item.infoPhotoUrl ? '/ts-document/attachment/' + item.infoPhotoUrl : '/static/img/other/def_img.png';
                                    return (
                                        '<div class="info-content-item ' +
                                        isRead +
                                        '"><div class="info-img"><img src="' +
                                        photo +
                                        '" alt="信息图片"></div><div class="overflow-hide" style="padding-right:16px">' +
                                        '<div class="flex-box first-title">' +
                                        '<p class="one-line flex-box-1">' +
                                        topHtml +
                                        importantHtml +
                                        contentText +
                                        '</p>  </div>' +
                                        '<div class="flex-box second-title">' +
                                        '<p class="one-line flex-box-1">' +
                                        '<span class="pd-lf-10 "><i class="fa fa-eye" aria-hidden="true"></i> ' +
                                        item.informationKits +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createDeptName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        item.createUserName +
                                        '</span>' +
                                        '<span class="pd-lf-10">' +
                                        new Date(item.createDate).format('yyyy-MM-dd hh:mm') +
                                        '</span>' +
                                        '</p>' +
                                        '</div>' +
                                        textMain +
                                        '</div>' +
                                        '</div>'
                                    );
                                }
                            },
                            func: function (item, index) {
                                openInfoDetails(item.id);
                                clearTimeout(timer);
                                timer = setTimeout(function () {
                                    getInfoNav();
                                }, 500);
                            },
                        });*/
                    } else {
                        $('#indexIFrame [identification="' + uuid + '"]  .info-tab-content')
                            .closest('.content-bg-con')
                            .addClass('two-col-home-page');
                    }
                },
                error: function (res) {
                    // res = JSON.parse(res.responseText);
                    // layer.msg(res.message);
                },
            });
        }

        $('body').off('click', '#index-info-temp-many .info-content-left').on('click', '#index-info-temp-many .info-content-left', function () {
            $(this).find('.new').eq(0).remove();
            openInfoDetails($(this).attr("attr-id"));
        });

        $('body').off('click', '#index-info-temp-many .info-title').on('click', '#index-info-temp-many .info-title', function () {
            $(this).find('.new').eq(0).remove();
            openInfoDetails($(this).attr("attr-id"));
        });

        function openInfoDetails(id) {
            var rowData = {};
            rowData.reader = 'yes';
            rowData.id = id;
            $.quoteFun('/information/messageRelease/msgDetail', {
                // trasen: trasenTable,
                data: rowData,
                title: '信息详情',
                ref: function () {},
            });
        }
    };
});