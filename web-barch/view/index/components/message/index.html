<style>
    #indexIFrame .info-nav-box {
        float: left;
        width: 200px;
        height: 100%;
        background-color: #f6f7ff;
        background-image: url(/static/img/other/corner_bg.png);
        background-repeat: no-repeat;
        background-position: left bottom;
        position: relative;
        /* background-color: #f6f7ff; */
    }

    #indexIFrame .info-nav {
        width: 100%;
        /* overflow: hidden; */
        /* height: 100%; */
    }

    #indexIFrame .info-nav .nav {
        height: 50px;
        line-height: 50px;
        color: #333;
        padding-left: 30px;
        cursor: pointer;
        overflow: hidden;
    }

    #indexIFrame .info-nav .nav .fr {
        line-height: 50px;
    }

    #indexIFrame .info-nav .nav i {
        padding-right: 10px;
        color: #999;
    }

    #indexIFrame .info-nav .nav:hover {
        background-color: #fff;
        color: #5260ff;
    }

    #indexIFrame .info-nav .nav:hover i {
        color: #5260ff;
    }

    #indexIFrame .info-nav .nav.active {
        background-color: #fff;
        color: #5260ff;
    }

    #indexIFrame .info-nav .nav.active i {
        color: #5260ff;
    }

    /* #indexIFrame .info-nav + div {
    overflow: hidden;
    height: 100%;
} */

    #indexIFrame .info-content {
        height: 100%;
        background-color: #fff;
        box-sizing: border-box;
        border-radius: 0 0 4px 4px;
    }

    #indexIFrame .info-content .box:hover {
        background-color: #edefff;
    }

    #indexIFrame .info-content .box:hover .first-title p {
        color: #5260ff;
    }

    #indexIFrame .info-content .info-content-item {
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
        padding: 8px;
        cursor: pointer;
    }
    #indexIFrame .info-content .info-content-item:hover .one-line  {
        white-space: unset;
    }
    #indexIFrame .info-content .info-content-item::after {
        content: '';
        display: block;
        width: 0;
        height: 0;
        clear: both;
    }

    #indexIFrame .info-content .info-content-item .first-title {
        color: #333333;
        line-height: unset;
    }

    #indexIFrame .info-content .info-content-item .first-title .flex-align-center{
        display: flex;
        align-items: baseline;
    }

    #indexIFrame .info-content .info-content-item .first-title .date {
        font-size: 11px;
        color: rgba(51, 51, 51, 0.3);
        line-height: 16px;
        line-height: 35px;
    }

    #indexIFrame .info-content .info-content-item.read .first-title {
        color: #333333;
    }

    #indexIFrame .info-content .top {
        display: inline-block;
        line-height: 18px;
        width: 18px;
        text-align: center;
        border: 1px solid #f59a23;
        color: #f59a23;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-content .refine {
        display: inline-block;
        line-height: 18px;
        text-align: center;
        width: 18px;
        border: 1px solid #1677ff;
        color: #1677ff;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-content .important {
        display: inline-block;
        line-height: 18px;
        text-align: center;
        width: 18px;
        border: 1px solid #f93a4a;
        color: #f93a4a;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-img {
        float: right;
        width: 144px;
        height: 92px;
    }

    #indexIFrame .info-img.fl {
        float: left;
        margin-right: 8px;
    }

    #indexIFrame .info-img img {
        display: block;
        height: 100%;
        width: 100%;
    }

    #indexIFrame #index-info-temp-list .scroll-box-height {
        height: calc(100% - 42px);
    }

    #indexIFrame #index-info-temp-list .title {
        height: 40px;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        padding: 0 8px;
        box-sizing: border-box;
        background-image: linear-gradient(180deg, #ffff 0%, #ffff 100%);
        border-bottom: 1px solid #E4E4E4;
    }

    #indexIFrame #index-info-temp-list .sign {
        width: 4px;
        height: 18px;
        background: #5260FF;
        border-radius: 2px;
        margin-top: 6px;
    }

    #indexIFrame #index-info-temp-list .infoDate {
        color: #999;
        font-size: 11px;
    }

    #indexIFrame #index-info-temp-list .title .title_content i {
        color: #5b98d9;
        font-size: 18px;
        display: inline-block;
        font-weight: 400;
        vertical-align: top;
    }

    #indexIFrame #index-info-temp-list .title .title_content .red-icon {
        width: 8px;
        height: 8px;
        background: #E24242;
        border-radius: 50%;
        display: inline-block;
        margin-left: 2px;
    }

    #indexIFrame #index-info-temp-many {
        /* height: 100%; */
    }

    #indexIFrame #index-info-temp-many .info-nav-header {
        height: 40px;
        background: #FFFFFF;
        box-shadow: 0px 1px 0px 0px #E4E4E4;
        border-radius: 4px 4px 0px 0px;
        font-size: 14px;
        color: #333;
        display: flex;
        justify-content: space-between;
    }

    #indexIFrame #index-info-temp-many .info-nav-item {
        font-size: 15px;
        padding: 0 8px;
        text-align: center;
        line-height: 40px;
        letter-spacing: 2px;
        text-indent: 5px;
        cursor: pointer;
        /* flex-shrink: 0; */
        white-space: nowrap;
        overflow: hidden;
    }

    #indexIFrame #index-info-temp-many .info-nav-item .down-select-icon{
        font-size: 24px;
        margin-top: 8px;
        color: #000;
        font-weight: 700;
    }

    #indexIFrame #index-info-temp-many .info-nav-item .red-icon{
        width: 8px;
        height: 8px;
        background: #E24242;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    #indexIFrame #index-info-temp-many .active {
        color: #5260FF;
        font-weight: 600;
    }

    #indexIFrame #index-info-temp-many .dropdown-box.active {
        color: #333;
        font-weight: normal;
    }

    #indexIFrame #index-info-temp-many .active::after {
        content: '';
        width: 60px;
        display: block;
        margin: 0 auto;
        border-bottom: 2px solid #5260FF;
    }

    #indexIFrame #index-info-temp-many .info-nav-more {
        font-size: 12px;
        color: #999;
        letter-spacing: 2px;
        display: block;
        text-align: center;
        height: 30px;
        line-height: 40px;
    }

    #indexIFrame #index-info-temp-many .info-nav-more {
        font-size: 12px;
        color: #999;
        letter-spacing: 2px;
        display: block;
        text-align: center;
        height: 30px;
        line-height: 40px;
    }

    #indexIFrame #index-info-temp-many .info-tab-content {
        display: flex;
        padding: 16px 0 16px 16px;
        height: calc(100% - 40px);
        box-sizing: border-box;
    }

    #indexIFrame #index-info-temp-many .info-content-left {
        flex: 1;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        cursor: pointer;
        margin-right: 16px;
        overflow: hidden;
        background: #FFFFFF;
        box-shadow: 0px 0px 8px 0px #D2DEF0;
    }

    #indexIFrame #index-info-temp-many .info-content-left .left-info-content-box {
        padding: 8px 8px 0;
        height: 100%;
        position: relative;
    }

    #indexIFrame #index-info-temp-many .info-content-left .date {
        position: absolute;
        right: 8px;
        bottom: 8px;
        font-size: 12px;
        color: rgba(51, 51, 51, 0.3);
        line-height: 16px;
    }

    #indexIFrame #index-info-temp-many .info-content-left img {
        width: 100%;
        height: 108px;
    }

    #index-info-temp-many .info-content-left .firstTitle {
        margin-bottom: 4px;
        width: 100%;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 5;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        overflow: hidden;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
    }

    #index-info-temp-many .info-content-left .firstContent {
        font-size: 12px;
        color: rgba(51, 51, 51, 0.7);
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
    }

    #indexIFrame #index-info-temp-many .info-content-right {
        flex: 2;
        height: 100%;
        overflow: hidden;
        border-radius: 4px;
    }

    #indexIFrame #index-info-temp-many .content-right-list {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        padding-right: 8px;
    }

    #indexIFrame #index-info-temp-many .content-right-list li {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        box-shadow: 0px 0.5px 0px 0px #f4f4f4;
        cursor: pointer;
        position: relative;
        padding: 8px 12px 8px 8px;
    }

    #index-info-temp-many .content-right-list li .info-title {
        flex: 1;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        display: flex;
        align-items: center;
    }

    #index-info-temp-many .content-right-list li .yuan {
        position: absolute;
        top: 50%;
        left: 0px;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #5260FF;
    }

    #index-info-temp-many .content-right-list li .info-title .content {
        font-size: 14px;
        color: #333333;
        line-height: 16px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    #index-info-temp-many .content-right-list li .date {
        font-size: 11px;
        color: #999;
        line-height: 16px;
    }

    #indexIFrame .new {
        font-size: 12px;
        color: red;
        font-family: monospace;
        margin-right: 4px;
    }

    #indexIFrame .info-tab-content .important {
        display: inline-block;
        line-height: 18px;
        text-align: center;
        width: 18px;
        border: 1px solid #f93a4a;
        color: #f93a4a;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-tab-content .top {
        display: inline-block;
        line-height: 18px;
        width: 18px;
        text-align: center;
        border: 1px solid #f59a23;
        color: #f59a23;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-tab-content .refine {
        display: inline-block;
        line-height: 18px;
        text-align: center;
        width: 18px;
        border: 1px solid #1677ff;
        color: #1677ff;
        margin-right: 5px;
        border-radius: 3px;
        font-size: 12px;
    }

    #indexIFrame .info-tab-content li:hover {
        background-color: #edefff;
    }

    #indexIFrame .info-tab-content li:hover .info-title {
        color: #5260ff;
    }

    #indexIFrame .dropdown-box {
        display: none;
        position: absolute;
        top: 40px;
        filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.2));
    }
    #indexIFrame .dropdown-box::after {
        display: none !important;
    }

    #indexIFrame .dropdown-box.active {
        display: block;
    }

    #indexIFrame .dropdown-box .angle-icon {
        position: absolute;
        border-bottom: 8px solid #FFF;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
    }

    #indexIFrame .dropdown-box .dropdown-content {
        min-width: 100px;
        overflow: auto;
    }
    #indexIFrame .dropdown-box .dropdown-content::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    #indexIFrame .dropdown-box .dropdown-content:hover::-webkit-scrollbar-thumb{
      border-radius: 8px;
      height: 50px;
      background: #00000033;
    }
    #indexIFrame .dropdown-box .dropdown-content::-webkit-scrollbar-track{
      box-shadow: inset 0 0 5px #ffffffdc;
      border-radius: 8px;
      background: #FFF;
    }
    #indexIFrame #index-info-temp-many .content-right-list li:hover .one-line {
      white-space: unset;
    }
</style>
<div class="real_content shadow_box bg_fff none" id="index-info-temp-many">
    <div class="info-nav-header">
        <div class="info-nav" style="display: flex;">
        </div>
        <div class="fr" style="width: 24px; margin-right: 8px;">
            <a href="javascript:;" class="info-nav-more" _target="self">
                <i class="oaicon oa-icon-icon_shouye_gengduo" style="font-size: 20px;color: #999"></i>
            </a>
        </div>
    </div>
    <div class="info-tab-content content-bg-con">
    </div>
</div>

<div class="real_content shadow_box bg_fff content-bg-con home-page noBg none" id="index-info-temp-list">
    <div class="">
        <div class="title">
            <div class="fr" style="width: 24px">
                <a href="javascript:;" class="more" _target="self">
                    <i class="oaicon oa-icon-icon_shouye_gengduo"></i>
                </a>
            </div>
            <p class="title_content">
                <i class="sign"></i>
                <span class="column-name"></span>
                <span class="data-num info-num none">0</span>
            </p>
        </div>
    </div>
    <div class="index_content scrollbar-box scroll-box-height">
        <div class="info-content content-bg-con home-page noBg"></div>
    </div>
</div>