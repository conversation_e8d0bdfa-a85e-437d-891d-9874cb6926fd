<style>
    #outLinkIndex .index_content {
        height: calc(100% - 40px);
    }
    #outLinkIndex .access-box {
        width: 100%;
        height: 80%;
        overflow: hidden;
        position: relative;
    }

    #outLinkIndex .access-box ol,
    #outLinkIndex .access-box .access-list .access-list-item > .layui-row {
        padding: 0;
    }

    #outLinkIndex .access-footer {
        width: 100%;
        height: 20%;
        display: flex;
        align-items: center;
    }

    #outLinkIndex .access-footer .operate {
        margin: 0 auto;
        width: 40px;
        display: flex;
        justify-content: space-between;
    }

    #outLinkIndex .control_prev,
    #outLinkIndex .control_next {
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 12px solid #E4E4E4;
        transform: rotate(90deg);
    }

    #outLinkIndex .control_prev {
        transform: rotate(-90deg);
    }

    #outLinkIndex .control_next {
        /*right: 0px;*/
    }

    #outLinkIndex .access-box ul.access-list {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
    }
    #outLinkIndex .access-box ul.access-list > li {
        position: relative;
        float: left;
        width: 100%;
        height: 100%;
    }
    #outLinkIndex .access-list-item ol {
        height: 100%;
        padding: 3%;
        box-sizing: border-box;
    }
    #outLinkIndex .access-list-item ol li {
        position: relative;
        height: 50%;
    }
    #outLinkIndex .access-list-item ol li .access-item {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: table;
    }
    #outLinkIndex .access {
        height: 100%;
        width: 100%;
        display: table;
        vertical-align: middle;
        text-align: center;
    }
    #outLinkIndex .access a {
        display: table-cell;
        height: 100%;
        width: 100%;
        vertical-align: middle;
    }
    #outLinkIndex .icon-box {
        display: inline-block;
        height: 24px;
        width: 24px;
        background-color: #fff;
        border-radius: 50%;
        text-align: center;
        color: #fff;
        font-size: 0;
        line-height: 1;
        position: relative;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: 0 0;
    }

    #outLinkIndex .icon-box i {
        font-size: 24px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    #outLinkIndex .access-text {
        overflow: hidden;
        text-overflow: ellipsis;
        /* white-space: nowrap; */
    }

    #indexIFrame #outLinkIndex .title {
    	border-bottom: 1px solid #E4E4E4;
        /* background-image: linear-gradient(180deg, rgba(231, 243, 255, 0.8) 0%, rgba(207, 231, 255, 0.8) 100%); */
    }

    #indexIFrame #outLinkIndex .title .title_content i {
        color: #5b98d9;
        font-size: 18px;
        display: inline-block;
        font-weight: 400;
        vertical-align: top;
    }

    #indexIFrame #outLinkIndex .title_content .sign {
        width: 4px;
		height: 18px;
		background: #5260FF;
		border-radius: 2px;
		margin-top: 6px;
    }
</style>
<div class="real_content bg_fff shadow_box content-bg-con home-page" id="outLinkIndex">
    <!-- <div class="">
        <div class="title">
            <p class="title_content">
                <i class="sign"></i>
                应用导航
            </p>
        </div>
    </div> -->
    <div class="home-page-new-item-box">
        <div class="tips-title-left">
            <i class="tips"></i>
            <span class="top-item-title">应用导航</span>
        </div>
    </div>
    <div class="index_content" style="bottom: 0">
        <div class="access-box">
            <ul class="access-list"></ul>
        </div>
        <div class="access-footer">
            <div class="operate">
                <a href="javascript:;" class="control_prev none">
                </a>
                <a href="javascript:;" class="control_next none">
                </a>
            </div>
        </div>
    </div>
</div>
