'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        opt.$dom.html(html);

        hotRecommendation();

        function openDetails(id) {
            var rowData = {};
            rowData.reader = 'yes';
            rowData.id = id;
            $.quoteFun('/information/messageRelease/msgDetail', {
                trasen: trasenTable,
                data: rowData,
                title: '查看详情',
            });
        }

        function hotRecommendation() {
            $.ajax({
                type: 'get',
                url: common.url + '/ts-information/information/selectBannerInformation',
                async: false,
                success: function (res) {
                    var imgList = [];
                    var html = [];
                    $.each(res.object || [], function (index, item) {
                        imgList.push({
                            img: '/ts-document/attachment/' + item.infoPhotoUrl,
                            a: 'javascript:;',
                            id: item.id,
                        });
                        html.push('<p class="one-line">' + item.informationTitle + '</p>');
                    });
                    if (imgList.length) {
                        $('#indexIFrame .hot-recommendation').closest('.content-bg-con').addClass('noBg');
                    } else {
                        $('#indexIFrame .hot-recommendation').closest('.content-bg-con').removeClass('noBg');
                    }
                    // $('#indexIFrame .hot-recommendation-text').html(html);
                    $('.hot-recommendation', opt.$dom).trasenSlider({
                        imgList: imgList, //图片的列表
                        textList: html,
                        width: '100%', //图片的宽
                        height: 'unset', //图片的高
                        isAuto: imgList.length > 1, //是否自动轮播
                        moveTime: 5000, //运动时间
                        direction: 'right', //轮播的方向
                        btnWidth: 0, //按钮的宽
                        btnHeight: 0, //按钮的高
                        spanWidth: 8, //span按钮的宽
                        spanHeight: 8, //span按钮的高
                        spanColor: 'rgba(255, 255, 255, 0.5)', //span按钮的颜色
                        activeSpanColor: 'rgba(255, 255, 255, 1)', //选中的span颜色
                        spanPosition: {
                            left: '92%',
                        },
                        activeSpanWidth: 20,
                        activeSpanHeight: 8,
                        btnBackgroundColor: 'transparent', //两侧按钮的颜色
                        spanRadius: '5px', //span按钮的圆角程度
                        spanMargin: 3, //span之间的距离
                        activeFun: function (index) {},
                        imgClick: function (index) {
                            openDetails(imgList[index].id);
                        },
                    });

                    let dotDom = $('.spot', opt.$dom),
                      domWidth = dotDom.width();
                    dotDom.css({
                      left: '',
                      right: '4px'
                    });
                    $('.imgText', opt.$dom).css('padding-right', `${domWidth + 8}px`);
                },
                error: function (res) {},
            });
        }
    };
});
