'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var checkData = [];
        var defData = [];
        var treeObj;
        var colorObj = {
            0: '#B75E5E',
            1: '#5260FF',
            2: '#43AD6A',
            3: '#4AA7D9',
            4: '#A052FF',
        };
        var wins = layer.open({
            type: 1,
            title: '常用入口配置',
            closeBtn: false,
            maxmin: false,
            shadeClose: false,
            area: ['605px', '435px'], //宽高
            content: html,
            success: function (layero) {
                getData();
            },
        });

        function dealData(node) {
            var n;
            for (var i = 0; i < checkData.length; i++) {
                if (checkData[i].menuUrl == node.alink) {
                    n = i;
                    break;
                }
            }
            if (n != undefined) {
                checkData.splice(n, 1);
            } else {
                checkData.push({
                    menuUrl: node.alink,
                    menuName: node.name,
                    menuIcon: node.menuIcon,
                });
            }
            setTemp();
        }

        function setTemp() {
            var html = '';
            for (var i = 0; i < checkData.length; i++) {
                html +=
                    '<div class="layui-col-md4" data-id="' +
                    i +
                    '">\
                        <div class="access-item">\
                            <div class="access">\
                                <a href="#">\
                                    <span class="icon-box" style="background-color:' +
                    colorObj[i % 5] +
                    '">\
                                                                    <i class="oaicon ' +
                    (checkData[i].menuIcon || 'oa-icon-rukou') +
                    '"></i>\
                                                                </span>\
                                                                <p class="access-text">' +
                    checkData[i].menuName +
                    '</p>\
                                </a>\
                            </div>\
                            <div class="acess-del '+ (checkData[i].isDefault ? 'is-defualt': '')  +'"><i class="oaicon oa-icon-error"></i></div>\
                        </div>\
                    </div>';
            }
            $('#setUsesMenu  .usesmenu-box .layui-row').html(html);
            var so = Sortable.create($('#setUsesMenu  .usesmenu-box .layui-row')[0], {
                sort: true,
                dragClass: 'sortable-drag',
                onEnd: function (evt) {
                    sortData(so.toArray());
                },
            });
        }

        function dealDefMenu(arr) {
            var obj = [];
            for (var i = 0; i < arr.length; i++) {
                if (arr[i].alink == '/index') {
                    continue;
                }
                var item = {
                    name: arr[i].menuname,
                    alink: arr[i].alink,
                    icon: arr[i].icon,
                    checked: false,
                    children: [],
                };
                for (var n = 0; n < checkData.length; n++) {
                    if (checkData[n].menuUrl == item.alink) {
                        item.checked = true;
                        break;
                    }
                }
                obj.push(item);
                if (arr[i].menus) {
                    item.children = dealDefMenu(arr[i].menus);
                }
                if (!item.children.length) {
                    item.children = undefined;
                }
                if (item.alink == '#' || !item.alink || item.alink == 'javascript:;') {
                    item.chkDisabled = true;
                }
            }
            return obj;
        }

        function sortData(nArr) {
            var arr = [];
            if (nArr) {
                for (var i = 0; i < nArr.length; i++) {
                    var newItem = Object.assign(checkData[nArr[i]], { sord: i });
                    arr.push(newItem);
                }
            }
            checkData = arr;
            setTemp();
        }

        function getData() {
            $.ajax({
                url: '/ts-basics-bottom/quickMenu/selectQuickMenuList',
                method: 'post',
                success: function (res) {
                    if (res.success && res.object) {
                        checkData = res.object || [];
                        defData = dealDefMenu(common.menuData);
                        initTree();
                        setTemp();
                    }
                },
            });
        }

        function initTree() {
            var setting = {
                data: {
                    simpleData: {
                        enable: true,
                    },
                },
                check: {
                    enable: true,
                    chkStyle: 'checkbox',
                },
                callback: {
                    onClick: onClick,
                    onCheck: function (event, treeId, treeNode) {
                        var p = treeNode.getParentNode();
                        if (p) {
                            treeNode.menuIcon = p.icon;
                        }
                        if (treeNode.icon) {
                            treeNode.menuIcon = treeNode.icon;
                        }
                        dealData(treeNode);
                    },
                    onNodeCreated: function (e, id, node) {},
                },
            };
            function onClick(event, treeId, treeNode) {}
            treeObj = $.fn.zTree.init($('#setUsesMenuTree'), setting, defData);
        }
        $('#setUsesMenu')
            .off('click', '.acess-del')
            .on('click', '.acess-del', function () {
                var index = $(this).closest('.layui-col-md4').attr('data-id');
                var data = checkData[index];
                var checkNodes = treeObj.getCheckedNodes(true);
                for (var i = 0; i < checkNodes.length; i++) {
                    if (checkNodes[i].alink == data.menuUrl) {
                        treeObj.checkNode(checkNodes[i], false);
                        break;
                    }
                }
                checkData.splice(index, 1);
                setTemp();
                return false;
            });
        $('#setUsesMenu #save')
            .off('click')
            .on('click', function () {
                $.ajax({
                    url: '/ts-basics-bottom/quickMenu/save',
                    method: 'post',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify(checkData),
                    success: function (res) {
                        if (res.success) {
                            opt.ref && opt.ref();
                            layer.msg(res.message || '保存成功');
                            layer.close(wins);
                        } else {
                            layer.msg(res.message || '保存失败');
                        }
                    },
                });
                return false;
            });
        $('#setUsesMenu #close')
            .off('click')
            .on('click', function () {
                layer.close(wins);
                return false;
            });
    };
});
