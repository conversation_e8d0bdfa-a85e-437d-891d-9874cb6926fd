"use strict";
define(function (require, exports, module) {
	exports.init = function (opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect'], function () {
			var form = layui.form,
				laydate = layui.laydate,
				trasen = layui.trasen,
				upload = layui.upload;
			var trasenTable;
			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['100%', '100%'],
				skin: 'yourclass',
				content: html,
				success: function (layero, index) {
					//设置隐藏applyId
					$("input[name='applyId']").val(opt.data.applyId);

					form.render();

					//掉用回显数据
					theEchoData(opt.data.applyId, opt.data.id);
				}
			});

			//员工类型搜索
			$('#singInPersonSearch').funs('click', function () {
				refresh();
			})

			//刷新
			function refresh() {
				trasenTable.refresh();
			}

			//人员查看详情
			$('#meeting_showPersonDetails').funs('click', function () {
				var itemtab = $("#meetingDetailForm .left-wrap").children().eq(3);
				$(itemtab).addClass('shell-active').siblings().removeClass("shell-active");
				var getHrefId = $(itemtab).attr("hrefId");
				$("#" + getHrefId).addClass('shell-active').siblings().removeClass("shell-active");
			})

			//流程表单左侧tab切换
			$(".container-left .control-item").click(function () {
				$(this).addClass('shell-active').siblings().removeClass("shell-active");
				var getHrefId = $(this).attr("hrefId");
				$("#" + getHrefId).addClass('shell-active').siblings().removeClass("shell-active");
			})
		})

		//回显数据
		function theEchoData(applyId, businessId) {
			$.ajax({
				type: "post",
				url: common.url + '/ts-resource/boardroom/apply/selectBusinessIdDetail/' + businessId,
				success: function (res) {
					var obj = res.object;
					//填充数据
					if (obj != null) {
						var addrHtml = '';
						$.each(obj, function (i, val) {
							addrHtml += '<div class="layui-col-xs6"><label class="shell-layui-form-label control-lable">会议时间：</label>';
							addrHtml += '<div class="shell-layer-input-box control-box" >' + val.startTime + ' 至 ' + val.endTime + '</div></div>';
							addrHtml += '<div class="layui-col-xs6"><label class="shell-layui-form-label control-lable">会议地点：</label>';
							addrHtml += '<div class="shell-layer-input-box control-box" style="color: #70B603">' + val.location + '  ' + val.name + '</div> </div>';
						});
					}

					$("#oaMymyAppointmentMeetingDetails_name").html(obj[0].name);
					$("#oaMymyAppointmentMeetingDetails_name").attr('title', obj[0].name);
					$("#oaMymyAppointmentMeetingDetails_motif").html(obj[0].motif);
					$("#oaMymyAppointmentMeetingDetails_motif").attr('title', obj[0].motif);
					$("#oaMymyAppointmentMeetingDetails_type").html(obj[0].apptypeid);
					$("#oaMymyAppointmentMeetingDetails_content").html(obj[0].content);
					$("#oaMymyAppointmentMeetingDetails_emceeName").html(obj[0].emceeName);
					$("#oaMymyAppointmentMeetingDetails_controlNumber").html(obj[0].controlNumber);
					$("#oaMymyAppointmentMeetingDetails_linktelePhone").html(obj[0].linktelePhone);
					$("#oaMymyAppointmentMeetingDetails_linktelePerson").html(obj[0].linktelePerson);
					$("#meetingDetailsAddrAndTime").append(addrHtml);
					showPerson(obj[0].id); //回显人员
					//回显议程
					var html = '';
					if (obj[0].boardroomAgenda != null) {

						$.each(obj[0].boardroomAgenda, function (i, val) {
							html += '<tr>';
							html += '<td><div>' + val.agenda + '</div></td>';
							html += '<td><div>' + val.content + '</div></td>';
							html += '<td><div>' + val.functionary + '</div></td>';
							html += '<td><div>' + val.num + '</div></td>';
							html += '<td><div></div></td>';
							html += '</tr>';
						});
					}
					$("#oa_myAppointment_TB_file").empty();
					$("#oa_myAppointment_TB_file").append(html);
					//回显附件
					initInformationFiles(obj[0].accessoryId, obj[0].accessoryName, obj[0].accessoryUrl);
				},
				error: function (res) {
					res = JSON.parse(res.responseText);
					layer.msg(res.message);
				}
			});
		}

		//参会人员
		function showPerson(applyId) {

			$.ajax({
				type: "get",
				url: common.url + '/ts-resource/boardroom/signin/getAllList/' + applyId,
				success: function (res) {
					var text = '';
					if (res.object != null && res.object.length > 0) {
						$.each(res.object, function (i, val) {
							if (i == res.object.length - 1) {
								text += val.signinUsername;
							} else {
								text += val.signinUsername + "、";
							}

						});
					}
					$("#oaMymyAppointmentMeetingDetails_showPersonDetails").html(text);
				},
				error: function (res) {
					layer.msg('参会人员获取失败');
				}
			});
		}

		//初始化附件信息
		var fileArr = [];
		function initInformationFiles(fileIds, fileNames, fileUrls) {
			var filesStr = '';
			if (!IsSpace(fileIds) && !IsSpace(fileNames) && !IsSpace(fileUrls)) {
				var fileIdsArr = fileIds.split("#,#");
				var fileNamesArr = fileNames.split("#,#");
				var fileUrlArr = fileUrls.split("#,#");
				for (var i = 0; i < fileIdsArr.length; i++) {
					var isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(fileNamesArr[i]);
					var isDoc = common.isDoc(fileNamesArr[i]);
					//var suffix = fileNamesArr[i].split(".")[1];
					var suffix = fileNamesArr[i].substr(fileNamesArr[i].lastIndexOf("."));  
					
					var fileNameSuffix = fileIdsArr[i]  + suffix;
					var downUrl = common.url + '/ts-document/attachment/downloadFile/' +  fileIdsArr[i];
					fileArr.push({
						fileName:  fileNameSuffix,
						fileUrl:  fileNameSuffix
					})
					filesStr += "<li>";
					filesStr += fileNamesArr[i] + '&nbsp;&nbsp;&nbsp;&nbsp;';
					filesStr += isImg ? '<em style="margin-right:10px;cursor: pointer;color:blue" class="viewerImg" fileurl="' +  fileNameSuffix + '">预览</em>' : '';
					filesStr += isDoc ? '<em style="margin-right:10px;cursor: pointer;color:blue" class="viewerDocOne2" filename="' + fileNameSuffix + '"  fileid="' +  fileIdsArr[i] + '" fileurl="' + downUrl + '">预览</em>' : '';
					filesStr += "<a href='" + downUrl + "'>";
					filesStr += '<img src="../../static/img/other/download.png" style="width: 16px;height: 16px;vertical-align: middle;cursor: pointer;"/>';
					filesStr += '</a></li>';
				}
			}
			$("#oa_myAgenda_detailsFiles").html(filesStr);
		}
		$('.myAgenda-control-inverted').off('click', '.viewerImg').on('click', '.viewerImg', function (e) {
			common.viewerImg(fileArr, $(this).attr('fileurl'))
			e.stopPropagation();
			return false
		})

		//非空判断
		function IsSpace(strMain) {
			var strComp = strMain;
			try {
				if (strComp == "  " || strComp == "" || strComp == " " ||
					strComp == null || strComp == "null" || strComp.length == 0 ||
					typeof strMain == "undefined" || strMain == "undefined") {
					return true;
				} else {
					return false;
				}
			} catch (e) {
				return false;
			}
		}

	};

});