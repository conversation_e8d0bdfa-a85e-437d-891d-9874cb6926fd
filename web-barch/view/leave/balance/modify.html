<style>
    .shell-layer-input-box{margin: 0; width: 40%; float: left;box-sizing: border-box; padding-left: 110px;}
    .layui-col-xs11{margin-bottom: 10px;}
    #evaBaseFile{position: absolute; background: #009b8d; height: 20px; font-size: 12px; color: #fff; right: 5px; top: 4px; border-radius: 3px; line-height: 20px; padding: 0 5px; cursor: pointer;}
</style>
<form id="evaBaseAddForm" class="layui-form">
    <input type="hidden" name="userId" value="" id="userId">
    <input type="hidden" name="userName" value="" id="userName">
    <input type="hidden" name="leaveTypeId" value="" id="leaveTypeId">
    <input type="hidden" name="leaveTypeName" value="" id="leaveTypeName">
    <div class="layui-content-box">

        <div class="layui-col-xs11">
            <label class="shell-layui-form-label"><span class="required">*</span>修改假期余额</label>
            <div class="shell-layer-input-box">
                <select name="modifyType" id="modifyType" lay-search>
                    <option value="add">增加</option>
                    <option value="reduce">减少</option>
                </select>
            </div>
            <input type="text" name="modifyValue" autocomplete="off" lay-verify="required" class="layui-input"
            id="modifyValue" style="width: 30%; display: inline-block; margin: 0 10px;">小时
        </div>

        <div class="layui-col-xs11">
            <label class="shell-layui-form-label"><span class="required">*</span>理由</label>
            <div class="shell-layer-input-box">
                <textarea type="text" name="remark" class="layui-input" id="remark"></textarea>
            </div>
        </div>
    </div>

    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="evaBaseSubmitCofirm">确定</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">取消</button>
    </div>
</form>