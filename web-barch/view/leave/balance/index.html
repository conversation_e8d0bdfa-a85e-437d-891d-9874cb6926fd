<style type="text/css">
    .topBox {
        background: #fff;
        margin-right: 10px;
        /*padding-bottom: 10px;*/
        margin-bottom: 10px;
    }

    .content-box {
        background: #fff;
        margin-right: 10px;
    }

    .topTabBox {
        border-bottom: 1px solid #ccc;
        height: 30px;
    }

    .topTabBox span {
        line-height: 30px;
        position: relative;
        top: -1px;
        float: left;
        padding: 0 20px;
        cursor: pointer;
    }

    .topTabBox .on {
        border-bottom: 3px solid #5260ff;
        color: #5260ff;
    }

    .employeeBasicListBox {
        display: none;
    }

    #employeeBasicListBox .trasen-con-box .table-box {
        right: 10px;
        top: 50px !important;
        background: #fff;
    }

    #employeeBasicListBox .trasen-con-box {
        background: #fff;
        left: 0;
    }
</style>
<!-- 主表格内容 -->
<!--<div style="font-size: 14px; line-height:30px;" id="inspetSetmealCount">-->
<!--<span style="padding-left: 9px;"><b style="color: #1E9FFF">本月请假次数统计</b>&nbsp;</span> &nbsp;|&nbsp;&nbsp;-->
<!--</div>-->

<div class="content-box" id="employeeBasicListBox">
    <div class="trasen-con-box employeeBasicListBox" style="display: block; top: 0 !important">
        <div class="topBox">
            <div class="row" style="padding-top: 10px">
                <form id="evaBaseEmployeeForm" class="layui-form">
                    <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">姓名</span>
                        <div class="shell-layer-input-box">
                            <input type="text" name="empName" class="layui-input" id="emp_Name" placeholder="" />
                        </div>
                    </div>

                    <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">发薪号</span>
                        <div class="shell-layer-input-box">
                            <input type="text" name="empCode" class="layui-input" id="emp_Code" placeholder="" />
                        </div>
                    </div>

                    <div class="shell-search-box">
                        <span class="shell-layer-input-boxTit">部门</span>
                        <div class="shell-layer-input-box">
                            <input type="text" autocomplete="off" name="empDeptName" class="layui-input" placeholder="请选择" lay-verify="required" id="empDept" zTreeLick="click" />
                            <i class="layui-edge"></i>
                            <input type="hidden" id="empDeptCode" name="empDeptCode" />
                        </div>
                    </div>

                    <div class="pubBtnBox fl mgl10">
                        <button type="button" class="" id="EvaBaseSearch">搜索</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-box" style="top: 45px">
            <table id="indexEmployeeTable"></table>
            <!-- 分页 -->
            <div id="indexEmployeePage"></div>
        </div>
    </div>
</div>
