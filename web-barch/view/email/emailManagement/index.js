'use strict';

define(function (require, exports, module) {
    module.exports = {
        init: init,
        cancle: function () {
            Event.create('emailList').remove('changeStatus');
        },
    };
    function init() {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            var util = layui.util;

            //初始化form表单
            form.render();

            // 写内部邮件
            $('#inEmailBtn')
                .off('click')
                .on('click', function () {
                    $.quoteFun('/email/emailManagement/editInEmail', {
                        title: '写邮件',
                    });
                });
            
            //  写外部邮件
            /*$('#outEmailBtn').funs('click', function () {
                $.quoteFun('/email/emailManagement/editOutEmail', {
                    title: '写邮件',
                });
            });*/

            //tab页签切换
            var index = 0;
            $('#emailManage .oa-nav .oa-nav_item')
                .off('click')
                .on('click', function () {
                    setTabClick($(this));
                });
            
            function setTabClick($dom) {
                $dom.addClass('active').siblings().removeClass('active');
                index = $dom.index();
                $('.email-slider-content .email-slider').eq(index).show().siblings().hide();
                $('#emailManagementSearchForm')[0].reset();
                $('#startEmailManagementDate').val('');
                $('#endEmailManagementDate').val('');
                if (0 == index) {
                    $('#folderId').val('1');
                    $('#detOptBtnBox').show();
                    $("#signEmailReadDiv").show();
                    $("#delEmailBtns").show();
                    $("#emailManagementToBox").hide();
                    $("#emailManagementSenderBox").show();
                    var len = $('#emailManagementTable').getGridParam('width');
                    $('#emailManagementTable').setGridParam().showCol('senderName');
                    $('#emailManagementTable').setGridParam().hideCol('toName');
                    $('#emailManagementTable').setGridWidth(len);
                }
                if (1 == index) {
                    $('#folderId').val('2');
                    $('#detOptBtnBox').hide();
                    $("#signEmailReadDiv").hide();
                    $("#delEmailBtns").show();
                    $("#emailManagementToBox").show();
                    $("#emailManagementSenderBox").hide();
                    var len = $('#emailManagementTable').getGridParam('width');
                    $('#emailManagementTable').setGridParam().hideCol('senderName');
                    $('#emailManagementTable').setGridParam().showCol('toName');
                    $('#emailManagementTable').setGridWidth(len);
                }
                if (2 == index) {
                    $('#folderId').val('3');
                    $('#detOptBtnBox').hide();
                    $("#signEmailReadDiv").hide();
                    $("#delEmailBtns").show();
                    $("#emailManagementToBox").show();
                    $("#emailManagementSenderBox").hide();
                    var len = $('#emailManagementTable').getGridParam('width');
                    $('#emailManagementTable').setGridParam().hideCol('senderName');
                    $('#emailManagementTable').setGridParam().showCol('toName');
                    $('#emailManagementTable').setGridWidth(len);
                }
                if (3 == index) {
                    $('#folderId').val('4');
                    $('#detOptBtnBox').hide();
                    $("#signEmailReadDiv").hide();
                    $("#delEmailBtns").hide();
                    $("#emailManagementToBox").show();
                    $("#emailManagementSenderBox").hide();
                    var len = $('#emailManagementTable').getGridParam('width');
                    $('#emailManagementTable').setGridParam().hideCol('senderName');
                    $('#emailManagementTable').setGridParam().showCol('toName');
                    $('#emailManagementTable').setGridWidth(len);
                }
                refresh();
            }
            var inboxTable;
            initTable();
            function initTable() {
                inboxTable = new $.trasenTable('emailManagementTable', {
                    url: common.url + '/ts-information/optimize/emailInternal/list',
                    mtype: 'get',
                    datatype: 'json',
                    ajaxGridOptions: {
                        async: false,
                        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                    },
                    colModel: [
                        {
                            // label: '<img src="/static/img/other/mail_read.png" style="margin-right:5px;margin-left:10px"><img src="/static/img/other/mail_file_2.png">',
                            label:' ',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            name: 'labelMail',
                            align: 'left',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, rowObject) {
                                var str = '';
                                if (index == 2) {
                                    str += "<img width='16' heigth='16' src='/static/img/other/mail_file.png' style='margin-left:8px'>";
                                } else {
                                    if (0 == rowObject.isSeen) {
                                        if(6 == rowObject.status){
                                            str += "<img width='16' heigth='16' title='已读' src='/static/img/other/mail_read.png' style='margin-left:8px'>";
                                        }else{
                                            str += "<img width='16' heigth='16' title='未读' src='/static/img/other/mail_no.png' style='margin-left:8px'>";
                                        }
                                    } else if (1 == rowObject.isSeen || 1 == cellvalue.isSeen) {
                                        if ((null != rowObject.subject && rowObject.subject.indexOf('回复：') != -1) || (null != cellvalue && cellvalue.subject.indexOf('回复：') != -1)) {
                                            str += "<img  width='16' heigth='16' title='回复' src='/static/img/other/mail_send.png' style='margin-left:8px'>";
                                        } else if ((null != rowObject.subject && rowObject.subject.indexOf('转发：') != -1) || (null != cellvalue && cellvalue.subject.indexOf('转发：') != -1)) {
                                            str += "<img  width='16' heigth='16' title='转发'  src='/static/img/other/mail_back.png' style='margin-left:8px'>";
                                        } else {
                                            str += "<img  width='16' heigth='16'  title='已读' src='/static/img/other/mail_read.png' style='margin-left:8px'>";
                                        }
                                    }
                                }
                                if (1 == rowObject.hasAttachment || (null != cellvalue && 1 == cellvalue.hasAttachment)) {
                                    str += '<img width="16" heigth="16" title="附件" src="/static/img/other/mail_file.svg" style="margin-left:5px">';
                                }
                                return str;
                            }
                        },
                        {
                            label: '发件人',
                            sortable: false,
                            name: 'senderName',
                            width: 70,
                            formatter: function (cellvalue, options, rowObject) {
                                if (0 == rowObject.isSeen && 6 != rowObject.status) {
                                    return "<span style='font-weight: bold'>" + cellvalue + "</span>";
                                }else{
                                    return cellvalue;
                                }
                            }
                        },
                        {
                            label: '收件人',
                            sortable: false,
                            name: 'toName',
                            hidden: true,
                            width: 70,
                            formatter: function (cellvalue, options, rowObject) {
                                if(null != rowObject.outEmailAddress && '' != rowObject.outEmailAddress){
                                    if(null != cellvalue && '' != cellvalue){
                                        return cellvalue + "," + rowObject.outEmailAddress
                                    }else{
                                        return rowObject.outEmailAddress
                                    }
                                }else{
                                    return cellvalue;
                                }
                            }
                        },
                        {
                            label: '主题', 
                            sortable: false,
                            name: 'subject',
                            width: 200,
                            formatter: function (cellvalue, options, rowObject) {
                                var str = "";
                                if (6 == rowObject.status || (null != cellvalue && 6 == cellvalue.status)) {
                                    str += '<img width="16" heigth="16" title="已撤回" src="/static/img/other/icon_chehui.svg" style="margin-left:5px">';
                                /* str += '发件方已撤回邮件：';*/
                                }
                                if(2 == rowObject.stateLevel || (null != cellvalue && 2 == cellvalue.stateLevel)){
                                    str += '<img width="16" heigth="16"  title="紧急邮件" src="/static/img/other/veto.svg" style="margin-left:5px">' ;
                                }
                                if ((0 == rowObject.isSeen || (null != cellvalue && 0 == cellvalue.isSeen)) && 6 != rowObject.status) {
                                    str += "<span width='16' heigth='16' style='font-weight: bold'>" +  rowObject.subject || cellvalue.subject + "</span>";
                                }else{
                                    if(null != rowObject.subject && '' != rowObject.subject){
                                        str += rowObject.subject;
                                    }else{
                                        if(null != cellvalue){
                                            str += cellvalue.subject;
                                        }
                                    }
                                }
                                return str;
                            },
                        },
                        {
                            label: '时间',
                            name: 'postTime',
                            sortable: false,
                            width: 80,
                            formatter: function (cellvalue, options, rowObject) {
                                var postTime = "";
                                var str = "";
                                if(1 == rowObject.timing || (null != cellvalue && 1 == cellvalue.timing)){
                                    str += '<img title="定时发送" src="/static/img/other/clock.svg" style="margin-left:5px;margin-top:-1px;"> ' ;
                                    if(null != cellvalue && "undefined" != typeof(cellvalue.postTime)){
                                        postTime = cellvalue.startTiming;
                                    }else{
                                        postTime = rowObject.startTiming;
                                    }
                                }else{
                                    if(null != cellvalue && "undefined" != typeof(cellvalue.postTime)){
                                        postTime = cellvalue.postTime;
                                    }else{
                                        postTime = rowObject.postTime;
                                    }
                                }
                                
                                var postDay = util.toDateString(postTime,'yyyy-MM-dd');
                                var today = util.toDateString(new Date(),'yyyy-MM-dd');
                                
                                var days = DateDiff(postDay,today);
                                
                                var postYear = util.toDateString(postTime,'yyyy');
                                var toYear = util.toDateString(new Date(),'yyyy');
                                
                                if (postDay == today) {
                                    str += "今天 " + util.toDateString(postTime,'HH:mm');
                                }else if (days == 1 && postDay < today) {
                                    str += "昨天 " + util.toDateString(postTime,'HH:mm');
                                }else if (days == 2 && postDay < today) {
                                    str += "前天 " + util.toDateString(postTime,'HH:mm');
                                }else if (days == 1 && postDay > today) {
                                    str += "明天 " + util.toDateString(postTime,'HH:mm');
                                }else if (days == 2 && postDay > today) {
                                    str += "后天 " + util.toDateString(postTime,'HH:mm');
                                }else if(postYear == toYear){
                                    str += util.toDateString(postTime,'MM月dd日');
                                }else{
                                    str += util.toDateString(postTime,'yyyy年MM月dd日');
                                }
                                
                                if ((0 == rowObject.isSeen || (null != cellvalue && 0 == cellvalue.isSeen)) && 6 != rowObject.status) {
                                    return "<span style='font-weight: bold'>" + str + "</span>";
                                }else{
                                    if(1 == rowObject.timing || (null != cellvalue && 1 == cellvalue.timing)){
                                        return str;
                                    }else{
                                        if(2 == index){
                                            return "<span style='margin-left: 26px'>" + str + "</span>";
                                        }else{
                                            return str;
                                        }
                                    }
                                }
                            },
                        },
                        /*    {
                                label: 'id',
                                name: 'id',
                                hidden: true
                            },*/
                        {
                            label: 'outEmailAddress',
                            name: 'outEmailAddress',
                            hidden: true,
                        },
                        {
                            label: 'status',
                            name: 'status',
                            hidden: true
                        },
                        {
                            label: 'statusId',
                            name: 'statusId',
                            hidden: true,
                            key: true,
                        },
                        {
                            label: 'hasAttachment',
                            name: 'hasAttachment',
                            hidden: true,
                        },
                        {
                            label: 'content',
                            name: 'content',
                            hidden: true,
                        }, 
                        {
                            label: 'timing',
                            name: 'timing',
                            hidden: true,
                        }, 
                        {
                            label: 'stateLevel',
                            name: 'stateLevel',
                            hidden: true,
                        },
                        {
                            label: 'startTiming',
                            name: 'startTiming',
                            hidden: true,
                        }, 
                        {
                            label: 'firstType',
                            name: 'firstType',
                            hidden: true,
                        }, 
                        {
                            label: 'sendToAll',
                            name: 'sendToAll',
                            hidden: true,
                        }, 
                        {
                            label: 'tag',
                            name: 'tag',
                            hidden: true,
                        }
                    ],
                    buidQueryParams: function () {
                        var search = $('#emailManagementSearchForm').serializeArray();
                        var opt = [];
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value.trim();
                        }
                        return data;
                    },
                    onCellSelect: function (rowId, iCol, cellcontent, e) {
                        var ids = $("#emailManagementTable").getDataIDs();
                        for(var i = 0;i < ids.length; i++){
                            $('#'+ids[i]).css('background', '');
                        }
                        if (iCol > 1) {
                            $('#emailManagementTable').jqGrid('setSelection', rowId, false);
                            $('#emailManagementTable').find('.ui-state-highlight').css({'background': 'rgba(82, 96, 255,0.2)'});
                            var rowData = $('#emailManagementTable').jqGrid('getRowData', rowId);
                            Event.create('indexEmailList').trigger('changeStatus', rowData.statusId);
                            loadEmailData(rowData.statusId, rowId, rowData);
                        }
                    },
                    onSelectAll: function (rowid, status) {
                        var ids = $('#emailManagementTable').jqGrid('getGridParam','selarrrow');
                        if(ids.length > 0){
                            $("#batchEmailBtnDiv").show();
                        }else{
                            $("#batchEmailBtnDiv").hide();
                        }
                    },
                    onSelectRow: function (rowid, status) {
                        var ids = $('#emailManagementTable').jqGrid('getGridParam','selarrrow');
                        if(ids.length > 0){
                            $("#batchEmailBtnDiv").show();
                        }else{
                            $("#batchEmailBtnDiv").hide();
                        }
                    },
                    viewrecords: true,
                    //autowidth: true,
                    height: 'auto',
                    rownumbers: true,
                    //minColWidth : 100,
                    forceFit: false,
                    shrinkToFit: true,
                    //ondblClickRow: editRow,
                    multiselect: true,
                    gridview: true,
                    altRows: true,
                    pager: '#emailManagementPager',
                });
            }

            function DateDiff(sDate1, sDate2) {
                var aDate, oDate1, oDate2, iDays;
                aDate = sDate1.split("-");
                oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);  //转换为yyyy-MM-dd格式
                aDate = sDate2.split("-");
                oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
                iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数
                return iDays;  //返回相差天数
            }

            loadEmailNumbers();
            //邮件数量统计
            function loadEmailNumbers(){
            	 $.ajax({
                     type: 'post',
                     url: common.url + '/ts-information/optimize/emailInternal/selectMainEmailList',
                     success: function (res) {
                         if (res.success) {
                        	 $("#inboxSpan").html("0");
                        	 $("#outBoxSpan").html("0");
                        	 $("#draftBoxSapn").html("0");
                        	 $("#recycleSpan").html("0");
                        	 $.each(res.object,function(i,item){
                        		 if(1 == item.FOLDER_ID){
                        			 $("#inboxSpan").html(item.EMAIL_NUMS);
                        		 }
                        		 if(2 == item.FOLDER_ID){
                        			 $("#outBoxSpan").html(item.EMAIL_NUMS);
                        		 }
                        		 if(3 == item.FOLDER_ID){
                        			 $("#draftBoxSapn").html(item.EMAIL_NUMS);
                        		 }
                        		 if(4 == item.FOLDER_ID){
                        			 $("#recycleSpan").html(item.EMAIL_NUMS);
                        		 }
                        		 if(item.NO_READ > 0){
                        			 $("#noReadDiv").show();
                        			 $("#noReadSpan").html(item.NO_READ);
                        		 }else{
                        			 $("#noReadDiv").hide();
                        		 }
                        	 })

                            let noRead = res.object.map(item => item.NO_READ) || [];
                            function areAllValuesEqual(arr) {
                                for (let i = 1; i < arr.length; i++) {
                                    if (arr[i] !== arr[0]) {
                                        return false;
                                    }
                                }
                                return true;
                            }

                            if(areAllValuesEqual(noRead)) {
                                window.dispatchEvent(
                                    new CustomEvent('sendToNewFrameMessage', {
                                        detail: {
                                            type: 'headRightMessageChange',
                                            data: {
                                                type: 'emailNum',
                                                data: noRead[0] || 0
                                            }
                                        }
                                    })
                                );
                            }
                         } else {
                             layer.msg(res.message);
                         }
                     },
                     error: function (res) {
                         res = JSON.parse(res.responseText);
                         layer.msg(res.message);
                     },
                 });
            }
            
            //展开
            $('#emailManage')
                .off('click', '#showFileListBtn')
                .on('click', '#showFileListBtn', function (e) {
                    $("#emailFileListDiv").show();
                    $("#showFileListBtn").hide();
                    $("#hideFileListBtn").show();
                });

            //收起
            $('#emailManage')
                .off('click', '#hideFileListBtn')
                .on('click', '#hideFileListBtn', function (e) {
                    $("#emailFileListDiv").hide();
                    $("#showFileListBtn").show();
                    $("#hideFileListBtn").hide();
                });
            
          //清除详情
          function clearDetail() {
        	 $('#emailManage .content_bg').removeClass('no_bg');
        	 $("#emailTitle").html('');
        	 $("#senderName").html('');
        	 $("#postTime").html('');
        	 $("#postTime").html('');
        	 $("#receiveName").html('');
           $('#bccName').hide();
           $('#bccName span').html('');
        	 $("#ccName").html('');
        	 $("#readReceiptDiv").hide();
        	 $("#readReceiptedDiv").hide();
        	 $("#refuseReceiptedDiv").hide();
        	 $("#emailFileDiv").hide();
        	 $("#emailContent").html('');
        	 $('#emailManage #emailsHeadDiv').css("visibility","hidden");
        	 
        	 
        	 $("#signEmailNoReadBtn").hide();
            // $("#signEmailReadBtn").hide();
             $("#replyEmailBtn").hide();
             $("#replyAllEmailBtn").hide();
             $("#forwardEmailBtn").hide();
             $("#delEmailBtn").hide();
             $("#cancelMailBtn").hide();
             $("#editInEmailBtn").hide();
             $("#restoreEmailBtn").hide();
             $("#completelydelEmailBtn").hide();
             $("#sendStatusDiv").hide();
          }

            var detailsData;
            //加载左侧邮件信息
            function loadEmailData(statusId, rowid, rowData) {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-information/optimize/emailInternal/getEmailDetails?statusId=' + statusId,
                    async: false,
                    success: function (res) {
                        if (res.success) {
                        	$('#emailManage #emailsHeadDiv').css("visibility","visible");
                            $('#emailManage .content_bg').addClass('no_bg');
                            $('#emailManage .email-btn button').prop('disabled', false);
                            detailsData = res.object;
                            $('#emailManage .email-detail').removeClass('none');
                            if("6" == res.object.status){
                            	$('#emailManage #emailTitle').text("发件方已撤回邮件：" + res.object.subject);
                            }else{
                            	$('#emailManage #emailTitle').text(res.object.subject);
                            }
                            
                            $('#emailManage #senderName').text(res.object.senderName);
                            if("1" == res.object.timing && null != res.object.startTiming){
                            	$('#emailManage #postTime').text("将在" + util.toDateString(res.object.startTiming,'yyyy年MM月dd日 HH:mm') + "发送");
                            }else{
                            	$('#emailManage #postTime').text(res.object.postTime);
                            }
                            $('#emailManage #emailId').val(res.object.id);
                            $('#emailManage #emailStatusId').val(res.object.statusId);
                            
                            if (null != res.object.ccName && "" != res.object.ccName) {
                            	var ccNameArr = res.object.ccName.split(",");
                            	if(ccNameArr.length > 10){
                            		 var ccNameStr = "";
                            		 for(var i = 0; i <= 8; i++){
                            			 ccNameStr += ccNameArr[i] + ",";
                            		 }
	                           		 $('#emailManage #ccName').text(ccNameStr + "等" + ccNameArr.length + "人...");
	                           	}else{
	                           		$('#emailManage #ccName').text(res.object.ccName);
	                           	}
                            }else{
                            	$('#emailManage #ccName').text("无");
                            }
                        	var toNameArr = res.object.toName.split(",");
                        	if(toNameArr.length > 10){
                        		 var toNameStr = "";
                           		 for(var i = 0; i <= 8; i++){
                           			toNameStr += toNameArr[i] + ",";
                           		 }
                        		 $('#emailManage #receiveName').text(toNameStr + "等" + toNameArr.length + "人...");
                        	}else{
                        		
                        		if (res.object.outEmailAddress != null && res.object.outEmailAddress != '') {
                                    $('#emailManage #receiveName').text(res.object.toName + "," + res.object.outEmailAddress);
                                } else{
                                	$('#emailManage #receiveName').text(res.object.toName);
                                }
                        		
                        		
                        	}
                            if("1" == res.object.folderId){
                            	if("1" == res.object.readReceipt && "0" == res.object.isReplied){
                                	$("#readReceiptedDiv").hide();
                                	$("#readReceiptDiv").show();
                                	$("#refuseReceiptedDiv").hide();
                                }else if("1" == res.object.readReceipt && "1" == res.object.isReplied){
                                	$("#readReceiptedDiv").show();
                                	$("#readReceiptDiv").hide();
                                	$("#refuseReceiptedDiv").hide();
                                }else if("1" == res.object.readReceipt && "2" == res.object.isReplied){
                                	$("#readReceiptedDiv").hide();
                                	$("#readReceiptDiv").hide();
                                	$("#refuseReceiptedDiv").show();
                                }else{
                                	$("#readReceiptedDiv").hide();
                                	$("#readReceiptDiv").hide();
                                	$("#refuseReceiptedDiv").hide();
                                }
                            }else{
                            	$("#readReceiptedDiv").hide();
                            	$("#readReceiptDiv").hide();
                            	$("#refuseReceiptedDiv").hide();
                            }
                            var filesStr = '';
                            var fileArr = [];
                            if (1 == res.object.hasAttachment) {
                            	if("6" == res.object.status && 0 == index){
                            		$("#emailFileDiv").hide();
                            	}else{
                            		$("#emailFileDiv").show();
                                	$("#emailFileNumbers").html(res.object.attachmentList.length);
                                	$("#emailFileSize").html((res.object.attachmentSize / 1024).toFixed(1));
                                   /* filesStr = "<div class='emailAttachment'><div style='height: 20px;width: 100%;background: #eee;margin-top: 10px;padding: 5px;'>附件(" + res.object.attachmentList.length + '个，共' + (res.object.attachmentSize / 1024).toFixed(1) + 'KB)';
                                    if (res.object.attachmentList.length > 1) {
                                        filesStr += "<span class='emailAllFileDownBtn' style='color:blue;margin-left: 40px;cursor: pointer;'>批量下载</span>";
                                    }
                                    filesStr += '</div>';*/
                                    $.each(res.object.attachmentList, function (index, item) {
                                        fileArr.push({
                                            fileName: item.fileName,
                                            fileUrl: item.fileName,
                                        });
                                        var isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
                                        var isDoc = common.isDoc(item.fileName);
                                        var fileUrl = common.url + '/ts-document/attachment/downloadFile/' + item.id;
                                        filesStr += '<div><img src="/static/img/other/mail_file.svg" style="margin-left:6px;">';
                                        filesStr += item.originalName;
                                        /*filesStr += '<a href="javascript:void(0)" style="color:#2B8894;margin-left: 12px ">预览</a>';*/
                                        filesStr += isImg ? '<em style="margin-left:10px;cursor: pointer;" class="viewerImg" fileurl="' + item.fileName + '">预览</em>' : '';
                                        filesStr += isDoc ? '<em style="margin-left:10px;cursor: pointer;" class="viewerDocOne2" filename="' + item.fileName + '"  fileid="' + item.id + '" fileurl="' + item.fileName + '">预览</em>' : '';
                                        filesStr += '<a href="' + fileUrl + '" style="color:#2B8894;margin-left: 12px ">下载</a></div>';
                                        
                                        /*filesStr += "<div><i class='oaicon oa-icon-icon_attachment'></i><a href='" + fileUrl + "'>";
                                        filesStr += "<span style='color:#333'>" + item.originalName + '</spn>&nbsp;&nbsp;&nbsp;&nbsp;';
                                        filesStr += isImg ? '<em style="margin-right:10px" class="viewerImg" fileurl="' + item.fileName + '">预览</em>' : '';
    									filesStr += isDoc ? '<em style="margin-right:10px" class="viewerDocOne" fileid="' + item.id + '" fileurl="' + item.fileName + '">预览</em>' : '';
                                        filesStr += isImg ? '<em style="margin-right:10px" class="viewerImg" fileurl="' + item.fileName + '">预览</em>' : '';
                                        filesStr += isDoc ? '<em style="margin-right:10px" class="viewerDocOne2" filename="' + item.fileName + '"  fileid="' + item.id + '" fileurl="' + item.fileName + '">预览</em>' : '';
                                        filesStr += '<em>下载</em>';
                                        filesStr += '</a></li>';*/
                                    });
                                    $("#emailFileListDiv").html(filesStr);
                            	}
                            }else{
                            	$("#emailFileDiv").hide();
                            }
                            
                            var postTime = res.object.postTime;
                            var postdate = new Date(postTime);
                            var nowdate = new Date();
                           /* if (nowdate.getTime() - postdate.getTime() > 1000 * 60 * 30) {
                            	 $("#cancelMailBtn").attr("disabled","disabled");
                            }else{
                            	 $("#cancelMailBtn").removeAttr("disabled");
                            }*/

                            $('#emailManage')
                                .off('click', '.viewerImg')
                                .on('click', '.viewerImg', function (e) {
                                    common.viewerImg(fileArr, $(this).attr('fileurl'));
                                    e.stopPropagation();
                                    return false;
                                });
                            $('body').on('click', '.emailAllFileDownBtn', function () {
                                var fileList = $('#emailFileListDiv').find('a');
                                let ids = [];
                                $(fileList).each((index, item) => {
                                    let href = $(item).attr('href');
                                    let paramsArr = href.split('/');
                            
                                    ids.push(paramsArr[paramsArr.length - 1]);
                                    let url = common.url + '/ts-document/attachment/batchDownloadByIds?ids=' + ids.join(',');
                                    var alink = document.createElement('a');
                                    alink.href = url;
                                    alink.click();
                                })
                                // for (var i = 0; i < fileList.length; i++) {
                                //     (function (el, n) {
                                //         setTimeout(function () {
                                //             el.click();
                                //         }, n * 500);
                                //     })(fileList[i], i);
                                // }
                                return;
                            });
                            
                            if(null != res.object.content && "" != res.object.content){
                            	if("6" == res.object.status && 0 == index){
                            		$('#emailManage #emailContent').html("该邮件已被发件人撤回");
                            	}else{
                            		$('#emailManage #emailContent').html(res.object.content);
                            	}
                            }else{
                            	$('#emailManage #emailContent').html("无内容");
                            }
                            
                            $('#emailManage .email-detail').css({
                                position: 'absolute',
                            });
                            setTimeout(function () {
                                if ($('#emailManage .email-content').outerHeight() > $('#emailManage .email-content').parent().outerHeight() - $('#emailManage .email-detail').outerHeight()) {
                                    $('#emailManage .email-detail').css({
                                        position: 'static',
                                    });
                                } else {
                                    $('#emailManage .email-detail').css({
                                        position: 'absolute',
                                    });
                                }
                            }, 200);
                            
                            $("#completelydelEmailBtn").show();
                            if (0 == index) {
                            	if("6" == res.object.status){
                            		$("#signEmailNoReadBtn").hide();
                                   // $("#signEmailReadBtn").hide();
                                    $("#replyEmailBtn").hide();
                                    $("#replyAllEmailBtn").hide();
                                    $("#forwardEmailBtn").hide();
                                    $("#delEmailBtn").hide();
                                    $("#cancelMailBtn").hide();
                                    $("#editInEmailBtn").hide();
                                    $("#restoreEmailBtn").hide();
                                    $("#sendStatusDiv").hide();
                            	}else{
                            		$("#signEmailNoReadBtn").show();
                                   // $("#signEmailReadBtn").show();
                                    $("#replyEmailBtn").show();
                                    $("#replyAllEmailBtn").show();
                                    $("#forwardEmailBtn").show();
                                    $("#delEmailBtn").show();
                                    $("#cancelMailBtn").hide();
                                    $("#editInEmailBtn").hide();
                                    $("#restoreEmailBtn").hide();
                                    $("#sendStatusDiv").hide();
                            	}
                            }
                            if (1 == index) {
                            	if("6" == res.object.status){
                            		$("#signEmailNoReadBtn").hide();
                                    //$("#signEmailReadBtn").hide();
                                    $("#replyEmailBtn").hide();
                                    $("#replyAllEmailBtn").hide();
                                    $("#forwardEmailBtn").hide();
                                    $("#delEmailBtn").show();
                                    $("#cancelMailBtn").hide();
                                    $("#editInEmailBtn").show();
                                    $("#restoreEmailBtn").hide();
                                    $("#sendStatusDiv").show();
                                    $("#sendStatusSpan").html("已撤回");
                            	}else{
                            		$("#signEmailNoReadBtn").hide();
                                   // $("#signEmailReadBtn").hide();
                                    $("#replyEmailBtn").hide();
                                    $("#replyAllEmailBtn").hide();
                                    $("#forwardEmailBtn").show();
                                    $("#delEmailBtn").show();
                                    $("#cancelMailBtn").show();
                                    $("#editInEmailBtn").show();
                                    $("#restoreEmailBtn").hide();
                                    $("#sendStatusDiv").show();
                                    $("#sendStatusSpan").html("投递成功");
                            	}
                            }
                            if (2 == index) {
                                $("#signEmailNoReadBtn").hide();
                                //$("#signEmailReadBtn").hide();
                                $("#replyEmailBtn").hide();
                                $("#replyAllEmailBtn").hide();
                                $("#forwardEmailBtn").hide();
                                $("#delEmailBtn").show();
                                $("#cancelMailBtn").hide();
                                $("#editInEmailBtn").show();
                                $("#restoreEmailBtn").hide();
                                $("#sendStatusDiv").hide();
                            }
                            if (3 == index) {
                                $("#signEmailNoReadBtn").hide();
                               // $("#signEmailReadBtn").hide();
                                $("#replyEmailBtn").hide();
                                $("#replyAllEmailBtn").hide();
                                $("#forwardEmailBtn").hide();
                                $("#delEmailBtn").hide();
                                $("#cancelMailBtn").hide();
                                $("#editInEmailBtn").hide();
                                $("#restoreEmailBtn").show();
                                $("#sendStatusDiv").hide();
                            }
                            
                            if(common.userInfo.usercode == res.object.senderId && index > 0) {
                                $('#bccName').show();
                                $('#bccName span').html(res.object.bccName || '无')
                            }
                            
                            loadEmailNumbers();
                            rowData.isSeen = 1;
                            rowData.hasAttachment = res.object.hasAttachment;
                            rowData.stateLevel = res.object.stateLevel;
                            rowData.subject = res.object.subject;
                            rowData.status = res.object.status;
                            rowData.timing = res.object.timing;
                            rowData.postTime = res.object.postTime;
                            $('#emailManage #emailManagementTable').jqGrid("setCell", rowid, "labelMail", rowData);
                            $('#emailManage #emailManagementTable').jqGrid("setCell", rowid, "senderName", res.object.senderName);
                            $('#emailManage #emailManagementTable').jqGrid("setCell", rowid, "subject", rowData);
                            $('#emailManage #emailManagementTable').jqGrid("setCell", rowid, "postTime", rowData);
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            }

            //附件预览
            /*$('#emailManage').off('click', '.viewerDocOne2').on('click', '.viewerDocOne2', function (e) {
				 var url = common.url + '/ts-document/attachment/downloadFile/' + $(this).attr('fileid') + "?fullfilename=" + $(this).attr('filename')+ "&token=" + $.cookie('token');
				 window.open(common.url + '/ts-preview/onlinePreview?url='+encodeURIComponent(Base64.encode(url)));
				 return false;
			})*/
            
            
            //邮件签名
            $('#setEmailSignatureBtn').funs('click', function () {
            	$.quoteFun('/email/emailManagement/emailSignature', {
                    title: '邮件签名'
                });
            });
           /* //标记已读（批量）
            $('#signEmailReads').funs('click', function () {
            	operateMail('read','');
            });
           //标记未读（批量）
            $('#signEmailNoReads').funs('click', function () {
            	operateMail('unread','');
            });*/
            //标记已读 未读（批量）
            form.on('select(signEmailRead)', function (data) {
                if (0 == data.value) {
                    //已读
                    operateMail('read','');
                }
                if (1 == data.value) {
                    //未读
                    operateMail('unread','');
                }
            });
            //删除（批量）
            $('#delEmailBtns').funs('click', function () {
                operateMail('delete','确定要删除当前选中的邮件吗？');
            });
            //彻底删除（批量）
            $('#completelydelEmailBtns').funs('click', function () {
                operateMail('clean','彻底删除后邮件将无法恢复，确定要删除吗?');
            });
            //删除
            $('#delEmailBtn').funs('click', function () {
                operateMail('delete','确定要删除当前选中的邮件吗？',$("#emailStatusId").val());
            });
            //彻底删除
            $('#completelydelEmailBtn').funs('click', function () {
                operateMail('clean','彻底删除后邮件将无法恢复，确定要删除吗?',$("#emailStatusId").val());
            });
            //标记未读
            $('#signEmailNoReadBtn').funs('click', function () {
            	operateMail('unread','',$("#emailStatusId").val());
            });
            //标记已读
            $('#signEmailReadBtn').funs('click', function () {
            	operateMail('read','',$("#emailStatusId").val());
            });
            //恢复	
            $('#restoreEmailBtn').funs('click', function () {
            	operateMail('recovery','确定要恢复邮件吗？',$("#emailStatusId").val());
            });
            
            //查看投递详情
            $('#showSendDetails').funs('click', function () {
                $.quoteFun('/email/emailManagement/emailReadDetails', {
                    title: '投递详情',
                    data: detailsData
                });
            });
            //回复邮箱
            $('#replyEmailBtn').funs('click', function () {
                detailsData.type = '';
                $.quoteFun('/email/emailManagement/replyInEmail', {
                    title: '回复邮件',
                    data: detailsData,
                    ref: refresh,
                });
            });
            //回复全部
            $('#replyAllEmailBtn').funs('click', function () {
                detailsData.type = 'all';
                $.quoteFun('/email/emailManagement/replyInEmail', {
                    title: '回复邮件',
                    data: detailsData,
                    ref: refresh,
                });
            });
            //转发
            $('#forwardEmailBtn').funs('click', function () {
                detailsData.type = 'forward';
                $.quoteFun('/email/emailManagement/replyInEmail', {
                    title: '转发邮件',
                    data: detailsData,
                    ref: refresh,
                });
            });
            //回执
            $('#receiptBtn').funs('click', function () {
            	var emailId = $("#emailId").val();
            	var emailStatusId = $("#emailStatusId").val();
            	$.ajax({
                    type: 'post',
                    url: common.url + '/ts-information/optimize/emailInternal/receiptOpt?optType=1&emailId='+emailId + "&emailStatusId=" + emailStatusId,
                    success: function (res) {
                        if (res.success) {
                            layer.msg("您已发送回执");
                            $("#readReceiptedDiv").show();
                        	$("#readReceiptDiv").hide();
                        	$("#refuseReceiptedDiv").hide();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                })
            });
            //拒绝回执
            $('#refuseReceiptBtn').funs('click', function () {
            	var emailId = $("#emailId").val();
            	var emailStatusId = $("#emailStatusId").val();
            	$.ajax({
                    type: 'post',
                    url: common.url + '/ts-information/optimize/emailInternal/receiptOpt?optType=2&emailId=' + emailId + "&emailStatusId=" + emailStatusId,
                    success: function (res) {
                        if (res.success) {
                            layer.msg("您拒绝发送回执");
                            $("#readReceiptedDiv").hide();
                        	$("#readReceiptDiv").hide();
                        	$("#refuseReceiptedDiv").show();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                })
            });
            
            //标记已读未读
            /*form.on('select(signEmailRead)', function (data) {
                if (0 == data.value) {
                    //已读
                    operateMail('read');
                }
                if (1 == data.value) {
                    //未读
                    operateMail('unread');
                }
            });*/
            

            //移动
            /*form.on('select(moveEmailFolder)', function (data) {
                if (data.value != '') {
                    operateMail('move', data.value);
                }
            });*/

            function operateMail(operater,title,emailStatusId,value) {
            	var ids = "";
            	if('' != emailStatusId && null != emailStatusId){
            		ids = emailStatusId;
            	}else{
            		ids = $('#emailManagementTable').jqGrid('getGridParam', 'selarrrow');
            	}
                if (ids.length <= 0 || ids == "") {
                    layer.msg('请选择要操作的邮件！');
                    return;
                }
                
                if("" != title){
                	 layer.confirm(title,{
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                     },function (index) {
                         $.ajax({
                             type: 'post',
                             url: common.url + '/ts-information/optimize/emailInternal/operateMail?method=' + operater + '&ids=' + ids + '&folderId=' + value,
                             success: function (res) {
                                 if (res.success) {
                                     layer.msg(res.object);
                                     refresh();
                                 } else {
                                     layer.msg(res.message);
                                 }
                             },
                             error: function (res) {
                                 res = JSON.parse(res.responseText);
                                 layer.msg(res.message);
                             },
                         });
                     });
                }else{
                	$.ajax({
                        type: 'post',
                        url: common.url + '/ts-information/optimize/emailInternal/operateMail?method=' + operater + '&ids=' + ids + '&folderId=' + value,
                        success: function (res) {
                            if (res.success) {
                                layer.msg(res.object);
                                refresh();
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                }
            }
            
            //搜索
            $('#emailSearchBtn').funs('click', function () {
                refresh();
            });

            //重置
            $('#emailResetBtn').funs('click', function () {
                $('#emailManagementSearchForm')[0].reset();
                $('#startEmailManagementDate').val('');
                $('#endEmailManagementDate').val('');
                refresh();
            });

            //编辑内部邮箱
            $('body')
                .off('click', '#editInEmailBtn')
                .on('click', '#editInEmailBtn', function () {
                	if(1 == index){
                		detailsData.id = '';
                	}
                    $.quoteFun('/email/emailManagement/editInEmail', {
                        trasen: trasenTable,
                        title: '编辑邮件',
                        data: detailsData,
                        ref: refresh,
                    });
                });
            //编辑外部邮箱
            /*$('body')
                .off('click', '#editOutEmailBtn')
                .on('click', '#editOutEmailBtn', function () {
                    $.quoteFun('/email/emailManagement/editOutEmail', {
                        trasen: trasenTable,
                        title: '编辑邮箱',
                        data: detailsData,
                        ref: refresh,
                    });
                });*/
            //撤回
            $('body')
                .off('click', '#cancelMailBtn')
                .on('click', '#cancelMailBtn', function () {
                	
                    layer.confirm(
                        '<span style="font-size:16px">确定撤回此邮件吗？</span><br>如果撤回成成功，对方只能看到邮件的主题，并得到被撤回的提示。<br><span style="color:#777777;font-size:12px">详细说明：<br>仅能撤回发往内部系统的邮件，不支持外部邮件撤回。</span>',
                        {
                            btn: ['确定', '取消'],
                            title: '提示',      
							closeBtn: 0,
                            area:["540px","254px"]
                        },
                        function (index) {
                            $.ajax({
                                type: 'post',
                                url: common.url + '/ts-information/emailInternal/cancelEmailInternal?emailId=' + detailsData.id,
                                success: function (res) {
                                    if (res.success) {
                                        layer.msg(res.object);
                                        refresh();
                                    } else {
                                        layer.msg(res.message);
                                    }
                                },
                                error: function (res) {
                                    res = JSON.parse(res.responseText);
                                    layer.msg(res.message);
                                },
                            });
                            layer.close(index);
                        }
                    );
                });

            //查看转发信息详情
            $('body')
                .off('click', '.informationDetail')
                .on('click', '.informationDetail', function () {
                    var informationId = $(this).attr('id');
                    var rowData = {
                        id: informationId,
                    };
                    $.quoteFun('/information/messageRelease/msgDetail', {
                        trasen: trasenTable,
                        data: rowData,
                        title: '查看详情',
                    });
                });

            //时间控件
            laydate.render({
                elem: '#emailManagementDate',
                range: '~',
                showBottom: true,
                done: function (value, date, endDate) {
                    var dateArr = value.split(' ~ ');
                    $('#startEmailManagementDate').val(dateArr[0]);
                    $('#endEmailManagementDate').val(dateArr[1]);
                },
            });

            function refresh() {
                loadEmailNumbers();
                inboxTable.refresh();
                clearDetail();
            }
            //模块监听
            Event.create('emailList').listen('changeStatus', function (statusId, id, data) {
                if(index != 0) {
                    index = 0;
                    setTabClick($('#inBoxTab'));
                }
                setTimeout(() => {
                    var ids = $("#emailManagementTable").getDataIDs();
                    for(var i = 0;i < ids.length; i++){
                        $('#'+ids[i]).css('background', '');
                    }
                    $('#emailManagementTable').jqGrid('setSelection', statusId);
                    $('#emailManagementTable').find('.ui-state-highlight').css({'background': 'rgba(82, 96, 255,0.2)'});
                    $('#emailManagementTable').jqGrid('setSelection', statusId, false);
                    loadEmailData(statusId, statusId, data);
                }, 10)
            });
            Event.create('mainMessage').listen('messageToastEvent', function(val) {
              if(location.hash !== '#/email/emailManagement') {
                return;
              }
              let { businessId } = val;
              Event.create('hashChangeLoadJs').remove('jsLoaded');
              setTimeout(() => {
                // var ids = $("#emailManagementTable").getDataIDs();
                let tableData = $("#emailManagementTable").getGridParam('userData'),
                userItem = tableData.filter(item=>item.id == businessId);
                if(!userItem.length) {
                  return
                }
                let {statusId} = userItem[0];
                var ids = $("#emailManagementTable").getDataIDs();
                for(var i = 0;i < ids.length; i++){
                    $('#'+ids[i]).css('background', '');
                }
                $('#emailManagementTable').jqGrid('setSelection', statusId);
                $('#emailManagementTable').find('.ui-state-highlight').css({'background': 'rgba(82, 96, 255,0.2)'});
                $('#emailManagementTable').jqGrid('setSelection', statusId, false);
                loadEmailData(statusId, statusId, userItem[0]);
              }, 10)
            })
        });
    };
});
