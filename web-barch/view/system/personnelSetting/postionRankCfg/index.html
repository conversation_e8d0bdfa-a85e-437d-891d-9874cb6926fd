<style type="text/css">
    .mgl5 {
        margin-left: 5px;
    }
</style>
<div  id="postionnelSertting">

    <div>
        <form id="queryForm" class="layui-form areaButtonBoxL">
            <div class="layui-inline fl"><input type="text" name="postionRankName" placeholder="请输入等级名称" autocomplete="off" class="layui-input edi-layui-input searchInput" id="search-val" search-input="search">
            </div>
            <div class="pubBtnBox fl mgl5">
                <button type="button" lay-submit="" lay-filter="search" search-input="button">搜索</button>
            </div>
        </form>

        <div class="pubBtnBox areaButtonBoxR">
            <button type="button" class="" id="postionnelSerttingadd" data-permission="on">新增</button>
            <button type="button" class="" id="postionnelSerttingeditor" data-permission="on">编辑</button>
            <button type="button" class="" id="deleteBtn" data-permission="on">删除</button>
        </div>
    </div>
    
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table"></table>
            <!-- 分页 -->
            <div id="grid-pager"></div>
        </div>
    </div>
</div>
<!-- 添加表单 -->
<script type="text/html" id="postionnelSerttingaddHtml">
<div class="layui-tab-content">
    <form class="layui-form" id="postionnelSerttingaddForm">

        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label" for="postionRankName"><span class="required">*</span> 等级名称</label>
                <div class="layui-input-block">
                  <input type="text" lay-verify="required" autocomplete="off" placeholder="请输入等级名称" id="postionRankName" name="postionRankName" class="layui-input">
                  <input type="hidden" id="postionRankCfgId" name="postionRankCfgId" >
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span> 初始化积分</label>
                <div class="layui-input-block">
                    <input type="text" lay-verify="required" autocomplete="off" placeholder="请输入初始化积分" id="score" name="score" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span> 晋升积分</label>
                <div class="layui-input-block">
                    <input type="text" lay-verify="required" autocomplete="off" placeholder="请输入晋升积分" id="promotionScore" name="promotionScore" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span> 排序</label>
                <div class="layui-input-block">
                    <input type="text" required lay-verify="required" autocomplete="off" placeholder="请输入排序号" id="sortNo" name="sortNo" class="layui-input">
                </div>
            </div>
        </div>
        
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layer_btn" id="postionnelSerttingaddSub" lay-submit="" lay-filter="formSave">保存</button>
            <a href="javascript:;" class="layer_btn layer_back" id="close">关闭</a>
        </div>
    </form>
</div>
</script>