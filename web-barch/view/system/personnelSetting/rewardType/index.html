<style type="text/css">
    .mgl5 {
        margin-left: 2px;
    }
</style>
<div >

    <div class="button-area">
        <form id="queryForm" class="layui-form areaButtonBoxL">
            <div class="layui-inline">
                <input type="text" name="rewardTypeName" placeholder="请输入奖惩名称" autocomplete="off" class="layui-input edi-layui-input searchInput" id="search-val" search-input="search">
            </div>
            <a class="layui-btn mgl5 layui-btn-normal edi-button edi-button-blue" lay-submit="" lay-filter="search" search-input="button">搜索</a>
        </form>

        <div class="pubBtnBox areaButtonBoxR">
            <button type="button" class="" id="add" data-permission="on">新增</button>
            <button type="button" class="" id="editor" data-permission="on">编辑</button>
	        <button type="button" class="" id="ban" data-permission="on">删除</button>

            <!--更多 <button type="button" class="more" id="areaButtonMore"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></button> -->
        </div>
    </div>
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table"></table>

            <!-- 分页 -->
            <div id="grid-pager"></div>
        </div>
    </div>
</div>
<!-- 添加表单 -->
<script type="text/html" id="addHtml">
<div class="layui-tab-content">
    <form class="layui-form" id="addForm">
        
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label" for="rewardTypeName"><span class="required">*</span> 奖惩名称</label>
                <div class="layui-input-block">
                  <input type="text" lay-verify="title" required lay-verify="required" autocomplete="off" placeholder="请输入奖惩名称" id="rewardTypeName" name="rewardTypeName" class="layui-input" required>
                  <input type="hidden" id="rewardTypeCfgId" name="rewardTypeCfgId" >
                  <input type="hidden" id="isDeleted" name="isDeleted" value="y" >
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> 奖惩类型</label>
                    <div class="layui-input-block">
                        <select name="rewardCfgType" id="rewardCfgType" lay-verify="required" lay-search>
                            <option value="">请选择类型</option>
                            <option value="1">奖励类型</option>
                            <option value="2">扣款类型</option>
                        </select>
                    </div>
            </div>
        </div>

        <div class="layer-btn archivesTabBtn">
            <a href="javascript:;" class="layer_btn" id="addSub" lay-submit="" lay-filter="addSave">确定</a>
            <a href="javascript:;" class="layer_btn layer_back" id="close">关闭</a>
        </div>
    </form>
</div>
</script>
<script type="text/html" id="editorHtml">
<div class="layui-tab-content">
    <form class="layui-form" id="editorForm">
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label" for="rewardTypeName"><span class="required">*</span> 奖惩名称</label>
                <div class="layui-input-block">
                    <input type="text" autocomplete="off" lay-verify="required" value="{{ d.rewardTypeName }}" placeholder="请输入奖惩名称" id="rewardTypeName" name="rewardTypeName" class="layui-input">
                    <input type="hidden" value="" id="rewardTypeCfgId" name="rewardTypeCfgId" >
                </div>
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span> 奖惩类型</label>
                <div class="layui-input-block">
                    <select id="rewardCfgType" name="rewardCfgType" lay-verify="required" lay-search>
                        <option value="">请选择奖惩类型</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layer_btn" id="editorSub" lay-submit="" lay-filter="editorSub">确定</button>
            <a href="javascript:;" class="layer_btn layer_back" id="close">关闭</a>
        </div>
    </form>
</div>
</script>

