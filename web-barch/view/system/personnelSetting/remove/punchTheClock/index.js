"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function () {
        layui.config({
            debug: true,
            base: '/public/layui/lay/'
        })

        var trasenTable = new $.trasenTable("table-remove-punchTheClock", {
            url: common.url + '/ts-hr/exclusionperson/cfg/punchtheclock/list',
            pager: 'grid-pager',
            colNames: ['员工ID','ID', '工号','姓名'],
            colModel: [
                { name: 'employeeId', index: 'employeeId', width: 200,height:"auto", align: "center", editable: false , hidden: true},
                // {name: '', index: '', align: "center", width: 100, sortable: true, editable: false, formatter:
                //         function (cellvalue, options, rowObject) {
                //             var htmlBtn = '<button class="layui-btn layui-btn-danger layui-btn-xs"  id="deleteBtn" data-id=' + options.exclusionPersonCfgId + ' data-name=' + options.exclusionPersonCfgId + '> <i class="fa fa-trash-o"> </i> 删除 </button>'
                //             return htmlBtn;
                //         }},
                { name: 'exclusionPersonCfgId', index: 'exclusionPerson_cfg_id', width: "auto", align: "center", editable: false, hidden: true },
                { name: 'employeeCode', index: 'employee_code', width: 100,height:"auto", align: "center", editable: false },
                { name: 'employeeName', index: 'employee_name', width: 100,height:"auto", align: "center", editable: false }
            ],
            queryFormId: 'queryForm'
        });
        var rowData = trasenTable.getSelectRowData();
        function refreshTable() {
            trasenTable.refresh();
        }
        $(function () {
            //关闭layer
            $("body").off("click", "#close").on("click", "#close", function () {
                refreshTable();
                layer.closeAll()
            });

        })
        layui.use(['form', 'layedit', 'laydate', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laydate = layui.laydate;

            var trasen = layui.trasen;

            $("body").off("click", "#add").on("click", "#add", function () {
                var html = $("#addHtmlclock").html();
                var rowDatas = trasenTable.getSelectRowData();
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['680px', '400px'],
                    skin: 'yourclass',
                    content: html,
                    success: function (layero, index) {
                        trasen.setNamesVal(layero, rowDatas);
                        form.render();
                        var trasenTables = new $.trasenTable("grid-tables", {
                            url: common.url + '/ts-hr/employee/list',
                            pager: 'grid-pagers',
                            rowNum: 10,
                            postData:{
                                status:1,
                            },
                            colNames: ['ID', '工号', '姓名','操作'],
                            colModel: [
                                { name: 'employeeId', index: 'employeeId', width: "auto", align: "center", editable: false, sortable: true, hidden: true },
                                { name: 'code', index: 'code', width: 205, align: "center", editable: false , sortable: true},
                                {name: 'name', index: 'name', align: "center", width: 205, sortable: true, editable: false},
                                {name: '', index: '', align: "center", width: 205, sortable: true, editable: false, formatter:
                                        function (cellvalue, options, rowObject) {
                                            var htmlBtn = '<button class="layui-btn layui-btn layui-btn-xs edi-button-blue" id="removeClockeditBtn" data-id=' + options.employeeId + ' data-name=' + options.employeeId + '> <i class="fa fa-pencil-square-o" aria-hidden="true"></i> 添加 </button>'
                                            return htmlBtn;
                                        }},
                            ],
                            //queryFormId: 'queryForm'
                            buidQueryParams: function () {
                                return { name: $("#name").val(),status:1, }
                            },
                        });

                        function refreshTables() {
                            trasenTables.refresh();
                        }
                        $("body").off("click", "#removeClockeditBtn").on("click", "#removeClockeditBtn", function () {
                            var rowDatas = trasenTables.getSelectRowData();
                            var _url = common.url + "/ts-hr/exclusionperson/cfg/save";
                            var _data = {
                                "exclusionType":1,
                                "employeeId":rowDatas.employeeId,
                                "employeeName":rowDatas.name,
                                "employeeCode":rowDatas.code,
                                "isVaild":1,
                                "isDeleted":"N"};
                            _data = JSON.stringify(_data);
                            $.ajax({
                                type:"post", 
                                contentType: "application/json; charset=utf-8", 
                                url:_url, 
                                data:_data, 
                                success:function (res) {
                                    if(res.success){
                                        layer.msg(res.message);
                                    }else{
                                        layer.msg(res.message || '操作失败');
                                    }
                                    refreshTable()
                                }
                            });

                        });
                        $("body").off("click", "#removeClocksearch").on("click", "#removeClocksearch", function () {
                            refreshTables();
                        });
                    }
                });
            });

            // 删除操作
            $("body").off("click", "#deleteBtn").on("click", "#deleteBtn", function () {
                var rowId = trasenTable.getSelectRowId();
				if (rowId == null) {
					layer.msg("请选择一个记录进行操作.")
					return
				}
                layer.confirm('确定要删除吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var rowData = trasenTable.getSelectRowData(rowId);//表格行数据
                    var _url = common.url + "/ts-hr/exclusionperson/cfg/update";
                    var _data = {"exclusionPersonCfgId":rowData.exclusionPersonCfgId,"isDeleted":"Y"};
                    _data = JSON.stringify(_data);
                    $.ajax({
                        type:"post", 
                    contentType: "application/json; charset=utf-8", 
                        url:_url, 
                        data:_data, 
                        success:function (res) {
                            if(res.success){
                                refreshTable();
                            }else{
                                layer.closeAll();
                                layer.msg('操作失败！');
                            }
                        }
                    });

                    layer.close(index);
                    return false;
                }, function () {
                });
            });
        })
    }
})