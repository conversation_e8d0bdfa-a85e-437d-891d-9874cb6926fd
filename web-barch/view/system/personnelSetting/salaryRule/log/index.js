"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }

    module.exports = {
        init: init
    }

    //模版页面脚本
    var perform = function () {
        layui.use(['form', 'laytpl'], function () {

            var form = layui.form,
                laytpl = layui.laytpl;

            var jqGridF;

            $('#systemPersonnelSettingSalaryRuleTab').off('click','li>a').on('click','li>a',function () {
                $(this).parent().addClass('layui-this').siblings().removeClass('layui-this');
                if($(this).attr('data-href') == '/system/personnelSetting/salaryRule/log'){
                    $('#systemPersonnelSettingSalaryRulelog').show()
                    $('#systemPersonnelSettingSalaryRuleotherContent').hide()
                }else{
                    $('#systemPersonnelSettingSalaryRulelog').hide();
                    $('#systemPersonnelSettingSalaryRuleotherContent').show()
                    $.quoteFun($(this).attr('data-href')+'/index')
                }
            })

            var trasenTable = new $.trasenTable("grid-table-systemPersonnelSettingSalaryRulelog", {
                pager:'grid-pager-systemPersonnelSettingSalaryRulelog',
                url: common.url + '/ts-hr/log/fee/cfg/list',
                colNames: ['岗位ID', 'ID', '岗位', '扣款金额'],
                colModel: [
                    {
                        name: 'postionId',
                        index: 'postionId',
                        width: "auto",
                        align: "center",
                        editable: false,
                        hidden: true,
                        sortable: true
                    },
                    // {name : '',index : '',width:120,align : "center",editable : false, sortable: true,formatter:function(cellvalue, options, rowObject){
                    // 	var html = '<button class="layui-btn layui-btn layui-btn-xs edi-button-blue" id="logseditor" data-rowId="' + options.logFeeCfgId + '"> <i class="fa fa-pencil-square-o" aria-hidden="true"></i> 编辑 </button>' +
                    // 		'<button class="layui-btn layui-btn layui-btn-xs edi-button-red" id="deleteBtn" data-rowId="' + options.logFeeCfgId + '"> <i class="fa fa-trash-o"> </i> 删除 </button>';
                    //     return html;
                    // }},
                    {
                        name: 'logFeeCfgId',
                        index: 'log_fee_cfg_id',
                        width: "auto",
                        align: "center",
                        editable: false,
                        hidden: true,
                        sortable: true
                    },
                    {
                        name: 'postionName',
                        index: 'postion_name',
                        width: 100,
                        align: "center",
                        editable: false,
                        sortable: true
                    },
                    {name: 'fee', index: 'fee', width: 100, align: "center", editable: false, sortable: true}
                ],
            });

            function refreshTable() {
                trasenTable.refresh();
            }

            function initSelect(form, postionId) {
                $.ajax({
                    type: 'post',
                    url: common.url + "/ts-hr/postion/cfg/list",
                    success: function (data) {
                        if (data != null) {
                            var result = data.rows;
                            for (var i = 0; i < result.length; i++) {
                                if (postionId == result[i]['postionId']) {
                                    $("#systemPersonnelSettingSalaryRulelogeditorHtmlDiv #postionId").append("<option selected value='" + result[i]['postionId'] + "'>" + result[i]['postionName'] + "</option>");
                                } else {
                                    $("#systemPersonnelSettingSalaryRulelogeditorHtmlDiv #postionId").append("<option value='" + result[i]['postionId'] + "'>" + result[i]['postionName'] + "</option>");
                                }

                            }
                            form.render('select');
                        }
                    }
                })
            }

            //关闭layer
            $("body").on("click", "#close", function () {
                layer.closeAll()
            });

            //修改
            $("#systemPersonnelSettingSalaryRulelog").off("click", "#logseditor").on("click", "#logseditor", function () {
                var rowId = trasenTable.getSelectRowId();
                if (rowId == null) {
                    layer.msg("请选择一个记录进行操作.")
                    return
                }
                var rowData = trasenTable.getSelectRowData(rowId);

                var data = { //数据
                    "rowData": rowData
                }
                var getTpl = systemPersonnelSettingSalaryRulelogeditorHtml.innerHTML;
                laytpl(getTpl).render(data, function (html) {
                    layer.open({
                        type: 1,
                        title: '修改日志扣款设置',
                        closeBtn: 0,
                        shadeClose: false,
                        area: ['680px', '180px'],
                        skin: 'yourclass',
                        content: html,
                        success: function () {
                            initSelect(form, rowData.postionId);

                        }
                    });
                    form.render();
                    form.on('submit(systemPersonnelSettingSalaryRulelogeditorSub)', function (data) {
                        var _url = common.url + "/ts-hr/log/fee/cfg/update";
                        var _data = JSON.stringify(data.field);
                        $.ajax({
                            type: "post",
                            contentType: "application/json; charset=utf-8",
                            url: _url,
                            data: _data,
                            success: function (res) {
                                if (res.success) {
                                    trasenTable.refresh();
                                    layer.closeAll();
                                    layer.msg(res.message);
                                } else {
                                    layer.msg(res.message || '操作失败');
                                }
                            }
                        });
                        return false;
                    });

                });

            });

            function salaryRuleLogSelect(form) {
                $.ajax({
                    type: 'post',
                    contentType: 'application/json;charset=UTF-8',
                    url: common.url + "/ts-hr/postion/cfg/list",
                    success: function (data) {
                        if (data != null) {
                            var result = data.rows;
                            for (var i = 0; i < result.length; i++) {
                                $("#systemPersonnelSettingSalaryRulelogaddHtmllogDiv #postionIdadd").append("<option value='" + result[i]['postionId'] + "'>" + result[i]['postionName'] + "</option>");
                            }
                            form.render('select');
                        }
                    }
                })

            };
            //添加
            $("#systemPersonnelSettingSalaryRulelog").off("click", "#salaryRulelogadd").on("click", "#salaryRulelogadd", function () {
                var html = $("#systemPersonnelSettingSalaryRulelogaddHtmllog").html();
                layer.open({
                    type: 1,
                    title: '添加日志扣款设置',
                    closeBtn: 0,
                    shadeClose: false,
                    area: ['680px', '180px'],
                    skin: 'yourclass',
                    content: html,
                    success: function () {
                        salaryRuleLogSelect(form);
                    }
                });
                form.render();

                form.on('submit(systemPersonnelSettingSalaryRulelogaddSub)', function (data) {
                    var _url = common.url + "/ts-hr/log/fee/cfg/save";
                    var _data = JSON.stringify(data.field);
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: _url,
                        data: _data,
                        success: function (res) {
                            if (res.success) {
                                trasenTable.refresh();
                                layer.closeAll();
                                layer.msg(res.message);
                            } else {
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                    return false;
                });

            });


            //删除
            $("#systemPersonnelSettingSalaryRulelog").off("click", "#deleteBtn").on("click", "#deleteBtn", function () {
                var rowId = trasenTable.getSelectRowId();
                if (rowId == null) {
                    layer.msg("请选择一个记录进行操作.")
                    return
                }
                layer.confirm('确定要删除吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var rowData = trasenTable.getSelectRowData(rowId);//表格行数据
                    var _url = common.url + "/ts-hr/log/fee/cfg/update";
                    var _data = {"logFeeCfgId": rowData.logFeeCfgId, "isDeleted": 'Y'};
                    _data = JSON.stringify(_data);
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: _url,
                        data: _data,
                        success: function (res) {
                            if (res.success) {
                                layer.msg('删除成功！');
                                refreshTable();
                            } else {
                                layer.closeAll();
                                layer.msg('操作失败！');
                            }
                        }
                    });
                    layer.close(index);
                    return false;
                });
            });

        });
    };

})

