"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function () {
        layui.use(['form', 'layedit', 'upload', 'laydate', 'trasen'], function () {
            var form = layui.form,
                trasen = layui.trasen,
                laytpl = layui.laytpl,
                laydate=layui.laydate;
            var trasenTables = new $.trasenTable("grid-table-systemPersonnelSettingAppraiseTemplets", {
                url: common.url + '/ts-hr/appraise/templet/info/list',
                pager: 'grid-pager-systemPersonnelSettingAppraiseTemplets',
                loadComplete: function () {
                    var self = this;
                    $("#" + self.id).setGridWidth($('#systemPersonnelSettingAppraiseTemplet').width() * 0.995);
                },
                colModel: [
                    {label:'id',name: 'appraiseTempletInfoId', index: 'appraiseTempletInfoId', align: "center", editable: false , sortable: true, hidden: true},
                    {label:'模板名称',name: 'templetName', index: 'templetName', align: "center", editable: false , sortable: true, hidden: true},
                    {label:'岗位ID',name: 'postId', index: 'post_id', align: "center", editable: false , sortable: true, hidden: true},
                    {label:'模板名称',name: 'templetNames', index: 'templet_name', align: "center", width: 250, editable: false , sortable: true,formatter: function (cellvalue, options, rowObject) {
                            return '<a href="javascript:;" style="color:#0090ff;" class="" id="templetButton" data-rowid="' + options.rowId + '">' + rowObject.templetName + '</a>';
                        }},
                    {label:'适用岗位',name: 'postName', index: 'post_name', align: "center", width: 350, sortable: true, editable: false},
                    {label:'版本号',name: 'version', index: 'version', align: "center", width: 100, sortable: true, editable: false},
                    {label:'总分',name: 'score', index: 'score', align: "center", width: 100, sortable: true, editable: false},
                    {label:'状态',name: 'isStart', index: 'is_start', align: "center", width: 100, sortable: true, editable: false,formatter:
                            function (cellvalue, options, rowObject) {
                                var i='';
                                if(cellvalue=='N'){
                                    i='启用'
                                }else{
                                    i='禁用'
                                }
                                return i;
                            } },
                    {label: '创建人',name : 'createUserName',index : 'create_user_name', align: "center",width:100,editable : false, sortable: true},
                    {label: '创建时间',name : 'createDate',index : 'create_date', align: "center",width:165,editable : false, sortable: true},
                    {label: '修改人',name : 'updateUserName',index : 'update_user_name', align: "center",width:100,editable : false, sortable: true},
                    {label: '修改时间',name : 'updateDate',index : 'update_date', align: "center",width:165,editable : false, sortable: true},
                ],
                buidQueryParams: function () {
                    var search = $("#systemPersonnelSettingAppraiseTempletqueryForms").serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                }
            });
            function refreshTables() {
                trasenTables.refresh();
            }

            var trasenTable = new $.trasenTable("grid-table-systemPersonnelSettingAppraiseTemplet", {
                url: common.url + '/ts-hr/appraise/project/info/list',
                pager: 'grid-pager-systemPersonnelSettingAppraiseTemplet',
                loadComplete: function () {
                    var self = this;
                    $("#" + self.id).setGridWidth($('#systemPersonnelSettingAppraiseTemplet').width() * 0.995);
                },
                colModel: [
                    {label:'id',name: 'appraiseProjectInfoId', index: 'appraiseProjectInfoId', align: "center", width: 500, editable: false , sortable: true, hidden: true},
                    {label:'评价项目名称',name: 'projectName', index: 'project_name', align: "center", width: 500, editable: false , sortable: true, hidden: true},
                    {label:'评价项目名称',name: 'projectNames', index: 'project_name', align: "center", width: 500, editable: false , sortable: true,formatter: function (cellvalue, options, rowObject) {
                            return '<a href="javascript:;" style="color:#0090ff;" class="" id="detailButton" data-rowid="' + options.rowId + '">' + rowObject.projectName + '</a>';
                        }},
                    {label:'评分小计',name: 'score', index: 'score', align: "center", width: 100, sortable: true, editable: false},
                    {label: '创建人',name : 'createUserName',index : 'create_user_name', align: "center",width:100,editable : false, sortable: true},
                    {label: '创建时间',name : 'createDate',index : 'create_date', align: "center",width:165,editable : false, sortable: true},
                    {label: '修改人',name : 'updateUserName',index : 'update_user_name', align: "center",width:100,editable : false, sortable: true},
                    {label: '修改时间',name : 'updateDate',index : 'update_date', align: "center",width:165,editable : false, sortable: true},
                ],
                buidQueryParams: function () {
                    var search = $("#systemPersonnelSettingAppraiseTempletqueryForm").serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                }
            });
            function refreshTable() {
                trasenTable.refresh();
            }

            //评价项目tab切换
            $("#systemPersonnelSettingAppraiseTemplet").off("click", "#project").on("click", "#project", function () {
                refreshTable();
            })

            //评价模板tab切换
            $("#systemPersonnelSettingAppraiseTemplet").off("click", "#templet").on("click", "#templet", function () {
                refreshTables();
            })


            $(function () {
                //关闭layer
                $("body").off("click", "#close").on("click", "#close", function () {
                    refreshTable();
                    layer.closeAll()
                });

            })


            // start --------------评价模板事件------------------
            var projects;
            //新增项目
            $('body').off('click','#systemPersonnelSettingAppraiseTempletDiv #addsoftwareReceivables').on('click','#systemPersonnelSettingAppraiseTempletDiv #addsoftwareReceivables',function(){
                var data = {id:projects.getDataIDs().length + 1};
                $('#systemPersonnelSettingAppraiseTempletDiv #grid-table-hardwareReceivable .triggerClick').trigger('click');
                projects.addRowData(projects.getDataIDs().length + 1,data,'last');
                projects.setCell(projects.getDataIDs().length,'seqNo',projects.getDataIDs().length);
            });

            //行删除
            $('body').off('click','#systemPersonnelSettingAppraiseTempletDiv .softwareReceivablerowDels').on('click','#systemPersonnelSettingAppraiseTempletDiv .softwareReceivablerowDels',function(){
                var rowid = $(this).closest('tr').attr('id');
                projects.delRowData(rowid);
                var rows = projects.getAllData();
                var i=0;
                rows.forEach(function (value,index) {
                    if(value.score){
                        i+=Number(value.score);
                    }
                })
                $("#systemPersonnelSettingAppraiseTempletDiv #scores").html(i.toFixed(2));
            });

            //搜索
            $('#systemPersonnelSettingAppraiseTemplet').off('click','#screeningSubs').on('click','#screeningSubs',function(){
                refreshTables();
            });


            //查看
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#templetButton').on("click", "#templetButton", function () {
                var rowid = $(this).attr('data-rowId');
                var rowData = trasenTables.getRowData(rowid);
                layui.use(['form', 'laytpl'], function () {
                    var form = layui.form,
                        laytpl = layui.laytpl;
                    var getTpl = systemPersonnelSettingAppraiseTempletTempletHtml.innerHTML;
                    layer.open({
                        type: 1,
                        title: '查看模板',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['650px', '600px'],
                        skin: 'yourclass',
                        content: getTpl,
                        success: function (layero, index) {
                            $("#systemPersonnelSettingAppraiseTempletTempletHtmlDiv #title").html(rowData.templetName);
                            $("#systemPersonnelSettingAppraiseTempletTempletHtmlDiv #contents").html('适用岗位：'+rowData.postName);
                            var _data={
                                appraiseTempletInfoId:rowData.appraiseTempletInfoId,
                            }
                            $.ajax({
                                type: "post",
                                url:  common.url + "/ts-hr/appraise/templet/detailed/info/list",
                                data: _data,
                                success: function (res) {
                                    if (res.rows) {
                                       res.rows.forEach(function (val,index){
                                           if(val.appraiseProjectInfoId){
                                               var i=0;
                                               $('#systemPersonnelSettingAppraiseTempletTempletHtmlDiv #templets').append('<div id="aa' + val.appraiseProjectInfoId + '"></div>')
                                               $.ajax({
                                                   type: "post",
                                                   url: common.url + "/ts-hr/appraise/project/detailed/info/list",
                                                   data: {
                                                       appraiseProjectInfoId:val.appraiseProjectInfoId
                                                   },
                                                   success: function (res) {
                                                       if(res.rows.length>0){
                                                           var rows= res.rows;
                                                           rows.forEach(function (val) {
                                                               if(val.score>i){
                                                                   i=val.score
                                                               }
                                                           })
                                                           var html='<div class="layui-col-xs12">' +
                                                               '                <div class="lay-table-title">' +
                                                               '                    <span class="title">'+val.projectName+'</span>' +
                                                               '                    <span class="price" style="color: #a1a1a1">（总分'+val.score+'分，每项分值为0-'+i+'分，'+i+'分为最佳）</span>' +
                                                               '                </div>' +
                                                               '                <div class="table-box tra-table-box">' +
                                                               '                    <!-- 表单 -->' +
                                                               '                    <table id="'+val.appraiseTempletDetailedInfoId+'"></table>' +
                                                               '                </div>' +
                                                               '            </div>'
                                                           $("#systemPersonnelSettingAppraiseTempletTempletHtmlDiv #aa"+val.appraiseProjectInfoId).append(html);
                                                           new $.trasenTable(val.appraiseTempletDetailedInfoId, {
                                                               url:  common.url + "/ts-hr/appraise/project/detailed/info/list",
                                                               postData:{
                                                                   appraiseProjectInfoId:val.appraiseProjectInfoId
                                                               },
                                                               pager:"project-pagers",
                                                               cellEdit:true,
                                                               colModel: [
                                                                   {label: '评分内容',name : 'appraiseAppraise',index : 'appraise_appraise',width:440,editable : false, sortable: true},
                                                                   {label: '分值',name : 'score',index : 'score',width:90,editable : false, sortable: true,align: "center"},
                                                               ],
                                                           });
                                                       }
                                                   }
                                               })

                                           }

                                       })
                                    }
                                }
                            });
                        }
                    });
                });
            });


            //行内下拉选择
            $('body').off('click','#systemPersonnelSettingAppraiseTempletDiv .appraiseAppraises').on('click','#systemPersonnelSettingAppraiseTempletDiv .appraiseAppraises',function(e){
                interviewPeoClose();
                e.stopPropagation();
                $('body').trigger('click');
                var txt = $(this).text();
                $(this).attr('id', 'interviewPeoBoxse');
                $(this).closest('td').removeClass('appraiseAppraises').addClass('interviewPeoTd');
                setTimeout(function(){
                    //人员选择
                    new $.selectPlug('#systemPersonnelSettingAppraiseTempletDiv #interviewPeoBoxse', {
                        url: common.url + '/ts-hr/appraise/project/info/list',
                        searchType: 'json',
                        textName: 'projectName',  // 选项文字的key
                        valName: 'appraiseProjectInfoId', // 选项id的key
                        inpValId: 'interviewPeoval',  // 页面表单需要的 val参数的id
                        inpTextId: 'interviewPeoname',  // 页面表单需要的 name参数的id
                        callback: function (res) {
                            if(res){
                                var rowid = $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').closest('tr').attr('id');
                                $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').attr('id','');
                                $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').addClass('appraiseAppraises').removeClass('interviewPeoTd');
                                projects.setCell(rowid,'appraiseProjectInfoId',res.appraiseProjectInfoId);
                                projects.setCell(rowid,'projectName',res.projectName);
                                projects.setCell(rowid,'score',res.score);
                                var rows = projects.getAllData();
                                var i=0;
                                rows.forEach(function (value,index){
                                    if(value.score){
                                        i+=Number(value.score);
                                    }
                                })
                                $("#systemPersonnelSettingAppraiseTempletDiv #scores").html(i.toFixed(2));
                            }else{
                                $('#systemPersonnelSettingAppraiseTempletDiv #interviewPeoname').val(txt);
                            }
                        }
                    });
                    $('#systemPersonnelSettingAppraiseTempletDiv #interviewPeoname').trigger('click');
                })
            });

            //点击其他地方关闭面试部门选择
            function interviewPeoClose () {
                if($('#systemPersonnelSettingAppraiseTempletDiv #interviewPeoname').length > 0){
                    var value = $('#systemPersonnelSettingAppraiseTempletDiv #interviewPeoname').val();
                    var rowid = $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').closest('tr').attr('id');
                    $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').attr('id','');
                    $('#systemPersonnelSettingAppraiseTempletDiv .interviewPeoTd').addClass('appraiseAppraises').removeClass('interviewPeoTd');
                    projects.setCell(rowid, 'projectName', value); //保存name
                }
            }

            $(document).off('click', 'body').on('click', 'body', function(){
                interviewPeoClose();
            })

            //编辑
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#tableEditors_temple').on("click", "#tableEditors_temple", function () {
                var rowData = trasenTables.getSelectRowData();
                if (rowData.appraiseTempletInfoId==null || rowData.appraiseTempletInfoId==undefined ||rowData.appraiseTempletInfoId=='') {
                    layer.msg('请选中需要操作数据');
                    return false;
                }
                layui.use(['form', 'laytpl'], function () {
                    var form = layui.form,
                        laytpl = layui.laytpl;
                    var getTpl = systemPersonnelSettingAppraiseTempletaddTempletHtml.innerHTML;
                    layer.open({
                        type: 1,
                        title: '编辑-评价模板',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['800px', '550px'],
                        skin: 'yourclass',
                        content: getTpl,
                        success: function (layero, index) {
                            trasen.setNamesVal(layero, rowData);
                            $("#systemPersonnelSettingAppraiseTempletDiv #scores").html(rowData.score);
                            if(rowData.isStart=='启用'){
                                $("#systemPersonnelSettingAppraiseTempletDiv #isStart").attr("checked", true)
                            }else{
                                $("#systemPersonnelSettingAppraiseTempletDiv #isStart").attr("checked", false)
                            }
                            projects = new $.trasenTable("project-tables", {
                                url: common.url + '/ts-hr/appraise/templet/detailed/info/list',
                                postData:{
                                    appraiseTempletInfoId:rowData.appraiseTempletInfoId
                                },
                                pager:"project-pagers",
                                cellEdit:true,
                                colModel: [
                                    {label: 'id',name : 'appraiseProjectInfoId',index : 'appraiseProjectInfoId',width:450,editable : true, sortable: true, hidden: true},
                                    {label: '评分内容',name : 'projectName',index : 'project_name',width:450,classes:'appraiseAppraises',editable : false, sortable: true},
                                    {label: '分值',name : 'score',index : 'score',width:90,editable : false, sortable: true},
                                    {label: '操作',name : '',index : '',width:65,editable : false, sortable: true,formatter: function (cellvalue, options, rowObject) {
                                            return '<a href="javascript:;" class="softwareReceivablerowDels" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                        }},
                                ],
                            });
                            projects.refresh();
                            var obj=[];
                            rowData.postName = rowData.postName.split(',');
                            var postId = rowData.postId.split(',');
                            postId.pop();
                            rowData.postName.pop();
                            //格式化多选值
                            rowData.postName.forEach(function (val,index) {
                                if(val){
                                    var json={
                                        name:val,
                                        value:postId[index]
                                    }
                                    obj.push(json);
                                }
                            });
                            initRefUserNameListSelect(obj);

                            //监听指定开关
                            form.on('switch(systemPersonnelSettingAppraiseTempletDivswitchTest)', function(data){
                                var isStart=this.checked ? 'true' : 'false';
                                if(isStart=='true'){
                                    $("#systemPersonnelSettingAppraiseTempletDiv #isStart").val('N')
                                }else{
                                    $("#systemPersonnelSettingAppraiseTempletDiv #isStart").val('Y')
                                }
                            });
                            form.render("checkbox")
                        }
                    });
                });
            });

            //删除
            $("body").off("click", '#appraiseTempletdeleteBtnss_temple').on("click", "#appraiseTempletdeleteBtnss_temple", function () {
                var rowData = trasenTables.getSelectRowData();
                if (rowData==null || rowData==undefined ||rowData=='') {
                    layer.msg('请选择一条需要删除的数据!');
                    return false;
                }
                layer.confirm('确定要删除当前选中的数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var _url =  common.url + "/ts-hr/appraise/templet/info/delete/"+rowData.appraiseTempletInfoId;
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: _url,
                        success: function (res) {
                            if (res.success) {
                                trasenTables.refresh();
                                layer.closeAll();
                                layer.msg('操作成功');
                            } else {
                                layer.msg('操作失败！');
                            }
                        }
                    });
                    layer.close(index);
                }, function () {
                });
            });
            //新增
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#appraiseTempletadds_temple').on("click", "#appraiseTempletadds_temple", function () {
                layui.use(['form', 'laytpl'], function () {
                    var form = layui.form,
                        laytpl = layui.laytpl;
                    var getTpl = systemPersonnelSettingAppraiseTempletaddTempletHtml.innerHTML;
                    layer.open({
                        type: 1,
                        title: '新增-评价模板',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['800px', '550px'],
                        skin: 'yourclass',
                        content: getTpl,
                        success: function (layero, index) {
                            projects = new $.trasenTable("project-tables", {
                                url: common.url + '/ts-hr/appraise/templet/detailed/info/list',
                                postData:{
                                    appraiseTempletInfoId:1
                                },
                                pager:"project-pagers",
                                cellEdit:true,
                                colModel: [
                                    {label: 'id',name : 'appraiseProjectInfoId',index : 'appraiseProjectInfoId',width:450,editable : true, sortable: true, hidden: true},
                                    {label: '评分内容',name : 'projectName',index : 'projectName',width:450,classes:'appraiseAppraises',editable : false, sortable: true},
                                    {label: '分值',name : 'score',index : 'score',width:90,editable : false, sortable: true},
                                    {label: '操作',name : '',index : '',width:65,editable : false,classes:'sdww', sortable: true,formatter: function (cellvalue, options, rowObject) {
                                            return '<a href="javascript:;" class="softwareReceivablerowDels" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                        }},
                                ],
                            });
                            projects.refresh();
                            initRefUserNameListSelect();
                            //监听指定开关
                            form.on('switch(systemPersonnelSettingAppraiseTempletDivswitchTest)', function(data){
                                var isStart=this.checked ? 'true' : 'false';
                                if(isStart=='true'){
                                    $("#systemPersonnelSettingAppraiseTempletDiv #isStart").val('N')
                                }else{
                                    $("#systemPersonnelSettingAppraiseTempletDiv #isStart").val('Y')
                                }
                            });
                            form.render("checkbox")
                        }
                    });
                });
            });
            // 防止重复提交
            var locks = false;
            // 表单提交
            form.on('submit(systemPersonnelSettingAppraiseTempletDivsaves)', function (data) {
                if(data.field.isStart=='on' || data.field.isStart=='N' || data.field.isStart=='启用'){
                    data.field.isStart='N'
                }else{
                    data.field.isStart='Y'
                }
                $('#systemPersonnelSettingAppraiseTempletDiv .tra-table-box [classes="sdww"]').trigger('click');
                if($("#systemPersonnelSettingAppraiseTempletDiv #postId").val()==null || $("#systemPersonnelSettingAppraiseTempletDiv #postId").val()==undefined || $("#systemPersonnelSettingAppraiseTempletDiv #postId").val()==''){
                    layer.msg('适用岗位未选择');
                    return false;
                }
                //格式化报销项目
                var e=projects.getAllData();
                data.field.score=$("#systemPersonnelSettingAppraiseTempletDiv #scores").html();
                data.field.appraiseTempletDetailedInfoList=e;
                var _url=null;
                if(data.field.appraiseTempletInfoId){
                    _url=common.url + "/ts-hr/appraise/templet/info/update";
                }else{
                    _url=common.url + "/ts-hr/appraise/templet/info/save";
                }
                //获取postId
                var postId='';
                var postIds=$("#systemPersonnelSettingAppraiseTempletDiv #postId").val();
                //将postID转为字符串
                postIds.forEach(function (val,index) {
                    postId+=val+",";
                })
                data.field.postId=postId;
                var _data = JSON.stringify(data.field);
                if (!locks) {
                    locks = true;
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url:_url,
                        data: _data,
                        success: function (res) {
                            locks = false;
                            if (res.success) {
                                trasenTables.refresh();
                                layer.closeAll();
                                layer.msg('操作成功');
                            } else {
                                layer.msg(res.message || '操作失败');
                            }
                        },
                        error:function () {
                            locks = false;
                        }
                    });
                } else {
                    layer.msg('处理中...', { shade: [0.8, '#393D49'], time: 60 * 60 * 1000 });
                }
            });
            //岗位多选
            function initRefUserNameListSelect(val) {
                $.publicSelect2($("#systemPersonnelSettingAppraiseTempletDiv #postId"), {
                    url: "/ts-hr/post/list",
                    valueName: "postId",
                    textName: "postName",
                    multipleDefaultValue:val,
                    width: '550px',
                })
            }


            // end  --------------评价模板事件------------------

            // start --------------评价项目事件------------------


            //搜索
            $('#systemPersonnelSettingAppraiseTemplet').off('click','#screeningSub').on('click','#screeningSub',function(){
                refreshTable();
            });
            var project;
            var i=100;
            //新增项目
            $('body').off('click','#systemPersonnelSettingAppraiseTempletaddHtmlDiv #addsoftwareReceivable').on('click','#systemPersonnelSettingAppraiseTempletaddHtmlDiv #addsoftwareReceivable',function(){
                $('#systemPersonnelSettingAppraiseTempletaddHtmlDiv .tra-table-box [role="gridcell"]').trigger('click');
                var data = {id:i};
                $('#systemPersonnelSettingAppraiseTempletaddHtmlDiv #grid-table-hardwareReceivable .triggerClick').trigger('click');
                project.addRowData(i,data,'last');
                i--;
            });

            //行删除
            $('body').off('click','#systemPersonnelSettingAppraiseTempletaddHtmlDiv .softwareReceivablerowDel').on('click','#systemPersonnelSettingAppraiseTempletaddHtmlDiv .softwareReceivablerowDel',function(){
                $('#systemPersonnelSettingAppraiseTempletaddHtmlDiv .tra-table-box [role="gridcell"]').trigger('click');
                var rowid = $(this).closest('tr').attr('id');
                project.delRowData(rowid);
                //评分计算
                var rowData = project.getAllData();
                var i=0;
                rowData.forEach(function (val,index) {
                    if(val.score){
                        i+=Number(val.score)
                    }
                })
                $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv #score").val(i.toFixed(2));
            });


            //新增
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#appraiseTempletadd_appraise').on("click", "#appraiseTempletadd_appraise", function () {
                layui.use(['form', 'laytpl'], function () {
                    var form = layui.form,
                        laytpl = layui.laytpl;
                    var getTpl = systemPersonnelSettingAppraiseTempletaddHtml.innerHTML;
                    layer.open({
                        type: 1,
                        title: '新增-评价项目',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['700px', '550px'],
                        skin: 'yourclass',
                        content: getTpl,
                        success: function (layero, index) {
                            project = new $.trasenTable("project-table", {
                                pager:"project-pager",
                                cellEdit:true,
                                colModel: [
                                    {label: '评分内容',name : 'appraiseAppraise',index : 'appraise_appraise',width:450,editable : true, sortable: true},
                                    {label: '分值',name : 'score',index : 'score',width:90,editable : true, sortable: true},
                                    {label: '操作',name : '',index : '',width:65,editable : false, sortable: true,formatter: function (cellvalue, options, rowObject) {
                                            return '<a href="javascript:;" class="softwareReceivablerowDel" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                        }},
                                ],
                                afterSaveCell:function(rowid, cellname, value, iRow, iCol){
                                    if(cellname == 'score'){
                                        //评分计算
                                        var rowData = project.getAllData();
                                        var i=0;
                                        rowData.forEach(function (val,index) {
                                            if(val.score){
                                                i=i+Number(val.score);
                                            }
                                        })
                                        $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv #score").val(i.toFixed(2));
                                    }
                                },
                            });
                            project.refresh();
                        }
                    });
                });
            });

            //修改
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#tableEditor_appraise').on("click", "#tableEditor_appraise", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.appraiseProjectInfoId==null || rowData.appraiseProjectInfoId==undefined ||rowData.appraiseProjectInfoId=='') {
                    layer.msg('请选中需要操作数据');
                    return false;
                }
                $.ajax({
                    type: "post",
                    url:common.url +"/ts-hr/appraise/templet/detailed/info/list",
                    data: {
                        appraiseProjectInfoId:rowData.appraiseProjectInfoId
                    },
                    success: function (res) {
                        if (res.rows.length>0) {
                            layer.msg('该项目已经被模板使用，不可进行编辑');
                            return false;
                        }
                        layui.use(['form', 'laytpl'], function () {
                            var form = layui.form,
                                laytpl = layui.laytpl;
                            var getTpl = systemPersonnelSettingAppraiseTempletaddHtml.innerHTML;
                            layer.open({
                                type: 1,
                                title: '编辑-评价项目',
                                closeBtn: 1,
                                shadeClose: false,
                                area: ['700px', '550px'],
                                skin: 'yourclass',
                                content: getTpl,
                                success: function (layero, index) {
                                    trasen.setNamesVal(layero, rowData);
                                    project = new $.trasenTable("project-table", {
                                        url: common.url + '/ts-hr/appraise/project/detailed/info/list',
                                        postData:{
                                            appraiseProjectInfoId:rowData.appraiseProjectInfoId
                                        },
                                        pager:"project-pager",
                                        cellEdit:true,
                                        sortname: "seq_no",
                                        sortorder:'asc',
                                        colModel: [
                                            {label: 'id',name : 'appraiseProjectDetailedInfoId',index : 'appraiseProjectDetailedInfoId',width:450,editable : true, sortable: true, hidden: true},
                                            {label: '评分内容',name : 'appraiseAppraise',index : 'appraise_appraise',width:450,editable : true, sortable: true},
                                            {label: '分值',name : 'score',index : 'score',width:90,editable : true, sortable: true},
                                            {label: '操作',name : '',index : '',width:65,editable : false, sortable: true,formatter: function (cellvalue, options, rowObject) {
                                                    return '<a href="javascript:;" class="softwareReceivablerowDel" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                                }},
                                        ],
                                        afterSaveCell:function(rowid, cellname, value, iRow, iCol){
                                            if(cellname == 'score'){
                                                //评分计算
                                                var rowData = project.getAllData();
                                                var i=0;
                                                rowData.forEach(function (val,index) {
                                                    if(val.score){
                                                        i=i+Number(val.score);
                                                    }
                                                })
                                                $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv #score").val(i.toFixed(2));
                                            }
                                        },
                                    });
                                    project.refresh();

                                }
                            });
                        });

                    }
                });


            });

            //查看
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#detailButton').on("click", "#detailButton", function () {
                var rowid = $(this).attr('data-rowId');
                var rowData = trasenTable.getRowData(rowid);
                layui.use(['form', 'laytpl'], function () {
                    var form = layui.form,
                        laytpl = layui.laytpl;
                    var getTpl = systemPersonnelSettingAppraiseTempletaddHtml.innerHTML;
                    layer.open({
                        type: 1,
                        title: '查看-评价项目',
                        closeBtn: 1,
                        shadeClose: false,
                        area: ['700px', '550px'],
                        skin: 'yourclass',
                        content: getTpl,
                        success: function (layero, index) {
                            trasen.setNamesVal(layero, rowData);
                            $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv .archivesTabBtn").hide();
                            $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv #addsoftwareReceivable").remove();
                            $("#systemPersonnelSettingAppraiseTempletaddHtmlDiv #updateForm input").attr("disabled",true);
                            project = new $.trasenTable("project-table", {
                                url: common.url + '/ts-hr/appraise/project/detailed/info/list',
                                postData:{
                                    appraiseProjectInfoId:rowData.appraiseProjectInfoId
                                },
                                pager:"project-pager",
                                cellEdit:true,
                                sortname: "seq_no",
                                sortorder:'asc',
                                colModel: [
                                    {label: 'id',name : 'appraiseProjectDetailedInfoId',index : 'appraiseProjectDetailedInfoId',width:450,editable : true, sortable: true, hidden: true},
                                    {label: '评分内容',name : 'appraiseAppraise',index : 'appraise_appraise',width:450,editable : false, sortable: true},
                                    {label: '分值',name : 'score',index : 'score',width:130,editable : false, sortable: true},
                                ],
                            });
                            project.refresh();

                        }
                    });
                });
            });

            //删除
            $("#systemPersonnelSettingAppraiseTemplet").off("click", '#appraiseTempletdeleteBtns_appraise').on("click", "#appraiseTempletdeleteBtns_appraise", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData==null || rowData==undefined ||rowData=='') {
                    layer.msg('请选择一条需要删除的数据!');
                    return false;
                }
                layer.confirm('确定要删除当前选中的数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var _url =  common.url + "/ts-hr/appraise/project/info/delete/"+rowData.appraiseProjectInfoId;
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: _url,
                        success: function (res) {
                            if (res.success) {
                                trasenTable.refresh();
                                layer.closeAll();
                                layer.msg('操作成功');
                            } else {
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                    layer.close(index);
                }, function () {
                });
            });

            // 防止重复提交
            var lock = false;
            // 表单提交
            form.on('submit(save)', function (data) {
                $('.tra-table-box [role="gridcell"]').trigger('click');
                //格式化报销项目
                var e=project.getAllData();
                data.field.appraiseProjectDetailedInfoList=e;
                var _url=null;
                if(data.field.appraiseProjectInfoId){
                    _url=common.url + "/ts-hr/appraise/project/info/update";
                }else{
                    _url=common.url + "/ts-hr/appraise/project/info/save";
                }
                data.field.score=$("#score").val();
                var _data = JSON.stringify(data.field);
                if (!lock) {
                    lock = true;
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url:_url,
                        data: _data,
                        success: function (res) {
                            lock = false;
                            if (res.success) {
                                trasenTable.refresh();
                                layer.closeAll();
                                layer.msg('操作成功');
                            } else {
                                layer.msg(res.message || '操作失败');
                            }
                        },
                        error:function () {
                            lock = false;
                        }
                    });
                } else {
                    layer.msg('处理中...', { shade: [0.8, '#393D49'], time: 60 * 60 * 1000 });
                }
            });
            // end  --------------评价项目事件------------------

        });
    }
})