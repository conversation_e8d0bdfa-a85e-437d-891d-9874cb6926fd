<style type="text/css">
    .mgl5 {
        margin-left: 5px;
    }
    .ui-jqgrid tr.footrow td{
    	border-bottom: 1px solid #ccc;
    }
    #systemProjectBonusManagerBonusSetDiv .lay-input-box{
    	padding-left:130px;
    }
    #systemProjectBonusManagerDetailDiv .lay-input-box{
    	padding-left:130px;
    }
    .staticTable{

    }
    .staticTable .ui-jqgrid{
        position: static;
    }
    .staticTable .ui-jqgrid .ui-jqgrid-view {
        position: static;
    }
    .staticTable .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv{
        position: static!important;
    }
</style>
<div  id="systemProjectBonusManager">
    <div>
        <form id="systemProjectBonusManagerQueryForm" class="layui-form areaButtonBoxL">
            <div class="layui-inline fl">
                <input type="text" class="layui-input" placeholder="项目名称" name="projectName"  search-input="search">
            </div>
            
            <div class="pubBtnBox fl mgl5">
                <button type="button" lay-submit="" lay-filter="systemProjectBonusManagerSearch" search-input="button">搜索</button>
                <button type="button" class="" id="systemProjectBonusManagerScreen">筛选</button>
            </div>
        </form>

		<div class="pubBtnBox areaButtonBoxR">
			<button type="button" class="" id="systemProjectBonusManagerAdd" data-permission="on">新增项目</button>
			<button type="button" class="" id="systemProjectBonusManagerEditor" data-permission="on">修改项目</button>
            <button type="button" class="" id="systemProjectBonusManagerSet" data-permission="on">奖金设置</button>
<!--            <button type="button" class="more" id="areaButtonMore"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></button>&lt;!&ndash; 更多 &ndash;&gt;-->
        </div>
        
<!--        <div class="areaButtonMoreBox">-->
<!--            <div class="areaButtonMore">-->
<!--                <a href="javascript:;" id="exportDevelopment"><i class="fa fa-sign-out signOut" aria-hidden="true"></i> 导出</a>-->
<!--            </div>-->
<!--            <div class="clear"></div>-->
<!--        </div>-->
    </div>
    
    <div id="systemProjectBonusManagerScreeningBox" class="screeningBox">
        <span class="triangle"><i></i></span>
        <div class="infoBox">
            <form class="layui-form" id="systemProjectBonusManagerScreening">
            	<div class="layui-form-item">
                    <div class="shell-screen-box">
                        <span class="screenSelectTit">审批状态</span>
                        <div class="layui-form screenSelect" lay-filter="select" >
                            <select name="isExamine" lay-search>
                                <option value="">--全部--</option>
                                <option value="1">未提交</option>
                                <option value="2">待审核</option>
                                <option value="3">驳回</option>
                                <option value="4">同意</option>
                            </select>
                        </div>
                    </div>
                    
		            <div class="shell-screen-box">
                        <span class="screenSelectTit">项目经理</span>
                        <div class="layui-form screenSelect" lay-filter="select" id="systemProjectBonusManagerQuerypmNameChooseBox">
                                <select name="pmCode" lay-search>
                                <option value="">--请选择人员--</option>
                                </select>
                        </div>
                    </div>
		            
					<div class="shell-screen-box">
                        <span class="screenSelectTit">项目类型</span>
                        <div class="layui-form screenSelect" lay-filter="select">
                            <select name="bonusType" lay-search>
                                <option value="">全部</option>
                                <option value="1">研发类</option>
                                <option value="2">运维类</option>
                                <option value="3">实施类</option>
                            </select>
                        </div>
                    </div>
					
		            <div class="shell-screen-box">
                        <span class="screenSelectTit">项目状态</span>
                        <div class="layui-form screenSelect" lay-filter="select">
                            <select name="status" lay-search>
                                <option value="">全部</option>
                                    <option value="1">未立项</option>
                                <option value="2">进行中</option>
                                <option value="3">已结项</option>
                            </select>
                        </div>
                    </div>
		            
		            <div class="shell-screen-btn">
                        <div class="pubBtnBox">
                            <button type="button" class="" lay-submit="" lay-filter="systemProjectBonusManagerScreeningSub">查询</button>
                            <button type="button" class="" id="systemProjectBonusManagerScreenCancel">取消</button>
                            <button type="button" class="" id="systemProjectBonusManagerScreenCRest">重置</button>
                        </div>
                    </div>
		            
            	</div>
            </form>
        </div>
    </div>
    
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table-systemProjectBonusManagerTable"></table>
            <!-- 分页 -->
            <div id="grid-pager-systemProjectBonusManagerPager"></div>
        </div>
    </div>
</div>

<script type="text/html" id="systemProjectBonusManagerAddFormHtml">
<div class="layui-tab-content" id="systemProjectBonusManagerAddFormDiv">
    <form class="layui-form" id="systemProjectBonusManagerAddForm">
        <input type="hidden" name="projectBonusBaseId" value="">
		
		<div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span> 合同名称：</label>
                <div class="layui-input-inline">
                    <input id="contractName" name="contractName" type="hidden">
                    <input id="contractNo" name="contractNo" type="hidden">
                    <select id="contractId" name="contractId" lay-search>
                        <option id="contractIdName"></option>
                    </select>
                </div>
            </div>
        </div>
		
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>项目名称：</label>
                <div class="layui-input-inline">
                    <input type="tex" name="projectName" lay-verify="required" autocomplete="off" class="layui-input layui-disabled" placeholder="请输入产品(项目)名称">
                </div>
            </div>
        </div>
        
        <div class="layui-col-xs6">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>项目编号：</label>
                <div class="layui-input-inline">
                    <input type="text" name="projectCode" id="projectCode" lay-verify="required" class="layui-input layui-disabled" placeholder="项目编号">
                </div>
            </div>
        </div>
        
        <div class="layui-col-xs6  layui-form" lay-filter="selectBonusTypeForm">
            <div class="layui-form-item">
                <label class="layui-form-label">当前状态：</label>
                <div class="layui-input-inline">
                    <select name="status" lay-search>
                        <option value="1">未立项</option>
                        <option value="2">进行中</option>
                        <option value="3">已结项</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="layui-form-item" style="display: none;">
            <label class="layui-form-label">项目类型：</label>
            <div class="layui-input-inline contractArea" id="projectType">
                <input type="radio" name="projectType" value="1" title="新技术研发" checked="">
                <input type="radio" name="projectType" value="2" title="新产品(项目)研发">
                <input type="radio" name="projectType" value="3" title="产品升级改造">
                <input type="radio" name="projectType" value="4" title="合同项目">
            </div>
        </div>


        <div class="layui-col-md6">
            <label class="lay-label-title"><span>*</span>项目经理</label>
            <div class="lay-input-box" id="pmName" style="width:202px">
                 
            </div>
        </div>


        <div class="layui-col-xs6">
            <div class="layui-form-item" >
                <label class="layui-form-label"><span class="required">*</span> 立项时间：</label>
                <div class="layui-input-inline">
                    <input type="tex" name="entryDate" lay-verify="required" autocomplete="off" class="layui-input layDate layui-disabled">
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <label class="lay-label-title"><span>*</span>所属部门:</label>
            <div class="lay-input-box" id="deptCodeChooseBox" style="width:202px">
                 <input type="text" name="deptName" class="layui-input" id="deptName" placeholder="请选择所属部门">
                <input type="hidden" name="deptCode" class="layui-input" id="deptCode">
            </div>
        </div>

        <div class="layui-col-md6 layui-form" lay-filter="selectBonusTypeForm">
            <label class="lay-label-title"><span>* </span>奖金核算类型:</label>
            <div class="lay-input-box" style="width:202px">
                <select name="bonusType" class="applyselect" lay-search>
                    <option value="1">研发类</option>
                    <option value="2">运维类</option>
                    <option value="3">实施类</option>
                </select>
            </div>
        </div>
        
        <div class="layui-col-xs6" style="display: none;">
            <label class="layui-form-label">是否核算奖金：</label>
            <div class="layui-input-inline" id="isAccountingBonus">
                <input type="radio" name="isAccountingBonus" value="1" title="是" checked="">
                <input type="radio" name="isAccountingBonus" value="2" title="否">
            </div>
        </div>

        <!--<div class="layui-form-item"  >
            <label class="layui-form-label">相关人员：</label>
            <div class="layui-form layui-input-inline" lay-filter="select" id="refUserNameListBox">
                <select name="refUserNameList" id="refUserNameList" multiple="multiple">
                        <option id="initrefUserNameList">请输入姓名查询</option>
                </select>
            </div>
        </div>-->

        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layer_btn" lay-submit="" lay-filter="systemProjectBonusManagerFormSaveProjectInfo">保存</button>
            <a href="javascript:;" class="layer_btn layer_back" id="systemProjectBonusManagerCancel">关闭</a>
        </div>
    </form>
</div>
</script>

<!-- **********************************************奖金设置************************************************************* -->

<!-- 设置奖金 -->
<script type="text/html" id="systemProjectBonusManagerBonusSetHtml">
<div class="layui-tab-content" id="systemProjectBonusManagerBonusSetDiv">
    <input type="hidden" name="id" id="id" value=""> <!-- 回款需要的id 7E60228489764AF391AC0C08102EC8B4 -->
    <input type="hidden" name="projectBonusBaseId" value="">
    <input type="hidden" name="developStageStatus" value="">
    <input type="hidden" name="projectBonusInfoId" id="projectBonusInfoId">
    <div class="layui-tab archivesTab layui-tab-oa-nav" lay-filter="systemProjectBonusManagerNcomeLaytab">
        <ul class="layui-tab-title">
            <li class="layui-this" title="项目任务">项目任务</li>
            <li title="任务模块">任务模块</li>
        </ul>
        <div class="layui-tab-content archivesTabBtnBox">
            <div class="layui-tab-item layui-show ">
				<!-- 项目任务 -->
				<form class="layui-form" id="updateForm" >
		            <input type="hidden" name="projectBonusInfoId">
		            <input type="hidden" name="projectBonusBaseId">
		            <input type="hidden" name="bonusType">
    				<input type="hidden" name="deptBonus">
		            <input type="hidden" name="id">
		            <input type="hidden" name="taskId" id="taskId">
		
		            <div class="layui-col-md4">
		                <label class="lay-label-title"><span>*</span> 项目预算</label>
                        <div class="lay-input-box">
                            <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                            <input type="text" name="" class="layui-input layInput layRmbInput"
                                   id="projectBudgetbasicsShow"
                                   onclick="$(this).hide();$('#projectBudgetbasics').show();setTimeout(function(){$('#projectBudgetbasics').focus()});"
                                   value="0.00" lay-verify="required">
                            <input type="number" name="projectBudget"
                                   onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBudgetbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                   class="layui-input layInput layRmbInput none" id="projectBudgetbasics"
                                   lay-verify="" placeholder="项目预算金额" value="0">
                        </div>
		            </div>
		            
		            <div class="layui-col-md4">
		                <label class="lay-label-title"><span>*</span> 奖金系数</label>
                        <div class="lay-input-box">
                            <input type="number" name="bonusCoefficient" id="bonusCoefficient" class="layui-input layInput focusEvent" lay-verify="required" placeholder="请输入奖金系数(例：0.15)" >
                            <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                        </div>
		            </div>
		            
		            <div class="layui-col-xs4 difficultyType">
		                <label class="layui-form-label"><span style="color:red">*</span> 难度系数</label>
                        <div class="lay-input-box">
		                    <select name="difficultyType" id="difficultyType" lay-filter="difficultyType" lay-verify="required" lay-search>
		                    	<option value="">请选择</option>
		                        <option value="1">简单-0.8</option>
		                        <option value="2">一般-1.0</option>
		                        <option value="3">较复杂-1.2</option>
		                        <option value="4">复杂-1.5</option>
		                    </select>
		                </div>
		            </div>
		            
		            <div class="layui-col-xs4 gradeType">
		                <label class="layui-form-label"><span style="color:red">*</span> 评级系数</label>
                        <div class="lay-input-box">
		                    <select name="gradeType" id="gradeType" lay-filter="gradeType" lay-verify="required" lay-search>
		                    	<option value="">请选择</option>
		                        <option value="1">优秀-1.5</option>
		                        <option value="2">良+-1.0</option>
		                        <option value="3">良-0.9</option>
		                        <option value="4">一般-0.8</option>
		                        <option value="5">合格-0.7</option>
		                        <option value="6">不合格-0</option>
		                    </select>
		                </div>
		            </div>
		            
		            <div class="layui-col-md4">
		                <label class="lay-label-title"><span>*</span> 部门占比(%)</label>
                        <div class="lay-input-box">
                            <input type="number" name="factPercent" id="factPercent" class="layui-input layInput focusEvent" placeholder="请输入部门占比(例：20)" lay-verify="required">
                            <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                        </div>
		            </div>
		            
		            <div class="layui-col-xs4 isHighLines">
		                <label class="layui-form-label"><span style="color:red">*</span> 是否上线/验收</label>
                        <div class="lay-input-box">
		                    <select name="isHighLines" id="isHighLines" lay-filter="isHighLines" lay-verify="required" lay-search>
		                    	<option value="">请选择</option>
		                        <option value="1">是</option>
		                        <option value="2">否</option>
		                    </select>
		                </div>
		            </div>
		            
		            <div class="layui-col-md4 travelAmount">
		                <label class="lay-label-title"> 差旅费</label>
		                <div class="lay-input-box">
                            <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                            <input type="text" name="" class="layui-input layInput layRmbInput" 
                                   id="travelAmountbasicsShow"
                                   onclick="$(this).hide();$('#travelAmountbasics').show();setTimeout(function(){$('#travelAmountbasics').focus()});"
                                   value="0.00" >
                            <input type="number" name="travelAmount" 
                                   onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#travelAmountbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                   class="layui-input layInput layRmbInput none" id="travelAmountbasics"
                                   lay-verify="" placeholder="项目奖金" value="0">
                        </div>
		            </div>
		            
		            <div class="layui-col-md4">
		                <label class="lay-label-title"> 项目奖金</label>
		                <div class="lay-input-box">
                            <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                            <input type="text" name="" class="layui-input layInput layRmbInput layui-disabled" disabled
                                   id="projectBonusbasicsShow"
                                   onfocus="$(this).hide();$('#projectBonusbasics').show();setTimeout(function(){$('#projectBonusbasics').focus()});"
                                   value="0.00" >
                            <input type="number" name="projectBonus" disabled
                                   onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                   class="layui-input layInput layRmbInput none layui-disabled" id="projectBonusbasics"
                                   lay-verify="" placeholder="项目奖金" value="0">
                        </div>
		            </div>
		            
		            <div class="layui-col-xs12" style="margin-top: 50px;" id="taskHtml">
		                <div class="lay-table-title" style="margin-top: -40px;">
		                    <span class="title">任务明细</span>
	                    	<span class="price" id="projectBonusPic" data-pic=""></span>
		                    <span class="fr">
		                        <button type="button" style="height: 28px;" class="tra-button addqici" data-table="projectTaskTable" id="addprojectTaskTable"><i class="fa fa-plus-circle" aria-hidden="true"></i> 添加明细</button>
		                    </span>
		                </div>
		                <div class="table-box tra-table-box" style="height: 350px;">
		                    <!-- 表单 -->
		                    <table id="grid-table-bonusManagerTaskTable"></table>
		                </div>
		            </div>
		            <button type="button" class="layer_btn none" id="systemProjectBonusManagerBasicinfo" lay-submit="" lay-filter="systemProjectBonusManagerBasicinfo">提交</button>
		        </form>
            </div>
            
            <div class="layui-tab-item">
                <!-- 模块信息 -->
                <div id="softwareReceivablePicHtml">
                    <div class="lay-table-title">
                    	<input type="hidden" name="projectTaskInfoId">
                        <span class="title">模块明细</span>
                        <span class="priceT">任务权重：</span>
                        <span class="price" id="moduleWeightPic" data-pic=""></span>
                        <span>(项目权重 = 任务权重 X 模块权重)</span>
                        <span class="fr">
                            <button type="button" class="tra-button addqici" data-table="projectModulTable" id="addprojectModulTable"><i class="fa fa-plus-circle" aria-hidden="true"></i> 添加模块</button>
                        </span>
                    </div>
                    <div class="table-box tra-table-box">
                        <!-- 表单 -->
                        <table id="grid-table-bonusManagerModuleTable"></table>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clear"></div>
    <div class="layer-btn archivesTabBtn">
        <a href="javascript:;" class="layer_btn layer_back" id="close">关闭</a>
        <button type="button" class="layer_btn layer_btn_bor" id="systemProjectBonusManagerAddSave" lay-submit="" lay-filter="systemProjectBonusManagerAddSave">保存</button>
        <!--<button type="button" class="layer_btn" id="addSub" lay-submit="" lay-filter="addSub">提交</button>-->
    </div>
</div>
</script>

<!-- 查看详情 -->
<script type="text/html" id="systemProjectBonusManagerDetailHtml">
    <div class="shell-layer-content-box" id="systemProjectBonusManagerDetailDiv" style="bottom: 0;">
        <form id="examineForm" class="layui-form">
            <input type="hidden" name="id">
            <input type="hidden" name="taskId">
            <input type="hidden" name="projectBonusBaseId" id="projectBonusBaseId">

            <div class="layui-tab-content ">
                <div class="layui-row ">
                    <div class="layui-col-md12 archivesTab travelApproveBox ">
                            <div class="layui-content-box layui-row" style="overflow: hidden;overflow-y: auto;">
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 项目预算</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="projectBudgetbasicsShow"
                                               onfocus="$(this).hide();$('#projectBudgetbasics').show();setTimeout(function(){$('#projectBudgetbasics').focus()});"
                                               value="0.00" lay-verify="required">
                                        <input type="number" name="projectBudget" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBudgetbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="projectBudgetbasics"
                                               lay-verify="" placeholder="项目预算金额" value="0">
                                    </div>
                                </div>

                                <div class="layui-col-xs4 travelAmount">
                                    <label class="lay-label-title"> 差旅费</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled"
                                               id="travelAmountbasicsShow" disabled
                                               onfocus="$(this).hide();$('#travelAmountbasics').show();setTimeout(function(){$('#travelAmountbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="travelAmount" disabled
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#travelAmountbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="travelAmountbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 奖金系数</label>
                                    <div class="lay-input-box">
                                        <input type="number" name="bonusCoefficient"
                                               class="layui-input layInput" layui-disabled readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4 difficultyType">
                                    <label class="layui-form-label"> 难度系数</label>
                                    <div class="lay-input-box">
                                        <select name="difficultyType" id="difficultyType" lay-filter="difficultyType"
                                                disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">简单-0.8</option>
                                            <option value="2">一般-1.0</option>
                                            <option value="3">较复杂-1.2</option>
                                            <option value="4">复杂-1.5</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-col-xs4 gradeType">
                                    <label class="layui-form-label"> 评级系数</label>
                                    <div class="lay-input-box">
                                        <select name="gradeType" id="gradeType" lay-filter="gradeType" disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">优秀-1.5</option>
                                            <option value="2">良+-1.0</option>
                                            <option value="3">良-0.9</option>
                                            <option value="4">一般-0.8</option>
                                            <option value="5">合格-0.7</option>
                                            <option value="6">不合格-0</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="layui-col-xs4" id="scheduleTypeDiv" style="display: none;">
		                            <label class="layui-form-label"><span style="color:red;display: none;">*</span> 进度系数</label>
		                            <div class="lay-input-box">
		                                <select name="scheduleType" id="scheduleType" lay-filter="scheduleType" disabled lay-search>
		                                    <option value="">请选择</option>
		                                    <option value="1">提前完成-1.2</option>
		                                    <option value="2">按时完成-1.0</option>
		                                    <option value="3">延期一周至两周(含)-0.8</option>
		                                    <option value="4">延期两周至四周(含)-0.6</option>
		                                    <option value="5">延期四周至八周(含)-0.4</option>
		                                    <option value="6">延后八周以上-0.2</option>
		                                </select>
		                            </div>
		                        </div>
                                
                                <div class="layui-col-xs4" id="delayBonusDiv" style="display: none;">
		                            <label class="lay-label-title"> 进度奖金</label>
		                            <div class="lay-input-box">
		                                <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
		                                <input type="text" name="" class="layui-input layInput layRmbInput layui-disabled" disabled
		                                       id="delayBonusbasicsShow"
		                                       onfocus="$(this).hide();$('#delayBonusbasics').show();setTimeout(function(){$('#delayBonusbasics').focus()});"
		                                       value="0.00">
		                                <input type="number" name="delayBonus" disabled
		                                       onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#delayBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
		                                       class="layui-input layInput layRmbInput none layui-disabled"
		                                       id="delayBonusbasics"
		                                       lay-verify="" placeholder="进度奖金" value="0">
		                            </div>
		                        </div>

                                <div class="layui-col-xs4 isHighLines">
                                    <label class="layui-form-label"> 是否上线/验收</label>
                                    <div class="lay-input-box">
                                        <select name="isHighLines" id="isHighLines" lay-filter="isHighLines" disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">部门占比(%)</label>
                                    <div class="lay-input-box">
                                        <input type="number" name="factPercent"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 项目奖金</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="projectBonusbasicsShow"
                                               onfocus="$(this).hide();$('#projectBonusbasics').show();setTimeout(function(){$('#projectBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="projectBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="projectBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>


                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">研发进度(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="developSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">本期申请比例(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="thisSchedule" id="thisSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">申请总比例(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="factSchedule" id="factSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">已申请奖金比(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="projectSchedule" id="projectSchedule"
                                               class="layui-input layInput layui-disabled" readonly>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">研发质量(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="developQuality" id="developQuality"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">历史结余质量奖</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="balanceQualityBonusbasicsShow"
                                               onfocus="$(this).hide();$('#balanceQualityBonusbasics').show();setTimeout(function(){$('#balanceQualityBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="balanceQualityBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#balanceQualityBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="balanceQualityBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">本期结余质量奖</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="thisBalanceQualityBonusbasicsShow"
                                               onfocus="$(this).hide();$('#thisBalanceQualityBonusbasics').show();setTimeout(function(){$('#thisBalanceQualityBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="thisBalanceQualityBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#thisBalanceQualityBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="thisBalanceQualityBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>
                                <div class="layui-col-xs12" style="margin-top: 50px;">
                                    <div class="lay-table-title" style="margin-top: -40px;width: 98%;padding: 0;">
                                        <span class="title">明细信息</span>
                                        <span class="price" data-pic="">项目奖金总额：<span id="projectBonusPic"></span>￥  本次申请项目奖金：<span
                                            id="thisBonus"></span>￥  （分配金额=项目奖金*（奖金申请比-已申请奖金比）*研发质量*个人权重）</span>
                                        <div class="fr" id="detailPreview" data-pic=""
                                             style="position: relative;z-index: 999;">
                                            <span
                                                style="display: inline-block;width: 45px;text-align: center;">预览</span>
                                        </div>
                                    </div>
                                    <div class="table-box tra-table-box"
                                         style="height: 364px;width: 98%;position: relative;overflow: hidden;margin-bottom: 0;">
                                        <!-- 表单 -->
                                        <table id="grid-table-bonusManagerExamineDetailTable"></table>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
            <div class="layer-btn archivesTabBtn">
                <a href="javascript:;" class="layer_btn layer_back" id="systemProjectBonusManagerCancel">返回</a>
            </div>

        </form>
    </div>
</script>

<!-- 预览 -->
<script type="text/html" id="bonusManagerPreviewFormHtml">
    <div class="layui-tab-content"
         style="position: absolute;width: 600px;;border: 1px solid #ccc;border-radius:8px;padding: 0px;top: -80px;left: -540px;background: #fff;display: none;">
        <div style="position: relative;height: 100%;">
            <!-- 表单 -->
            <div
                style="background:#f2f2f2;height: 30px;line-height: 30px;padding-left: 5px;background: -webkit-linear-gradient(top,#f7fbff,#f2f2f2); background: -o-linear-gradient(top,#f7fbff,#f2f2f2); background: -moz-linear-gradient(top,#f7fbff,#f2f2f2);">
                人员奖金详情
            </div>
            <div style="position: relative;margin-left: -2px" class="staticTable">
                <table id="grid-table-bonusManagerPreviewTable" style="top: 30px;height: 100%;"></table>
            </div>

        </div>
    </div>
</script>






