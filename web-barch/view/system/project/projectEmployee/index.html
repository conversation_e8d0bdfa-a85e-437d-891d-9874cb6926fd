<style type="text/css">
    .mgl5 {
        margin-left: 5px;
    }
</style>
<div class="layui-row">
	<div class="button-area">
		<form id="queryForm" class="layui-form">
			<input type="hidden" name="projectEmployeeId" autocomplete="off" class="layui-input edi-layui-input">
			<div class="fl">
				<div class="layui-inline fl">
					<input type="text" name="employeeName" placeholder="请输入关键字查询" autocomplete="off" class="layui-input edi-layui-input" search-input="search">
				</div>
			</div>

			<button type="button" class="layui-btn mgl5 layui-btn-normal edi-button edi-button-blue" lay-submit="" lay-filter="search" search-input="button">搜索</button>

		</form>
	</div>

	<div class="trasen-con-box">
		<div class="employeeGroupTree">
			<ul id="employeeGroupTree" class="ztree">
			</ul>
			<div class="pubBtnBox">
				<button type="button" class="" id="addEmployeeGroup">新增</button>
				<button type="button" class="" id="editEmployeeGroup">编辑</button>
				<button type="button" class="" id="deleteEmployeeGroup">删除</button>
			</div>
		</div>
		<div class="transen-con-view-box">
			<div class="table-box">
				<!-- 表单 -->
				<table id="employeegroup-grid-table"></table>
				<!-- 分页 -->
				<div id="employeegroup-grid-pager"></div>
		</div>
	</div>

<!-- 新增用户组信息 -->
<script type="text/html" id="addEmployeeGroupHtml">
<div class="layui-tab-content">
    <form class="layui-form" id="addEmployeeGroupForm">
        <div class="layui-col-md11">

			<input type="hidden" id="projectMessageId" name="projectMessageId">
			<input type="hidden" name="employeeCodes" id="employeeCodes">
			
			<div class="layui-col-md6">
                <label class="lay-label-title"><span>*</span>项目名称</label>
				<div class="lay-input-box" id="projectDiv" >
				</div>
            </div>
            <div class="layui-col-md6">
                <label class="lay-label-title"><span id="projectSpan">*</span>经理</label>
                <div class="lay-input-box" id="leaderCodeBox" >
				</div>
            </div>
            <div class="layui-col-md12">
                <label class="lay-label-title">选择用户</label>
                <div class="lay-input-box" id="transferBox">
                </div>
            </div>
        </div>
        <div class="layer-btn archivesTabBtn">
            <button type="button" class="layer_btn" id="formEmployeeGroupSave" lay-submit="" lay-filter="formEmployeeGroupSave">保存</button>
            <button type="button" class="layer_btn layer_back" id="close">关闭</button>
        </div>
    </form>
</div>
</script>