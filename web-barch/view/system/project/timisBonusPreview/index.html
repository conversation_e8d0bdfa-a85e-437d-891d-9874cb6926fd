<style>
    #bonusPreviewQueryForm .layui-unselect.layui-form-select{
        display: inline-block;
    }
</style>
<div  id="systemProjectTimisBonusPreview">
    <div>
        <form id="bonusPreviewQueryForm" class="layui-form areaButtonBoxL">
            <input type="hidden" name="examineStatus" value="1">
            <div class="layui-inline fl">
                <label class="layui-inline" style="vertical-align: baseline">项目：</label>
                <input type="text" class="layui-input" placeholder="项目名称" style="display: inline-block" name="condition">
            </div>
            <div class="layui-inline fl mrleft10">
                <label class="layui-inline" style="vertical-align: baseline">人员：</label>
                <input type="text" class="layui-input" placeholder="人员名称" style="display: inline-block" name="personCode">
            </div>
            <div class="layui-inline fl mrleft10">
                <label class="layui-inline" style="vertical-align: baseline">查询类型：</label>
                <select name="queryType" lay-search>
                    <option value="1" >人员</option>
                    <option value="2" selected>项目</option>
                </select>
            </div>
            <div class="layui-inline fl mrleft10">
                <label class="layui-inline" style="vertical-align: baseline">奖金期数：</label>
                <select name="developStageStatus" lay-search>
                    <option value="" selected>请选择</option>
                    <option value="1" >第一期</option>
                    <option value="2">第二期</option>
                    <option value="3">第三期</option>
                    <option value="4">第四期</option>
                </select>
            </div>

            <div class="pubBtnBox fl mrleft10">
                <button type="button" class="" id="bonusPreviewSearch" >搜索</button>

            </div>
        </form>

        <div class="screenPeo" id="runstatusBonusPreviewDiv">
            <button class="active" data-value="1">全部</button>
            <button  data-value="2">审批中</button>
            <button  data-value="3">已审批</button>
        </div>

    </div>



    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table-bonusPreview"></table>
            <!-- 分页 -->
            <div id="grid-pager-bonusPreview"></div>
        </div>
    </div>
</div>

