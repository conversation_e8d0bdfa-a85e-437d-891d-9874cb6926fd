"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen', 'element'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                element = layui.element,
                trasen = layui.trasen;

            var trasenTable;

            form.render();

            // new $.SelectPullDown('#fiscalExpendReprotParams_project', {
            //     url: common.url + "/ts-cp/baseProject/list",
            //     datatype: 'post',  // 请求方式
            //     valName: 'projectBonusBaseId', 
            //     textName: 'projectName',
            //     inpValId: 'projectBonusBaseId',
            //     inpTextId: 'projectName',  
            //     inpValName: 'projectBonusBaseId', 
            //     inpTextName: 'projectName', 
            //     callback: function (res) {
            //         //refreshTable();
            //     }
            // });

            var developStageStatusArr = ["", "第一期", "第二期", "第三期", "第四期", "第五期", "第六期", "第七期", "第八期", "第九期", "第十期"];

            trasenTable = new $.trasenTable("grid-table-bonusPreview", {
                url: common.url + '/ts-cp/moduleSchedule/employeeBonusPreview',
                shrinkToFit:true,
                pager: 'grid-pager-bonusPreview',
                colModel: [
                	{ label: 'ID', name: 'ID', index: 'ID', width: "auto", align: "center", editable: false, hidden: true,key: true },
                    {label: '人员名称', name: 'personName', index: 'create_date', width: 145, editable: false,sortable:false},
                    {label: '人员部门', name: 'deptName', index: 'contract_no', width: 145, editable: false,sortable:false},
                    {label: '项目名称', name: 'projectName', index: 'contract_no', width: 145, editable: false,sortable:false},
                    {
                        label: '期数', name: 'developStageStatus', width: 145, editable: false,sortable:false,
                        formatter: function (cell, opt, row) {
                            return developStageStatusArr[cell||0]
                        }
                    },
                    {
                        label: '个人项目奖',
                        name: 'projectBonusTotal',
                        index: 'contract_no',
                        width: 145,
                        editable: false,
                        formatter: "number",
                        align: "right",
                        sortable:false,
                        formatoptions: {
                            decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2, prefix: ""
                        }
                    },

                ],
                postData: {
                    examineStatus: "1",
                    queryType: "2"
                },
                buidQueryParams: function () {
                    var search = $("#bonusPreviewQueryForm").serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }

                    return data;
                }
            });

            function refreshTable() {
                trasenTable.refresh();
            }

            //表格切换
            $("#runstatusBonusPreviewDiv button").on("click", function () {
                $(this).addClass("active").siblings().removeClass("active");
                var state = $(this).attr('data-value');
                $('#bonusPreviewQueryForm input[name="examineStatus"]').val(state)
                refreshTable();
            });

            //查询
            $("body").off("click", "#bonusPreviewSearch").on("click", "#bonusPreviewSearch", function () {
                refreshTable();
            })

        })
    }
})
