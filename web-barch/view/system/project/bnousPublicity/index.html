<style type="text/css">
    .mgl5 {
        margin-left: 5px;
    }
    .ui-jqgrid tr.footrow td{
    	border-bottom: 1px solid #ccc;
    }
    #bonusPublicityDetailFormDiv .lay-input-box{
    	padding-left:130px;
    }
    .staticTable{

    }
    .staticTable .ui-jqgrid{
        position: static;
    }
    .staticTable .ui-jqgrid .ui-jqgrid-view {
        position: static;
    }
    .staticTable .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv{
        position: static!important;
    }
</style>
<div  id="systemProjectBonusPublicity">
    <div>
        <form id="bonusPublicityQueryForm" class="layui-form areaButtonBoxL">
            <div class="layui-inline fl">
                <input type="text" class="layui-input" placeholder="项目名称" name="projectName"  search-input="search">
            </div>
            
            <div class="pubBtnBox fl mgl5">
                <button type="button" class="" id="bonusPublicitySearch" lay-submit="" lay-filter="bonusPublicitySearch" search-input="button">搜索</button>
                
            </div>
        </form>

		<div class="pubBtnBox areaButtonBoxR">
			<button type="button" class="" id="bonusPublicityExport" data-permission="on">导出</button>
        </div>
    </div>
    
    
    
    <div class="trasen-con-box">
        <div class="table-box">
            <!-- 表单 -->
            <table id="grid-table-bonusPublicityTable"></table>
            <!-- 分页 -->
            <div id="grid-pager-bonusPublicityPager"></div>
        </div>
    </div>
</div>

<!-- 查看详情 -->
<script type="text/html" id="bonusPublicityDetailFormHtml">
    <div class="shell-layer-content-box" id="bonusPublicityDetailFormDiv" style="bottom: 0;">
        <form id="examineForm" class="layui-form">
            <input type="hidden" name="id">
            <input type="hidden" name="taskId">
            <input type="hidden" name="projectBonusBaseId" id="projectBonusBaseId">

            <div class="layui-tab-content ">
                <div class="layui-row ">
                    <div class="layui-col-md12 archivesTab travelApproveBox ">
                            <div class="layui-content-box layui-row" style="overflow: hidden;overflow-y: auto;">
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 项目预算</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="projectBudgetbasicsShow"
                                               onfocus="$(this).hide();$('#projectBudgetbasics').show();setTimeout(function(){$('#projectBudgetbasics').focus()});"
                                               value="0.00" lay-verify="required">
                                        <input type="number" name="projectBudget" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBudgetbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="projectBudgetbasics"
                                               lay-verify="" placeholder="项目预算金额" value="0">
                                    </div>
                                </div>

                                <div class="layui-col-xs4 travelAmount">
                                    <label class="lay-label-title"> 差旅费</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled"
                                               id="travelAmountbasicsShow" disabled
                                               onfocus="$(this).hide();$('#travelAmountbasics').show();setTimeout(function(){$('#travelAmountbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="travelAmount" disabled
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#travelAmountbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="travelAmountbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 奖金系数</label>
                                    <div class="lay-input-box">
                                        <input type="number" name="bonusCoefficient"
                                               class="layui-input layInput" layui-disabled readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4 difficultyType">
                                    <label class="layui-form-label"> 难度系数</label>
                                    <div class="lay-input-box">
                                        <select name="difficultyType" id="difficultyType" lay-filter="difficultyType"
                                                disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">简单-0.8</option>
                                            <option value="2">一般-1.0</option>
                                            <option value="3">较复杂-1.2</option>
                                            <option value="4">复杂-1.5</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-col-xs4 gradeType">
                                    <label class="layui-form-label"> 评级系数</label>
                                    <div class="lay-input-box">
                                        <select name="gradeType" id="gradeType" lay-filter="gradeType" disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">优秀-1.5</option>
                                            <option value="2">良+-1.0</option>
                                            <option value="3">良-0.9</option>
                                            <option value="4">一般-0.8</option>
                                            <option value="5">合格-0.7</option>
                                            <option value="6">不合格-0</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="layui-col-xs4" id="scheduleTypeDiv" style="display: none;">
		                            <label class="layui-form-label"><span style="color:red;display: none;">*</span> 进度系数</label>
		                            <div class="lay-input-box">
		                                <select name="scheduleType" id="scheduleType" lay-filter="scheduleType" lay-search disabled>
		                                    <option value="">请选择</option>
		                                    <option value="1">提前完成-1.2</option>
		                                    <option value="2">按时完成-1.0</option>
		                                    <option value="3">延期一周至两周(含)-0.8</option>
		                                    <option value="4">延期两周至四周(含)-0.6</option>
		                                    <option value="5">延期四周至八周(含)-0.4</option>
		                                    <option value="6">延后八周以上-0.2</option>
		                                </select>
		                            </div>
		                        </div>
                                
                                <div class="layui-col-xs4" id="delayBonusDiv" style="display: none;">
		                            <label class="lay-label-title"> 进度奖金</label>
		                            <div class="lay-input-box">
		                                <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
		                                <input type="text" name="" class="layui-input layInput layRmbInput layui-disabled" disabled
		                                       id="delayBonusbasicsShow"
		                                       onfocus="$(this).hide();$('#delayBonusbasics').show();setTimeout(function(){$('#delayBonusbasics').focus()});"
		                                       value="0.00">
		                                <input type="number" name="delayBonus" disabled
		                                       onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#delayBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
		                                       class="layui-input layInput layRmbInput none layui-disabled"
		                                       id="delayBonusbasics"
		                                       lay-verify="" placeholder="进度奖金" value="0">
		                            </div>
		                        </div>

                                <div class="layui-col-xs4 isHighLines">
                                    <label class="layui-form-label"> 是否上线/验收</label>
                                    <div class="lay-input-box">
                                        <select name="isHighLines" id="isHighLines" lay-filter="isHighLines" disabled lay-search>
                                            <option value="">请选择</option>
                                            <option value="1">是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">部门占比(%)</label>
                                    <div class="lay-input-box">
                                        <input type="number" name="factPercent"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title"> 项目奖金</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="projectBonusbasicsShow"
                                               onfocus="$(this).hide();$('#projectBonusbasics').show();setTimeout(function(){$('#projectBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="projectBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#projectBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="projectBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>


                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">研发进度(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="developSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">本期申请比例(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="thisSchedule" id="thisSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">申请总比例(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="factSchedule" id="factSchedule"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">已申请奖金比(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="projectSchedule" id="projectSchedule"
                                               class="layui-input layInput layui-disabled" readonly>
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">研发质量(%)</label>
                                    <div class="lay-input-box">
                                        <input type="text" name="developQuality" id="developQuality"
                                               class="layui-input layInput layui-disabled" readOnly>
                                        <i class="fa fa-times-circle clearIconInput" aria-hidden="true"></i>
                                    </div>
                                </div>

                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">历史结余质量奖</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="balanceQualityBonusbasicsShow"
                                               onfocus="$(this).hide();$('#balanceQualityBonusbasics').show();setTimeout(function(){$('#balanceQualityBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="balanceQualityBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#balanceQualityBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="balanceQualityBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>
                                <div class="layui-col-xs4">
                                    <label class="lay-label-title">本期结余质量奖</label>
                                    <div class="lay-input-box">
                                        <i class="fa fa-jpy layRmb" aria-hidden="true"></i>
                                        <input type="text" name=""
                                               class="layui-input layInput layRmbInput layui-disabled" readOnly
                                               id="thisBalanceQualityBonusbasicsShow"
                                               onfocus="$(this).hide();$('#thisBalanceQualityBonusbasics').show();setTimeout(function(){$('#thisBalanceQualityBonusbasics').focus()});"
                                               value="0.00">
                                        <input type="number" name="thisBalanceQualityBonus" readOnly
                                               onblur="$(this).hide();$(this).val(parseFloat($(this).val()));if($(this).val() == ''){$(this).val(0);}$('#thisBalanceQualityBonusbasicsShow').show().val($.formMoney(parseFloat($(this).val())));"
                                               class="layui-input layInput layRmbInput none layui-disabled"
                                               id="thisBalanceQualityBonusbasics"
                                               lay-verify="" placeholder="项目奖金" value="0">
                                    </div>
                                </div>
                                <div class="layui-col-xs12" style="margin-top: 50px;">
                                    <div class="lay-table-title" style="margin-top: -40px;width: 98%;padding: 0;">
                                        <span class="title">明细信息</span>
                                        <span class="price" data-pic="">项目奖金总额：<span id="projectBonusPic"></span>￥  本次申请项目奖金：<span
                                            id="thisBonus"></span>￥  （分配金额=项目奖金*（奖金申请比-已申请奖金比）*研发质量*个人权重）</span>
                                        <div class="fr" id="detailPreview" data-pic=""
                                             style="position: relative;z-index: 999;">
                                            <span
                                                style="display: inline-block;width: 45px;text-align: center;">预览</span>
                                        </div>
                                    </div>
                                    <div class="table-box tra-table-box"
                                         style="height: 364px;width: 98%;position: relative;overflow: hidden;margin-bottom: 0;">
                                        <!-- 表单 -->
                                        <table id="grid-table-bonusPublicityExamineTable"></table>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
            <div class="layer-btn archivesTabBtn">
                <a href="javascript:;" class="layer_btn layer_back" id="bonusPublicityCancel">返回</a>
            </div>

        </form>
    </div>
</script>

<!-- 预览 -->
<script type="text/html" id="bonusPublicityPreviewFormHtml">
    <div class="layui-tab-content"
         style="position: absolute;width: 600px;;border: 1px solid #ccc;border-radius:8px;padding: 0px;top: -80px;left: -540px;background: #fff;display: none;">
        <div style="position: relative;height: 100%;">
            <!-- 表单 -->
            <div
                style="background:#f2f2f2;height: 30px;line-height: 30px;padding-left: 5px;background: -webkit-linear-gradient(top,#f7fbff,#f2f2f2); background: -o-linear-gradient(top,#f7fbff,#f2f2f2); background: -moz-linear-gradient(top,#f7fbff,#f2f2f2);">
                人员奖金详情
            </div>
            <div style="position: relative;margin-left: -2px" class="staticTable">
                <table id="grid-table-bonusPublicityPreviewTable" style="top: 30px;height: 100%;"></table>
            </div>

        </div>
    </div>
</script>


