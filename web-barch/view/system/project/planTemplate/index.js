"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            var motheTableDom, sonTableDom;//子母表名字
            var defOption = {};
            defOption.stageList = [];//子母表的初始数据
            defOption.rows = [];//子母表的初始数据
            form.render("select");

            var lastSel, zmTables, zmtableID;
            //表格渲染
            var trasenTable = new $.trasenTable("grid-table-systemProjectPlanTemplate", {
                url: common.url + '/ts-cp/planTemplateCfg/list',
                pager: 'grid-pager-systemProjectPlanTemplate',
                //表格字段
                colModel: [
                    {
                        label: 'ID',
                        name: 'planTemplateId',
                        index: 'plan_template_id',
                        width: "auto",
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '模板名称',
                        name: 'templateName',
                        index: 'template_name',
                        width: 150,
                        align: "center",
                        editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<a href="javascript:;" id="templateNameDetail" class="tddetaile" rowid="' + options.rowId + '">' + cellvalue + '</a>';
                        }
                    },
                    {
                        label: '所属产品',
                        name: 'productName',
                        index: 'product_name',
                        width: 100,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '计划类型',
                        name: 'planType',
                        index: 'plan_type',
                        width: 150,
                        align: "center",
                        editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            var text;
                            if (cellvalue == 1) {
                                text = "实施类";
                            } else if (cellvalue == 2) {
                                text = "研发类";
                            } else if (cellvalue == 3) {
                                text = "接口类";
                            } else if (cellvalue == 4) {
                                text = "维护类";
                            }
                            return text;
                        }
                    },
                    {
                        label: '创建人',
                        name: 'createUserName',
                        index: 'create_user_name',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '创建时间',
                        name: 'createDate',
                        index: 'create_date',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '状态', name: 'isValid', index: 'isValid', width: 150, align: "center", editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            var text;
                            if (cellvalue == 1) {
                                text = "已启用";
                            } else if (cellvalue == 2) {
                                text = "未启用";
                            }
                            return text;
                        }
                    },
                    {name: 'isValid', index: 'isValid', width: "auto", editable: false, hidden: true}

                ],
                queryFormId: 'systemProjectPlanTemplatequeryForm'
            });

            zmTables = trasenTable;

            //表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            $(document).on('click', '#cancel', function () {
                layer.closeAll('page');
            });

            //查询
            form.on('submit(systemProjectPlanTemplatesearch)', function (data) {
                refreshTable()
            });

            // 新增操作
            $("#systemProjectPlanTemplate").off("click", "#addPlanTemplateCfg").on("click", "#addPlanTemplateCfg", function () {
                defOption.stageList = [];
                var html = systemProjectPlanTemplateAddHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '新增计划模板',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['70%', '90%'], //宽高
                    content: html,
                    scrollbar: true,
                    success: function (layero, index) {
                        form.render("select");
                        $('#systemProjectPlanTemplateAddHtmlDiv #productIdCheckDiv .layui-form-select').remove();
                        initProduct();
                        motheTable([]);
                        sonTable();
                    },
                });
            });

            //母表
            function motheTable(data, type) {
                if (data == null) {
                    layer.msg('数据为空！');
                    return false;
                }
                var tool = false,
                    toolEdi = true;
                if (type == 1) {
                    tool = true;
                    toolEdi = false;
                }
                motheTableDom = new $.trasenTable("table-modMain", {
                    datatype: 'local',
                    pager: '',
                    rownumbers: false,
                    shrinkToFit: true,
                    cellEdit: true,
                    data: data,
                    colModel: [
                        {
                            label: 'ID',
                            name: '',
                            index: '',
                            width: "auto",
                            align: "center",
                            editable: false,
                            hidden: true
                        },
                        {
                            label: '阶段名称',
                            name: 'stageName',
                            index: 'stage_name',
                            width: 100,
                            align: "center",
                            editable: toolEdi
                        },
                        {
                            label: '进度占比(%)',
                            name: 'ratio',
                            index: 'ratio',
                            width: 60,
                            align: "center",
                            editable: toolEdi,
                            number: true,
                            editrules: {
                                custom: true,
                                number: true,
                                custom_func: function (value, colNames) {
                                    value = Number(value);
                                    if (value > 100 || value < 0) {
                                        return [false, "数值不在0～100"];
                                    } else {
                                        return [true];
                                    }
                                }
                            },
                            formatter: 'integer',
                            formatoptions: {defaulValue: '0'}
                        },
                        {
                            label: '排序', name: 'seq', index: 'seq', width: 60, align: "center", editable: toolEdi,
                            editrules: {
                                number: true
                            },
                            formatter: 'integer',
                            formatoptions: {defaulValue: 0}
                        },
                        {
                            label: '操作',
                            name: 'caozuo',
                            index: '',
                            width: 60,
                            align: "center",
                            classes: 'MTsaveClick',
                            editable: false,
                            hidden: tool,
                            formatter: function (cellvalue, options, rowObject) {
                                return '<a href="javascript:;" class="motherTableDelect" id="motherTableDelect" rowid="' + options.rowId + '"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                            }
                        },
                        {
                            label: 'save',
                            name: '',
                            index: '',
                            width: 60,
                            align: "center",
                            classes: 'bodysaveClick',
                            editable: false,
                            hidden: true
                        }
                    ],
                    onCellSelect: function (rowid, n, m, v) {
                        $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left td input.ui-widget-content').focus();
                        zmtableID = '#systemProjectPlanTemplateAddHtmlDiv .childMother-left';
                        lastSel = rowid;
                        //选中
                        var motheData = motheTableDom.getAllData();
                        var preid = motheTableDom.getSelectRowId();
                        var zbreid = sonTableDom.getSelectRowId();
                        var sonData = sonTableDom.getAllData();
                        var data = [];
                        var poneid;
                        if (preid == null) {
                            poneid = rowid;
                        }
                        if (defOption.stageList.length == 0) {
                            defOption.stageList = motheData;
                        }
                        if (poneid != rowid) {
                            $.each(motheData, function (i, v) {
                                if (v.rowid == preid) {
                                    v.outputList = sonData;
                                    defOption.stageList[i] = v;
                                }
                            });
                        } else {
                            $.each(motheData, function (i, v) {
                                if (v.rowid == preid) {
                                    defOption.stageList[i] = v;
                                }
                            });
                        }
                        if (poneid == rowid && !poneid) {
                            $.each(motheData, function (i, v) {
                                if (v.rowid == preid) {
                                    v.outputList = sonData;
                                    defOption.stageList[i] = v;
                                }
                            });
                        }
                        $.each(defOption.stageList, function (i, v) {
                            if (v.rowid == rowid) {
                                data = v.outputList;
                            }
                        });
                        sonTableDom.staticrefresh(data);
                    },
                    //选中行前执行
                    beforeSelectRow: function (rowid, e) {
                        $('#systemProjectPlanTemplateAddHtmlDiv .childMother-right .bodysaveClick').trigger('click');

                    },
                    afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                        var md = motheTableDom.getAllData();
                        $.each(defOption.stageList, function (i, v) {
                            $.each(md, function (j, vo) {
                                if (v.rowid == vo.rowid) {
                                    defOption.stageList[i][cellname] = vo[cellname];
                                    defOption.stageList[i]['ratio'] = vo['ratio'];
                                    defOption.stageList[i]['stageName'] = vo['stageName'];
                                }
                            });
                        });
                        // $.each(defOption.stageList, function (i, v) {
                        //     if (v.rowid == rowid) {
                        //         defOption.stageList[i][cellname] = parseInt(value);
                        //     }
                        // });
                        // setTimeout(function(){
                        //     motheTableDom.staticrefresh(defOption.stageList);
                        // })
                        setTimeout(function () {
                            if (cellname == 'ratio') {
                                totalratioFun(defOption.stageList)
                            }
                            if (cellname == 'seq') {
                                var data = sortFun(defOption.stageList, 'seq') //排序
                                defOption.stageList = data
                                motheTableDom.staticrefresh(data);
                                var mdata = motheTableDom.getAllData();
                                sonTableDom.staticrefresh([]);
                                $.each(mdata, function (i, v) {
                                    defOption.stageList[i]['rowid'] = v.rowid;
                                })
                            }
                        })
                    }
                });
            }

            function sortReset(data) {

            }

            //进度总额计算
            function totalratioFun(data) {
                var ratioSum = 0;
                $.each(data, function (i, v) {
                    ratioSum += parseFloat(v['ratio']);
                });
                $('#systemProjectPlanTemplateAddHtmlDiv #totalratio').html(ratioSum);
            }

            //母表新增行
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv #motherTableAdd').on('click', '#systemProjectPlanTemplateAddHtmlDiv #motherTableAdd', function () {
                var data = {};
                var rowid = motheTableDom.getSelectRowId();//母表选中行id
                var id = '[id="' + rowid + '"]';
                var srowid = sonTableDom.getSelectRowId();//子表选中行id
                var sid = '[id="' + rowid + '"]';
                $('body').trigger('click');
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-right').find(sid).find('.saveClick').trigger('click');
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left').find(id).find('.MTsaveClick').trigger('click');

                var ids = motheTableDom.getDataIDs();
                var nid = ids.length + 1
                for (var i in ids) {
                    nid = nid + parseInt(ids[i])
                }

                var mdata = motheTableDom.getAllData();
                var seqn = 0;
                $.each(mdata, function (i, v) {
                    if (i == mdata.length - 1) {
                        seqn = v.seq
                    }
                })
                data.seq = parseInt(seqn) + 1;
                motheTableDom.addRowData(nid, data, 'last');
                // sonTableDom.staticrefresh([]);
                // motheTableDom.setSelection(nid);
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left tr').removeClass('ui-state-hover');
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left td').removeClass('ui-state-highlight');
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left tr.ui-state-highlight').addClass('ui-state-hover').removeClass('ui-state-highlight');
            });

            //母表行删除
            var m_time = 0;
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv .motherTableDelect').on('click', '#systemProjectPlanTemplateAddHtmlDiv .motherTableDelect', function () {
                var srowid = motheTableDom.getSelectRowId();
                var rowid = $(this).attr('rowid');
                var sdata = [];
                // return
                // if(defOption.stageList.length > 1){
                //     $.each(defOption.stageList, function (i, v) {
                //         if (i == 0 && rowid != v.rowid) {
                //             $('#table-modMain [id="' + v.rowid + '"] td').trigger('click');
                //             sdata = v.outputList
                //         }else if(i == 0 && rowid == v.rowid){
                //             $('#table-modMain [id="' + defOption.stageList[1].rowid + '"] td').trigger('click');
                //             sdata = defOption.stageList[1].outputList
                //         }
                //     })
                // }else{
                //     // sonTableDom.staticrefresh([]);
                // }

                motheTableDom.delRowData(rowid);
                sonTableDom.staticrefresh([]);
                var ad = motheTableDom.getAllData();
                // if(ad.length == 0){
                //     sonTableDom.staticrefresh([]);
                // }else{
                //     sonTableDom.staticrefresh(sdata);
                // }
                totalratioFun(ad)
            });

            //子表
            function sonTable(data, type) {
                var tool = false,
                    toolEdi = true,
                    toolclass = 'sonRatioButton sonRatioButtonBox',
                    isonButton = 'isonButton';
                if (type == 1) {
                    tool = true;
                    toolEdi = false;
                    toolclass = 'sonRatioButtonBox';
                    isonButton = '';
                }
                sonTableDom = new $.trasenTable("table-modSon", {
                    datatype: 'local',
                    pager: '',
                    rownumbers: false,
                    shrinkToFit: true,
                    cellEdit: true,
                    data: data,
                    colModel: [
                        {
                            label: 'ID',
                            name: 'planStageOutputId',
                            index: 'plan_stage_output_id',
                            width: "auto",
                            align: "center",
                            editable: false,
                            hidden: true
                        },
                        {
                            label: '阶段成果物',
                            name: 'stageOutputName',
                            index: 'stage_output_name',
                            width: 100,
                            align: "center",
                            editable: toolEdi
                        },
                        {
                            label: '计算产值',
                            name: 'isCalc',
                            index: 'is_calc',
                            width: 60,
                            align: "center",
                            editable: false,
                            hidden: true,
                            formatter: 'integer',
                            formatoptions: {defaulValue: 2, thousandsSeparator: ''}
                        },
                        {
                            label: '计算产值',
                            name: 'isCalcshow',
                            index: 'is_calc',
                            classes: 'layui-form',
                            width: 60,
                            align: "center",
                            editable: false,
                            formatter: function (cellvalue, options, rowObject) {//ayui-form-onswitch
                                var isc = rowObject.isCalc;
                                var cl, tp = 2;
                                if (isc == 1) {
                                    cl = 'layui-form-onswitch';
                                    tp = 1;
                                } else if (isc == 2 || isc == '') {
                                    cl = '';
                                    tp = 2;
                                }
                                return '<div class="layui-unselect layui-form-switch ' + isonButton + ' ' + cl + '" data-rowid="' + options.rowid + '" data-type="' + tp + '"><em></em><i></i></div>';
                            }
                        },
                        {
                            label: '核算比例(%)',
                            name: 'ratio',
                            index: 'ratio',
                            width: 60,
                            align: "center",
                            editable: false,
                            hidden: true
                        },
                        {
                            label: '核算比例(%)',
                            name: 'ratio',
                            index: 'ratio',
                            width: 60,
                            classes: toolclass,
                            align: "center",
                            editable: false
                        },
                        {
                            label: '排序', name: 'seqNo', index: 'seq_no', width: 60, align: "center", editable: toolEdi,
                            editrules: {
                                number: true,
                                required: true
                            },
                            formatter: 'integer',
                            formatoptions: {defaulValue: 0}
                        },
                        {
                            label: '操作',
                            name: 'caozuo',
                            index: '',
                            width: 60,
                            align: "center",
                            classes: 'saveClick',
                            editable: false,
                            hidden: tool,
                            formatter: function (cellvalue, options, rowObject) {
                                return '<a href="javascript:;" class="sonTableDelete" id="sonTableDelete"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                            }
                        },
                        {
                            label: 'save',
                            name: '',
                            index: '',
                            width: 60,
                            align: "center",
                            classes: 'bodysaveClick',
                            editable: false,
                            hidden: true
                        }
                    ],
                    afterInsertRow: function (rowid) {
                        sonTableDom.setCell(rowid, 'isCalc', 2);
                    },
                    //选中行前执行
                    beforeSelectRow: function (rowid, e) {
                    },
                    onCellSelect: function (rowid) {
                        zmTables = sonTableDom;
                        zmtableID = '#systemProjectPlanTemplateAddHtmlDiv .childMother-right'
                        lastSel = rowid
                    },
                    afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                        if (cellname == 'seqNo') {
                            var mdata = sonTableDom.getAllData();
                            var data = sortFun(mdata, 'seqNo');
                            var rid = motheTableDom.getSelectRowId();
                            $.each(defOption.stageList, function (i, v) {
                                if (v.rowid == rid) {
                                    defOption.stageList[i].outputList = data
                                }
                            })
                            sonTableDom.staticrefresh(data);

                        }
                    }
                });
            }

            // 点击表格外面保存可编辑表格数据
            $('body').on('click', 'td', function (event) {
                event.stopPropagation();
            });
            $('body').bind('click', function (e) {
                if (lastSel != "") {
                    $(zmtableID).find('[id="' + lastSel + '"] .bodysaveClick').trigger('click');
                    lastSel = false;
                }
            });

            //是否选择
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv .isonButton').on('click', '#systemProjectPlanTemplateAddHtmlDiv .isonButton', function () {
                var type = $(this).attr('data-type');
                var rowid = sonTableDom.getSelectRowId();
                var $tr = $(this).closest('tr');
                if (type == 2) {
                    $(this).attr('data-type', 1);
                    $(this).addClass('layui-form-onswitch');
                    sonTableDom.setCell(rowid, 'isCalc', 1);
                    $tr.find('.sonRatioButton').removeClass('sonRatioButtonBox').html('<input type="number" id="sonRatioButton" data-rowid="' + rowid + '" />');
                    setTimeout(function () {
                        $('#systemProjectPlanTemplateAddHtmlDiv #sonRatioButton').focus();
                    })
                } else {
                    $(this).attr('data-type', 2);
                    $(this).removeClass('layui-form-onswitch');
                    sonTableDom.setCell(rowid, 'isCalc', 2);
                    sonTableDom.setCell(rowid, 'ratios', 0);
                    sonTableDom.setCell(rowid, 'ratio', 0);
                    $tr.find('.sonRatioButton').addClass('sonRatioButtonBox').html('');
                }
            });
            $('body').off('blur', '#systemProjectPlanTemplateAddHtmlDiv #sonRatioButton').on('blur', '#systemProjectPlanTemplateAddHtmlDiv #sonRatioButton', function () {
                var val = $(this).val();
                var rowid = $(this).attr('data-rowid');
                var $tr = $(this).closest('.sonRatioButton');
                $tr.html(val).addClass('sonRatioButtonBox');
                $tr.attr({
                    'data-type': '1',
                    'rowid': rowid
                })
                // sonTableDom.setCell(rowid,'ratios',val);
                sonTableDom.setCell(rowid, 'ratio', val);
            });
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv .sonRatioButtonBox').on('click', '#systemProjectPlanTemplateAddHtmlDiv .sonRatioButtonBox', function (e) {
                e.stopPropagation();
                var type = $(this).closest('tr').find('.isonButton').attr('data-type');
                var val = $(this).text();
                var $this = $(this);
                $(this).removeClass('sonRatioButtonBox');
                if (type == 1) {
                    var rowid = $(this).attr('rowid');
                    // var val = sonTableDom.getCell(rowid, 'ratio');
                    $(this).attr({
                        'data-type': ''
                    })
                    $(this).html('<input type="number" value="' + val + '" id="sonRatioButton" data-rowid="' + rowid + '" />');
                    setTimeout(function () {
                        $('#systemProjectPlanTemplateAddHtmlDiv #sonRatioButton').focus();
                    })
                }
            });
            //子表新增行
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv #sonTableAdd').on('click', '#systemProjectPlanTemplateAddHtmlDiv #sonTableAdd', function () {
                var data = {};
                var ln = sonTableDom.getDataIDs().length;
                var rowid = motheTableDom.getSelectRowId();//母表选中行id
                var id = '[id="' + rowid + '"]';
                var srowid = sonTableDom.getSelectRowId();//母表选中行id
                var sid = '[id="' + rowid + '"]';
                if (rowid == null || rowid == 'null') {
                    layer.msg('请先选择阶段！');
                    return
                }
                $('body').trigger('click');
                // $('.childMother-right').find(sid).find('.saveClick').trigger('click');
                // $('.childMother-left').find(id).find('.MTsaveClick').trigger('click');
                data.seqNo = ln + 1;

                var mdata = sonTableDom.getAllData();
                var seqn = 0;
                $.each(mdata, function (i, v) {
                    if (i == mdata.length - 1) {
                        seqn = v.seqNo
                    }
                })
                data.seqNo = parseInt(seqn) + 1;

                sonTableDom.addRowData(ln + 1, data, 'last');
                sonTableDom.setSelection(ln + 1);
                $('.childMother-right .ui-state-highlight').removeClass('ui-state-highlight');
            });

            //子表行删除
            $('body').off('click', '#systemProjectPlanTemplateAddHtmlDiv .sonTableDelete').on('click', '#systemProjectPlanTemplateAddHtmlDiv .sonTableDelete', function () {
                var rowid = sonTableDom.getSelectRowId();
                sonTableDom.delRowData(rowid);
            });

            //提交数据
            form.on('submit(systemProjectPlanTemplateAddHtmlDivformSavePlanTemplateCfgSend)', function (data) {
                var planTemplateId = data.field['planTemplateId'];
                var options = $("#systemProjectPlanTemplateAddHtmlDiv #productId").find("option:selected");
                var productName = options[0].text;
                data.field.productName = productName;
                // data.field.templateName = $('#templateName').val();
                // 添加为空判断
                if (options.val() == '请输入产品名称查询') {
                    layer.msg('所属产品不能为空！')
                    return false;
                }
                //表格数据保存
                var rowid = motheTableDom.getSelectRowId();//母表选中行id
                var id = '[id="' + rowid + '"]';
                var srowid = sonTableDom.getSelectRowId();//母表选中行id
                var sid = '[id="' + rowid + '"]';
                // $(sid).find('.saveClick').trigger('click');
                // $(id).find('.MTsaveClick').trigger('click');

                var mTableData = motheTableDom.getAllData();
                $.each(mTableData, function (i, v) {
                    $.each(defOption.stageList, function (j, vo) {
                        if (v.rowid == vo.rowid) {
                            v = vo;
                        }
                    })
                    mTableData[i] = v;
                })
                var i = false
                //判断阶段下是否有成果物
                var j = 0;

                if (mTableData.length == 0) {
                    layer.msg('请添加阶段！');
                    return false;
                }

                mTableData.forEach(function (value) {
                    j = j + parseInt(value.ratio);
                    if (value.outputList.length == 0) {
                        i = true;
                    }
                });

                if (i) {
                    layer.msg('阶段下未添加成果物，保存失败！');
                    return false;
                }
                if (j != 100) {
                    layer.msg('进度占比需满100%');
                    return false;
                }
                if (j > 100) {
                    layer.msg('进度占比超过100%');
                    return false;
                }
                data.field.stageList = mTableData;
                if (planTemplateId) {
                    trasen.ajax({
                        url: common.url + '/ts-cp/planTemplateCfg/update',
                        type: 'POST',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.closeAll('page');
                            refreshTable();

                        }
                    })
                } else {
                    trasen.ajax({
                        url: common.url + '/ts-cp/planTemplateCfg/save',
                        type: 'post',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.closeAll('page');
                            refreshTable();
                        }
                    })
                }
            });

            form.on('submit(systemProjectPlanTemplateAddHtmlDivformSavePlanTemplateCfg)', function (data) {
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-right').find('.bodysaveClick').trigger('click');
                $('#systemProjectPlanTemplateAddHtmlDiv .childMother-left').find('.bodysaveClick').trigger('click');
                $('#systemProjectPlanTemplateAddHtmlDiv [lay-filter="systemProjectPlanTemplateAddHtmlDivformSavePlanTemplateCfgSend"]').trigger('click');
            })

            //编辑
            var lplanTemplateCfgEditorRowid //母表选中行id
            $("body").off("click", "#planTemplateCfgEditor").on("click", "#planTemplateCfgEditor", function () {
                var rowData = trasenTable.getSelectRowData();
                detailEdi(rowData, 0)
            });

            //查看
            $("#systemProjectPlanTemplate").off("click", "#templateNameDetail").on("click", "#templateNameDetail", function () {
                var id = $(this).attr('rowid');
                var rowData = trasenTable.getRowData(id);
                detailEdi(rowData, 1)
            });

            function detailEdi(rowData, type) {
                var id = rowData['planTemplateId'];
                var html = systemProjectPlanTemplateAddHtml.innerHTML;
                if ((rowData.length || rowData.length == 0) && type == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                $.ajax({
                    type: 'post',
                    url: common.url + "/ts-cp/project/stage/output/planTemplateId/" + id,
                    success: function (data) {
                        if (data && data.success) {
                            if (data.object.length > 0 && type == 0) {
                                layer.msg('模板已被使用,不能重新编辑');

                                return false;
                            } else {
                                defOption.stageList = [];
                                layer.open({
                                    type: 1,
                                    title: '编辑',
                                    closeBtn: 1,
                                    shadeClose: false,
                                    area: ['70%', '90%'], //宽高
                                    content: html,
                                    success: function (layero, index) {
                                        $.ajax({
                                            type: 'post',
                                            url: common.url + "/ts-cp/planTemplateCfg/findById/" + id,
                                            success: function (data) {
                                                if (data && data.success) {
                                                    trasen.setNamesVal(layero, data.object);

                                                    var productName = data.object.productName;
                                                    var productId = data.object.productId;
                                                    if (data.object.productName != null) {
                                                        $("#systemProjectPlanTemplateAddHtmlDiv #initproductName").text(productName);
                                                        $("#systemProjectPlanTemplateAddHtmlDiv #initproductName").val(productId);
                                                    }
                                                    form.render("select");
                                                    $('#systemProjectPlanTemplateAddHtmlDiv #productIdCheckDiv .layui-form-select').remove();
                                                    initProduct();
                                                    // if (data.object.stageList == null || data.object.stageList == undefined || data.object.stageList == '') {
                                                    //     data.object.stageList = [];
                                                    // }
                                                    defOption.stageList = data.object.stageList || []
                                                    motheTable(data.object.stageList || [], type);
                                                    var sData = [];
                                                    if (data.object.stageList != null && data.object.stageList != undefined && data.object.stageList != '') {
                                                        sData = data.object.stageList[0].outputList;
                                                    }
                                                    sonTable([], type);
                                                    var mdata = motheTableDom.getAllData();
                                                    var atao = 0
                                                    $.each(mdata, function (i, v) {
                                                        defOption.stageList[i]['rowid'] = v.rowid;
                                                        // if (i == 0) {
                                                        //     $('#table-modMain [id="1"] td').trigger('click');
                                                        // }
                                                    })
                                                    totalratioFun(defOption.stageList)
                                                }
                                            }
                                        })
                                    }
                                });
                            }
                            if (type == 1) {
                                $('#systemProjectPlanTemplateAddHtmlDiv #addPlanTemplateCfgForm input,#systemProjectPlanTemplateAddHtmlDiv #addPlanTemplateCfgForm select').attr('disabled', true);
                                $('#systemProjectPlanTemplateAddHtmlDiv #addPlanTemplateCfgBtn,#previewPlanTemplateCfgBtn,#systemProjectPlanTemplateAddHtmlDiv .lay-label-title span,#motherTableAdd,#sonTableAdd').remove();
                            }
                        }
                    }
                })
            }

            //复制
            $("#systemProjectPlanTemplate").off("click", "#copyTemplateBut").on("click", "#copyTemplateBut", function () {
                var rowData = trasenTable.getSelectRowData();
                var id = rowData.planTemplateId;
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.confirm('确定要复制吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    trasen.post(common.url + "/ts-cp/planTemplateCfg/copy/" + id, null, function (data) {
                        layer.closeAll('page');
                        layer.msg('操作成功.', {icon: 1});
                        refreshTable();
                    })
                }, function () {
                    layer.closeAll('page');
                });
            })

            //修改状态
            $("#systemProjectPlanTemplate").off("click", "#updateStatusBut").on("click", "#updateStatusBut", function () {
                var rowId = trasenTable.getSelectRowId();
                if (rowId == null) {
                    layer.msg("请选择一个记录进行操作.")
                    return
                }
                var rowObject = trasenTable.getSelectRowData();
                var isValid = rowObject['isValid'];
                var msg = "";
                if (isValid == 1) {
                    msg = "确定要禁用吗？";
                    isValid = 2;
                } else {
                    msg = "确定要启用吗？";
                    isValid = 1;
                }
                layer.confirm(msg, {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    layer.close(index);
                    $.ajax({
                        type: "post",
                        url: common.url + "/ts-cp/planTemplateCfg/updateStatus/" + rowObject.planTemplateId + "/" + isValid,
                        success: function (res) {
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('操作成功');
                                refreshTable();
                            } else {
                                layer.closeAll();
                                layer.msg('操作失败！');
                            }
                        }
                    });
                });
            });

            //所属产品
            function initProduct() {
                $.publicSelect2($("#systemProjectPlanTemplateAddHtmlDiv #productId"), {
                    url: '/ts-base-data/product/getcatalogandproduct/list',
                    valueName: "productInfoId",
                    textName: "productName"
                })
            }

            // 预览
            $("body").off("click", "#systemProjectPlanTemplateAddHtmlDiv #previewPlanTemplateCfgBtn").on("click", "#systemProjectPlanTemplateAddHtmlDiv #previewPlanTemplateCfgBtn", function () {
                //表格数据保存
                $('#systemProjectPlanTemplateAddHtmlDiv .archivesTab-con').trigger('click');
                var rowid = motheTableDom.getSelectRowId();//母表选中行id
                var id = '#systemProjectPlanTemplateAddHtmlDiv [id="' + rowid + '"]';
                var srowid = sonTableDom.getSelectRowId();//母表选中行id
                var sid = '#systemProjectPlanTemplateAddHtmlDiv [id="' + rowid + '"]';
                $(sid).find('#systemProjectPlanTemplateAddHtmlDiv .saveClick').trigger('click');
                $(id).find('#systemProjectPlanTemplateAddHtmlDiv .MTsaveClick').trigger('click');

                var mTableData = motheTableDom.getAllData();
                $.each(mTableData, function (i, v) {
                    $.each(defOption.stageList, function (j, vo) {
                        if (v.rowid == vo.rowid) {
                            v = vo;
                        }
                    })
                    if (v.stageName.indexOf('input') > -1) {
                        v.stageName = '';
                    }
                    mTableData[i] = v;
                })

                setTimeout(function () {
                    var html = previewPlanTemplateCfgDiv.innerHTML;
                    var data = {};
                    data.list = mTableData;
                    data.productname = $('#systemProjectPlanTemplateAddHtmlDiv #productId option:selected').text();
                    data.planTypename = $('#systemProjectPlanTemplateAddHtmlDiv #planType option:selected').text();
                    data.mb = $('#systemProjectPlanTemplateAddHtmlDiv #templateName').val();
                    if (data.productname == '请输入产品名称查询') {
                        data.productname == '';
                    }
                    $.each(data.list, function (i, v) {
                        if (v.outputList) {
                            $.each(v.outputList, function (j, vo) {
                                if (vo.isCalc == 1) {
                                    vo.isCalcName = '是';
                                } else if (vo.isCalc == 2) {
                                    vo.isCalcName = '否';
                                }
                                v[j] = vo;
                            })
                            data[i] = v;
                        }
                    })
                    laytpl(html).render(data, function (html) {
                        layer.open({
                            type: 1,
                            title: '预览',
                            closeBtn: 1,
                            shadeClose: false,
                            area: ['50%', '90%'], //宽高
                            content: html,
                            scrollbar: true,
                            success: function (layero, index) {
                            },
                        });
                    });
                })

            });

            form.on('select(systemProjectPlanTemplateplanTypeSelecte)', function (data) {
                refreshTable();
            });

            function sortFun(arr, name) {
                $.each(arr, function (i, v) {
                    arr[i][name] = parseInt(v[name])
                })
                var objectArraySort = function (keyName) {
                    return function (objectN, objectM) {
                        var valueN = objectN[keyName]
                        var valueM = objectM[keyName]
                        // if (valueN < valueM) return -1
                        // else if (valueN > valueM) return 1
                        // else return 0
                        return valueN - valueM
                    }
                }
                return arr.sort(objectArraySort(name))
            }

        })
    }
})
