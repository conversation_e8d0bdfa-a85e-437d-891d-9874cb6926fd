"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'trasen'], function() {
            var form = layui.form,
                layer = layui.layer,
                trasen = layui.trasen;

            // 表格渲染
            var trasenTable = new $.trasenTable("grid-table-numberConfigTable", {
                url: common.url + '/ts-hrms/numberConfig/list',
                pager: 'grid-pager-numberConfigPager',
                shrinkToFit: true,
                colModel: [
                    { label: '编号配置类别', name: 'configCategoryText', index: 'config_category', width: 150, editable: false },
                    { label: '序号位数', name: 'serialDigits', index: 'serial_digits', width: 80, editable: false },
                    { label: '初始值', name: 'initialNumber', index: 'initial_number', width: 150, editable: false },
                    { label: '前缀', name: 'serialPrefix', index: 'serial_prefix', width: 150, editable: false },
                    { label: '后缀', name: 'serialSuffix', index: 'serial_suffix', width: 150, editable: false },
                    { label: '说明', name: 'remark', index: 'remark', width: 300, editable: false },
                    { label: 'ID', name: 'numberConfigId', hidden: true },
                    { label: '编号配置类别id', name: 'configCategory', hidden: true },
                ],
                queryFormId: 'numberConfigQueryForm'
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 查询
            form.on('submit(numberConfigSearch)', function(data) {
                refreshTable();
            });
            
            // 新增编号配置
            $("#numberConfig").off("click", "#numberConfigAdd").on("click", "#numberConfigAdd", function() {
                $.quoteFun('system/manage/numberConfig/modules/add', {
                    title: '新增编号配置',
                    ref: refreshTable
                })
            });

            // 编辑编号配置
            $("#numberConfig").off('click', '#numberConfigTableEditor').on("click", "#numberConfigTableEditor", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                $.quoteFun('system/manage/numberConfig/modules/add', {
                    title: '编辑编号配置',
                    data: rowData,
                    ref: refreshTable
                })

            });

            // 删除
            $("#numberConfig").off("click", "#numberConfigTableDel").on("click", "#numberConfigTableDel", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条需要删除的数据!')
                    return false;
                }
                layer.confirm('确定要删除当前选中的数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function() {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/numberConfig/deletedById/" + rowData.numberConfigId,
                        success: function(res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function() {});
            });

        });
    }
});