"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    var selBlock;
    var so1;
    var elementChannels = [];
    var processType = [];
    var elementMedicalBusinessList = [
      {
        id: 1,
        medicalBusinessName: "业务情况",
      },
      {
        id: 101,
        medicalBusinessName: "百分制考核情况",
      },
    ];
    // elementType 6 elementColumn 1 通知公共
    // elementType 2 热点新闻
    // elementType 9 提醒事项

    // elementType 4 日程
    // elementType 1 流程待办
    // elementType 6 elementColumn 2 信息管理
    // elementType 5 常用流程

    // elementType 3 公文
    // elementType 7 外部系统
    // elementType 8 常用入口
    // elementType 10 文档管理
    // elementType 11 电子邮箱
    // elementType 14 医疗业务情况
    let heightDictionary = {
      1: 132,
      2: 100,
      9: 65,
      5: 120,
      6: 120,
      4: 136,
      3: 120,
      7: 74,
      8: 74,
      10: 74,
      11: 132,
      12: 132,
      13: 100,
      14: 132,
      15: 120,
    };
    const isAdmin = common.isAdmin;

    layui.use(["form", "trasen", "laytpl"], function () {
      var form = layui.form;
      var trasen = layui.trasen;
      var laytpl = layui.laytpl;
      var blockList = [];
      var userList = [];
      var roleList = [];
      var deptList = [];
      var groupList = [];
      var wins = layer.open({
        type: 1,
        title: "内容设置",
        closeBtn: false,
        maxmin: false,
        shadeClose: false,
        area: ["1000px", "620px"], //宽高
        content: html,
        success: function (layero, index) {
          trasen.setNamesVal($("#homePageEdit .layui-form"), {
            allReader: "1",
          });

          if (isAdmin === "N") {
            let name = common.userInfo.username;
            $("#homePageEdit #IsAdminShow").hide();
            $("#homePageEdit #UserPower").show();
            $("#homePageEdit #UserPower").text(name);
          } else {
            $("#homePageEdit #IsAdminShow").show();
            $("#homePageEdit #UserPower").hide();
            $("#homePageEdit #UserPower").text("");
          }

          $.ajax({
            url: "/ts-information/informationChannel/list?status=1&pageSize=100&pageNo=1",
            async: false,
            success: function (res) {
              elementChannels = res.rows || [];
              if (opt.data) {
                trasen.setNamesVal($("#homePageEdit .layui-form"), opt.data);
                blockList = opt.data.portalElement || [];
                //栏目名称
                for (var i = 0; i < blockList.length; i++) {
                  if (
                    blockList[i].elementType == 6 &&
                    blockList[i].elementColumn == 1
                  ) {
                    blockList[i].elementChannelName = getChannelName(
                      blockList[i].elementChannel
                    );
                  } else if (blockList[i].elementType == 14) {
                    blockList[i].elemenModuleName = getMedicalBusinessName(
                      blockList[i].elementMedicalBusiness
                    );
                  }
                }
                setWidthHeightTypeHandle(blockList, opt.data.fixedHeight);
                tempBlcok();

                if (opt.data.isDefault == 1) {
                  // $('#addNewBlock').closest('.layui-col-md6').remove();
                  $('#homePageEdit [name="title"]').prop("disabled", true);
                  $('#homePageEdit [name="allReader"]').prop("disabled", true);
                }
                changeRights();
                dealUser();
                dealRole();
                dealDept();
                dealGroup();
                showStr();
              }
              // so1 = Sortable.create($('#homePageEdit .home-box')[0], {
              //     sort: true,
              //     dragClass: 'sortable-drag',
              //     onSort: function () {},
              // });
            },
          });
        },
      });

      // 渲染盒子 并 进行瀑布流排序
      function tempBlcok() {
        laytpl($("#blockTempHtml").html()).render(blockList, function (string) {
          $("#homePageEdit .home-box").html(string);
        });

        let homeBox = $("#homePageEdit .home-box");
        homeBox.waterfall({
          itemClass: ".home-block",
          cellWidth: 100, //单元格宽度
          spacingWidth: 0, // 水平间距
          spacingHeight: 0, // 垂直间距
          column: 4, // 列数
        });

        addDragetHandle();
      }

      // 添加拖拽方法
      function addDragetHandle(params) {
        const homeBlock = $(".home-block");
        // 拖拽最后 在那个dom上
        let drageNeverDom = {};
        homeBlock.each((index, item) => {
          // 拖拽 触碰到的盒子 获取dom和sord
          item.ondragleave = function (event) {
            let dom = $(event.target).closest(".home-block");
            let data = $(dom).data();
            drageNeverDom.dom = dom;
            drageNeverDom.sord = data.sord;
          };
          // 拖拽结束
          item.ondragend = function (event) {
            // 拖动元素和sord
            const thisDom = $(event.target);
            const thisSord = $(thisDom).data("sord");
            // 最终拖拽至dom元素和sord
            const finalDom = drageNeverDom.dom;
            const finalSord = drageNeverDom.sord;

            blockList[thisSord].sord = finalSord;
            blockList[finalSord].sord = thisSord;
            blockList = blockList.sort((a, b) => {
              return a.sord - b.sord;
            });
            tempBlcok();
          };
        });
      }

      function setWidthHeightTypeHandle(blockList, fixedHeight = 0) {
        blockList.forEach((item) => {
          item.height = heightDictionary[item.elementType];
          if (item.elementType == 6 && item.elementColumn == 1) {
            item.height = 100;
          }
          if (fixedHeight) item.height = 132;
        });
      }

      // 栏目名称过滤替换
      function getChannelName(id) {
        for (var i = 0; i < elementChannels.length; i++) {
          if (elementChannels[i].id == id) {
            return elementChannels[i].channelName;
          }
        }
        return "";
      }

      // 医疗业务名称过滤替换
      function getMedicalBusinessName(id) {
        for (var i = 0; i < elementMedicalBusinessList.length; i++) {
          if (elementMedicalBusinessList[i].id == id) {
            return elementMedicalBusinessList[i].medicalBusinessName;
          }
        }
        return "";
      }

      function dealUser() {
        if (opt.data.readerName) {
          var arr = opt.data.readerName.split(",");
          var arr2 = opt.data.readerUser.split(",");
          for (var i = 0; i < arr.length; i++) {
            userList.push({
              name: arr[i],
              code: arr2[i],
            });
          }
        }
      }

      function dealRole() {
        if (opt.data.readerRoleName) {
          var arr = opt.data.readerRoleName.split(",");
          var arr2 = opt.data.readerRole.split(",");
          for (var i = 0; i < arr.length; i++) {
            roleList.push({
              name: arr[i],
              id: arr2[i],
            });
          }
        }
      }

      function dealDept() {
        if (opt.data.readerOrgName) {
          var arr = opt.data.readerOrgName.split(",");
          var arr2 = opt.data.readerOrg.split(",");
          for (var i = 0; i < arr.length; i++) {
            deptList.push({
              name: arr[i],
              id: arr2[i],
            });
          }
        }
      }

      function dealGroup() {
        if (opt.data.readerGroupName) {
          var arr = opt.data.readerGroupName.split(",");
          var arr2 = opt.data.readerGroup.split(",");
          for (var i = 0; i < arr.length; i++) {
            groupList.push({
              name: arr[i],
              id: arr2[i],
            });
          }
        }
      }

      function showStr() {
        var ua = [];
        var ra = [];
        var da = [];
        var ga = [];
        for (var i = 0; i < userList.length; i++) {
          ua.push(userList[i].name);
        }
        for (var i = 0; i < roleList.length; i++) {
          ra.push(roleList[i].name);
        }
        for (var i = 0; i < deptList.length; i++) {
          da.push(deptList[i].name);
        }
        for (var i = 0; i < groupList.length; i++) {
          ga.push(groupList[i].name);
        }
        if (ua.length > 10) {
          $("#homePageEdit .user-str")
            .text(ua.slice(0, 10).join(",") + "等" + ua.length + "人")
            .attr("title", ua.join(","));
        } else {
          $("#homePageEdit .user-str")
            .text(ua.join(","))
            .attr("title", ua.join(","));
        }
        if (ra.length > 10) {
          $("#homePageEdit .role-str")
            .text(ra.slice(0, 10).join(",") + "等" + ra.length + "个角色")
            .attr("title", ra.join(","));
        } else {
          $("#homePageEdit .role-str")
            .text(ra.join(","))
            .attr("title", ra.join(","));
        }
        if (da.length > 10) {
          $("#homePageEdit .dept-str")
            .text(da.slice(0, 10).join(",") + "等" + da.length + "个部门")
            .attr("title", da.join(","));
        } else {
          $("#homePageEdit .dept-str")
            .text(da.join(","))
            .attr("title", da.join(","));
        }
        if (ga.length > 10) {
          $("#homePageEdit .group-str")
            .text(ga.slice(0, 10).join(",") + "等" + ga.length + "个群组")
            .attr("title", ga.join(","));
        } else {
          $("#homePageEdit .group-str")
            .text(ga.join(","))
            .attr("title", ga.join(","));
        }
      }

      function getRights() {
        var d = {};
        d.readerName = "";
        d.readerUser = "";
        d.readerOrgName = "";
        d.readerOrg = "";
        d.readerRoleName = "";
        d.readerRole = "";
        d.readerGroupName = "";
        d.readerGroup = "";
        if ($('#homePageEdit [name="allReader"]').prop("checked")) {
          return d;
        }
        var ua = [];
        var usa = [];
        for (var i = 0; i < userList.length; i++) {
          ua.push(userList[i].name);
          usa.push(userList[i].code);
        }
        d.readerName = ua.join(",");
        d.readerUser = usa.join(",");
        ua = [];
        usa = [];
        for (var i = 0; i < roleList.length; i++) {
          ua.push(roleList[i].name);
          usa.push(roleList[i].id);
        }
        d.readerRoleName = ua.join(",");
        d.readerRole = usa.join(",");
        ua = [];
        usa = [];
        for (var i = 0; i < deptList.length; i++) {
          ua.push(deptList[i].name);
          usa.push(deptList[i].id);
        }
        d.readerOrgName = ua.join(",");
        d.readerOrg = usa.join(",");
        ua = [];
        usa = [];
        for (var i = 0; i < groupList.length; i++) {
          ua.push(groupList[i].name);
          usa.push(groupList[i].id);
        }
        d.readerGroupName = ua.join(",");
        d.readerGroup = usa.join(",");
        return d;
      }
      //高度模块一致 切换
      $('#homePageEdit [name="fixedHeight"]').on("change", function () {
        changeFixedHeightValue();
      });

      function changeFixedHeightValue() {
        var type = $('#homePageEdit [name="fixedHeight"]').prop("checked");
        setWidthHeightTypeHandle(blockList, !!type);

        layer.msg("模块位置可能发生变化, 请重新排序!");
        tempBlcok();
      }

      //权限切换
      $('#homePageEdit [name="allReader"]').on("change", function () {
        changeRights();
      });

      function changeRights() {
        var type = $('#homePageEdit [name="allReader"]').prop("checked");
        if (type) {
          $("#homePageEdit .rights-box").addClass("none");
        } else {
          $("#homePageEdit .rights-box").removeClass("none");
        }
      }

      //
      $("#homePageEdit .set-rights")
        .off("click")
        .on("click", function () {
          var type = $(this).attr("type");
          var el = $(this);
          if (type == "user") {
            $.quoteFun("/commonPage/userDeptGroup/index", {
              data: {
                user: true,
                dept: true,
                userList: userList,
              },
              callBack: function (list, seluser, dept) {
                userList = seluser;
                showStr();
              },
            });
          }
          if (type == "role") {
            $.quoteFun("/commonPage/roleSel/index", {
              data: {
                list: roleList,
              },
              callBack: function (datas) {
                roleList = datas;
                showStr();
              },
            });
          }
          if (type == "dept") {
            $.quoteFun("/commonPage/deptCheck/deptCheck", {
              checkData: deptList,
              callback: function (data) {
                deptList = data;
                showStr();
              },
            });
          }
          if (type == "group") {
            $.quoteFun("/commonPage/groupSel/index", {
              data: {
                list: groupList,
              },
              callBack: function (datas) {
                groupList = datas;
                showStr();
              },
            });
          }

          return false;
        });

      //显示内容切换
      form.on("select(elementType)", function (data) {
        blockConChange(data.value);
      });

      function blockConChange(type) {
        $("#homePageEdit .child-wrap").addClass("none");
        if (type == 6) {
          $("#homePageEdit .information-wrap").removeClass("none");
        } else if (type == 14) {
          $("#homePageEdit .medical-business-wrap").removeClass("none");
        } else if (type == 15) {
          $("#homePageEdit .process-wrap").removeClass("none");
          initProcessReviewBlock();
        }
        blockColChange($('#homePageEdit [name="elementColumn"]').val());
      }
      //显示栏目
      form.on("select(elementColumn)", function (data) {
        blockColChange(data.value, "form");
      });

      function blockColChange(type, to) {
        // if (type == 1) {
        //     $('#homePageEdit  [name="elementChannel"]').closest('.layui-form-item').removeClass('none');
        // } else {
        //     $('#homePageEdit  [name="elementChannel"]').closest('.layui-form-item').addClass('none');
        // }
        if (type == 1 && to == "form") {
          selBlock && (selBlock.elementChannel = "");
        }
        var names = [];
        var vas =
          selBlock && selBlock.elementChannel
            ? selBlock.elementChannel.split(",")
            : [];
        for (var i = 0; i < vas.length; i++) {
          for (var j = 0; j < elementChannels.length; j++) {
            if (vas[i] == elementChannels[j].id) {
              names.push(elementChannels[j].channelName);
            }
          }
        }
        new $.selectPlug("#elementChannelBox", {
          textName: "channelName", 
          valName: "id", 
          inpValName: "elementChannel",
          inpTextName: "",
          searchType: "local", // 静态数据
          datas: elementChannels,
          // choice: type != 1,
          choice: true,
          defaultText: names.join(","),
          defaultVal: selBlock ? selBlock.elementChannel : "",
        });
      }

      // 初始化 流程查阅
      // 新增 显示内容的切换
      //     编辑回显
      //     编辑其他栏目 显示内容切换 执行函数
      function initProcessReviewBlock() {
        let defaultText = "";
        let defaultVal = "";
        $.ajax({
          url: "/ts-workflow/workflow/definition/getAllDefinitionInfoList",
          async: false,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              processType = res.object.map((item) => {
                return {
                  name: item.workflowName,
                  id: item.wfDefinitionId,
                };
              });

              if (selBlock && selBlock.elementChannel) {
                const find =
                  processType.find(
                    (item) => item.id === selBlock.elementChannel
                  ) || {};
                defaultText = find.name || "";
                defaultVal = find.id || "";
              } else {
                defaultText = processType[0].name;
                defaultVal = processType[0].id;
              }

              new $.selectPlug("#processType", {
                textName: "name", // 选项文字的key
                valName: "id", // 选项id的key
                inpValName: "elementChannel", // 页面表单提交需要的 val参数的name
                inpTextName: "", // 页面表单提交需要的 文字参数的name
                searchType: "local",
                datas: processType,
                choice: false,
                defaultText,
                defaultVal,
              });
            }
          },
        });
      }

      //编辑块
      $("#homePageEdit")
        .off("click", ".home-block")
        .on("click", ".home-block", function () {
          var index = $(this).attr("data-index");
          $("#homePageEdit #addNewBlock").addClass("none");
          selBlock = blockList[index];
          initBlockData(blockList[index]);
          blockConChange(blockList[index].elementType);
          return false;
        });

      //添加块
      $("#homePageEdit #addNewBlock").on("click", function () {
        $(this).addClass("none");
        initBlockData();
        return false;
      });

      function initBlockData(data) {
        $("#homePageEdit #block-info")
          .html($("#blockDataHtml").html())
          .removeClass("none");
        if (data) {
          trasen.setNamesVal($("#homePageEdit #block-info"), data);
        }
        form.render("select");
      }

      function clearBlockData() {
        $("#homePageEdit #addNewBlock").removeClass("none");
        $("#homePageEdit #block-info").html("").addClass("none");
      }
      $("#homePageEdit ").on("click", "#cancleBlock", function () {
        selBlock = null;
        clearBlockData();
        return false;
      });

      //删除块
      $("#homePageEdit").on("click", ".del-block", function () {
        var index = $(this).closest(".home-block").attr("data-index");
        $(this).closest(".home-block").remove();
        blockList.splice(index, 1);
        // blockList = sortBlock();

        blockList.forEach((item, index) => {
          item.sord = index;
        });
        tempBlcok();
        if (selBlock) {
          selBlock = null;
          clearBlockData();
        }
        return false;
      });

      //显示内容确认
      $("#homePageEdit ").on("click", "#saveBlock", function () {
        var d = trasen.getNamesVal($("#homePageEdit #block-info"));
        if (!selBlock) {
          for (var i = 0; i < blockList.length; i++) {
            var theSameElementMedicalBusiness =
              d.elementMedicalBusiness &&
              blockList[i].elementMedicalBusiness == d.elementMedicalBusiness;
            if (
              blockList[i].elementType == d.elementType &&
              d.elementType != 6 &&
              theSameElementMedicalBusiness
            ) {
              layer.msg("请勿重复添加相同显示内容");
              return false;
            }
          }

          if (d.elementType == 6 && d.elementColumn == 1) {
            d.elementChannelName = getChannelName(d.elementChannel);
          } else if (d.elementType == 14) {
            d.elemenModuleName = getMedicalBusinessName(
              d.elementMedicalBusiness
            );
          }
          d.sord = blockList.length;
          // 新增块块 进行高度设置
          d.height = heightDictionary[d.elementType];
          // 是信息管理模块 如果是单例 盒子高度为100
          if (d.elementType == 6 && d.elementColumn == 1) {
            d.height = 100;
          }
          var type = $('#homePageEdit [name="fixedHeight"]').prop("checked");
          if (!!type) {
            d.height = 132;
          }
          blockList.push(d);
          // blockList = sortBlock();
        } else {
          selBlock = $.extend(selBlock, d);
        }

        blockList.forEach((item, index) => {
          item.sord = index;
        });
        tempBlcok();
        clearBlockData();
        selBlock = null;
        return false;
      });

      // function sortBlock(type) {
      //   var arr = [];
      //   if (so1) {
      //     var arrs = so1.toArray();
      //     for (var i = 0; i < arrs.length; i++) {
      //       var item = blockList[arrs[i]];
      //       if (!item) {
      //         continue;
      //       }
      //       item.sord = i;
      //       arr.push(item);
      //     }
      //     for (var i = 0; i < blockList.length; i++) {
      //       if (!blockList[i]) {
      //         blockList.splice(i, 1);
      //         break;
      //       }
      //     }
      //     if (arrs.length < blockList.length) {
      //       arr.push(blockList.pop());
      //     }
      //   } else {
      //     arr = blockList;
      //   }
      //   return arr;
      // }
      //保存
      $("#homePageEdit #save").on("click", function () {
        $('[lay-filter="homePageEditSave"]').trigger("click");
        return false;
      });
      form.on("submit(homePageEditSave)", function (data) {
        // blockList = sortBlock();
        blockList.forEach((item, index) => {
          item.sord = index;
        });
        tempBlcok();
        var d = trasen.getNamesVal($("#homePageEdit"));
        // d.portalElement = sortBlock();
        d.portalElement = blockList;
        var url = "/ts-basics-bottom/portalTheme/save";
        if (opt.data) {
          url = "/ts-basics-bottom/portalTheme/update";
          d = $.extend({}, opt.data, d);
        }
        if (!d.allReader) {
          d.allReader = 0;
        }
        if (!d.fixedHeight) {
          d.fixedHeight = 0;
        }
        d = $.extend(d, getRights());

        if (isAdmin === "N") {
          let name = common.userInfo.username;
          let code = common.userInfo.usercode;
          d.readerName = name;
          d.readerUser = code;
          d.allReader = 0;
        }
        $.ajax({
          url: url,
          method: "post",
          contentType: "application/json;charset=UTF-8",
          data: JSON.stringify(d),
          success: function (res) {
            if (res.success) {
              opt.ref && opt.ref();
              layer.msg(res.message || "保存成功");
              layer.close(wins);
            } else {
              layer.msg(res.message || "保存失败");
            }
          },
        });
        return false;
      });
      //
      $("#homePageEdit #close").on("click", function () {
        layer.close(wins);
      });
    });
  };
});
