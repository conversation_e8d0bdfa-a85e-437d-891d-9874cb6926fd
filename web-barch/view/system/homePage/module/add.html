<style>
    #homePageEdit .layui-row {
        margin: 0;
    }

    #homePageEdit .layui-row::before {
        clear: none;
        content: none;
    }

    #homePageEdit .layui-form-label {
        width: 99px;
        padding: 0;
        line-height: 30px;
    }

    #homePageEdit .layui-input-block {
        margin-left: 99px;
    }

    #homePageEdit .home-block {
        height: 100px;
        display: table;
        position: relative;
    }

    #homePageEdit .home-block .block {
        display: table-cell;
        text-align: center;
        vertical-align: middle;
        background: #e4e4e4;
        height: 100%;
        width: 100%;
    }

    #homePageEdit .home-block .del-block {
        position: absolute;
        top: 0;
        right: 5px;
        cursor: pointer;
        opacity: 0;
    }

    #homePageEdit .home-block:hover .del-block {
        opacity: 1;
    }

    #homePageEdit #addNewBlock {
        width: 100px;
        height: 100px;
        border-radius: 4px;
        border: 1px solid #cccccc;
        text-align: center;
        line-height: 110px;
        cursor: pointer;
    }

    #homePageEdit #addNewBlock i {
        font-size: 34px;
        color: #ccc;
    }

    #homePageEdit #block-info {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.3);
        padding-right: 8px;
        padding-top: 8px;
        box-sizing: border-box;
    }

    #homePageEdit .rights-box .more {
        color: #5260ff;
        cursor: pointer;
    }
</style>
<div class="layui-tab-content" id="homePageEdit">
    <div class="layui-content-box">
        <div class="layui-form">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">门户标题：</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" lay-verify="required" placeholder="请输入10字以内的名称"
                                    autocomplete="off" class="layui-input" maxlength="10" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md12">
                    <div class="layui-col-md12">
                        <div class="layui-form-item">
                            <label class="layui-form-label">使用权限：</label>
                            <div class="layui-input-block" id="IsAdminShow">
                                <div>
                                    <label class="labelCheckbox">
                                        <input type="checkbox" class="self-checkbox-switch" name="allReader"
                                            value="1" />
                                        <div class="self-checkbox-switch-icon"></div>
                                        全院人员
                                    </label>
                                </div>
                                <div class="rights-box none">
                                    <p>
                                        <span class="more set-rights" type="user">+开放给人员:</span>
                                        <span class="user-str"></span>
                                    </p>
                                    <p>
                                        <span class="more set-rights" type="role">+开放给角色:</span>
                                        <span class="role-str"></span>
                                    </p>
                                    <p>
                                        <span class="more set-rights" type="dept">+开放给部门:</span>
                                        <span class="dept-str"></span>
                                    </p>
                                    <p>
                                        <span class="more set-rights" type="group">+开放给群组:</span>
                                        <span class="group-str"></span>
                                    </p>
                                </div>
                            </div>
                            <div id="UserPower" style="padding-top: 4px;"></div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md12">
                    <div class="layui-col-md12">
                        <div class="layui-form-item">
                            <label class="layui-form-label">模块高度一致：</label>
                            <div class="layui-input-block">
                                <div>
                                    <label class="labelCheckbox">
                                        <input type="checkbox" class="self-checkbox-switch" name="fixedHeight"
                                            value="1" />
                                        <div class="self-checkbox-switch-icon"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">首页内容：</label>
                        <div class="layui-input-block">
                            <div id="addNewBlock">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                            </div>
                            <div id="block-info" class="none"></div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">当前状态：</label>
                        <div class="layui-input-block" style="width: 400px;">
                            <div class="layui-row layui-col-space4 home-box"></div>
                        </div>
                    </div>
                </div>
            </div>
            <button class="none" lay-submit lay-filter="homePageEditSave"></button>
        </div>
    </div>
    <div class="archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal" id="save">保存</button>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="close">取消</a>
    </div>
</div>
<script type="text/html" id="blockDataHtml">
    <div class="layui-form-item">
        <label class="layui-form-label">显示内容：</label>
        <div class="layui-input-block">
            <select name="elementType" lay-filter="elementType" lay-search>
                <option value="12">待办(小)</option>
                <option value="1">待办(大)</option>
                <option value="2">热点新闻(小)</option>
                <option value="13">热点新闻(大)</option>
                <option value="3">公文</option>
                <option value="4">日程</option>
                <option value="5">常用流程</option>
                <option value="6">信息管理</option>
                <option value="7">外部系统</option>
                <option value="8">常用入口</option>
                <option value="9">提醒事项</option>
                <option value="10">文档管理</option>
                <option value="11">邮箱</option>
                <option value="14">医疗业务</option>
                <option value="15">流程查阅</option>
                <option value="16">上级通文</option>
            </select>
        </div>
    </div>
    <div class="child-wrap information-wrap none">
        <div class="layui-form-item">
            <label class="layui-form-label">栏目数量：</label>
            <div class="layui-input-block">
                <select name="elementColumn" lay-filter="elementColumn" lay-search>
                    <option value="2">多栏目</option>
                    <option value="1">单栏目</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">显示模式：</label>
            <div class="layui-input-block">
                <select name="elementShow" lay-search>
                    <option value="1">列表模式</option>
                    <option value="2">图文模式</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">栏目名称：</label>
            <div class="layui-input-block">
                <div id="elementChannelBox"></div>
                <!-- <select name="elementChannel"></select> -->
            </div>
        </div>
    </div>
    <div class="child-wrap medical-business-wrap none">
        <div class="layui-form-item">
            <label class="layui-form-label">数据内容：</label>
            <div class="layui-input-block">
                <select name="elementMedicalBusiness" lay-search>
                    <option value="1">业务情况</option>
                    <option value="101">百分制考核情况</option>
                </select>
            </div>
        </div>
    </div>
    <!-- 流程查阅 对应表单 -->
    <div class="child-wrap process-wrap none">
        <div class="layui-form-item">
            <label class="layui-form-label">流程类型：</label>
            <div class="layui-input-block">
                <div id="processType"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">栏目名称：</label>
            <div class="layui-input-block">
                <input type="text" autocomplete="off" id="elementName" name="elementName" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item" style="text-align: right">
        <button class="layui-btn layui-btn-sm" id="saveBlock">确定</button>
        <button class="layui-btn layui-btn-sm layui-border-theme" id="cancleBlock">取消</button>
    </div>
    
    <!-- {{# }else if(item.elementType == 9){ }}
    <div class="layui-col-md3 home-block"
        draggable="true"
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}"
        data-id="{{index}}"
        data-sord="{{item.sord}}"
    >
        <div class="block">待办</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 10){ }}
    <div class="layui-col-md6 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">热点新闻</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div> -->
</script>
<script type="text/html" id="blockTempHtml">
    {{# layui.each(d, function(index, item){ }}
    {{# if(item.elementType == 1){ }}
    <div class="layui-col-md6 home-block"
        draggable="true"
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}"
        data-id="{{index}}"
        data-sord="{{item.sord}}"
    >
        <div class="block">待办</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 2){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">热点新闻</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 3){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">公文</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 4){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">日程</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 5){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">常用流程</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 6){ }} {{# if(item.elementColumn == 2){ }}
    <div class="layui-col-md6 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
    {{# }else{ }}
    <div class="layui-col-md3 home-block" 
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}"
        data-sord="{{item.sord}}"
    >
        {{# } }}
        <div class="block">{{item.elementChannelName || '信息管理'}}</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 7){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">外部系统</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 8){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">常用入口</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
	{{# }else if(item.elementType == 9){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">提醒事项</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
	{{# }else if(item.elementType == 10){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">文档管理</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
	{{# }else if(item.elementType == 11){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">邮箱</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 12){ }}
    <div class="layui-col-md3 home-block"
        draggable="true"
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}"
        data-id="{{index}}"
        data-sord="{{item.sord}}"
    >
        <div class="block">待办</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 13){ }}
    <div class="layui-col-md6 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};"
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">热点新闻</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 14){ }}
    <div class="layui-col-md12 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">{{'医疗业务 — ' + item.elemenModuleName}}</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 16){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">上级通文</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# }else if(item.elementType == 15){ }}
    <div class="layui-col-md3 home-block"
        draggable="true" 
        style="height: {{item.height + 'px'}};" 
        data-index="{{index}}" 
        data-id="{{index}}" 
        data-sord="{{item.sord}}"
    >
        <div class="block">流程查阅</div>
        <span class="del-block"><i class="oaicon oa-icon-cuowu"></i></span>
    </div>
    {{# } }} {{# }) }}
</div>
</script>