'use strict';

define(function(require, exports, module) {
  module.exports = {
    init: init,
    cancle: function() {}
  };

  function init(opt, html) {
    var form = layui.form;
    let oaTree = null, // OA 科室树
      oaTreeData = [], // OA 科室树数据
      hisTree = null, // HIS 科室树 Node
      hisTreeData = [], // HIS 科室数据
      tableNode = null, // 映射表格Node
      renderClassTag = false; // HIS科室名称Class设置
    renderOADeptTree();
    renderHisDeptTree();
    renderTable();

    /**@desc 搜索框输入筛选树 */
    $('#hisDeptMapping [name="oaDeptSearch"]')
      .off('input')
      .on(
        'input',
        (function() {
          let timer = null;
          return function() {
            timer && clearTimeout(timer);
            timer = setTimeout(() => {
              let val = $(this).val();

              oaTree
                ? $.fn.ztreeQueryHandler(oaTree, val)
                : renderHisDeptTree();
            }, 500);
          };
        })()
      );
    $('#hisDeptMapping [name="hisDeptSearch"]')
      .off('input')
      .on(
        'input',
        (function() {
          let timer = null;
          return function() {
            timer && clearTimeout(timer);
            timer = setTimeout(() => {
              let val = $(this).val();

              hisTree
                ? $.fn.ztreeQueryHandler(hisTree, val)
                : renderOADeptTree();
            }, 500);
          };
        })()
      );
    /**@desc 映射表格刷新 */
    $('#hisDeptMapping [name="searchBtn"]')
      .off('click')
      .on('click', function() {
        oaTreeSelectEmpty();
        hisTreeSelectEmpty();
        refresh();
      });
    /**@desc 搜索条件重置 */
    $('#hisDeptMapping [name="resetBtn"]')
      .off('click')
      .on('click', function() {
        $('#hisDeptMapping #mappingTableSearchForm input').val('');
        oaTreeSelectEmpty();
        hisTreeSelectEmpty();
        refresh();
      });
    /**@desc 新增映射 */
    $('#hisDeptMapping #addNewDeptMappingBtn')
      .off('click')
      .on('click', function() {
        $.quoteFun('/system/hisDeptMapping/add', {
          title: '新增映射',
          ref: function() {
            $('#hisDeptMapping #mappingTableSearchForm input').val('');
            oaTreeSelectEmpty();
            hisTreeSelectEmpty();
            refresh();
          }
        });
      });
    /**@desc 滚动条渲染 */
    $.each($('#hisDeptMapping .tree-content'), function(index, dom) {
      $(dom).overlayScrollbars({});
    });

    function refresh() {
      tableNode && tableNode.refresh();
    }
    /**@desc 选中数据 */
    function handleOADeptCheck(event, treeId, treeItem) {
      if (treeItem.id != 'empty') {
        hisTreeSelectEmpty();
        $('#hisDeptMapping #mappingTableSearchForm input').val(treeItem.name);
      } else {
        $('#hisDeptMapping #mappingTableSearchForm input').val('');
      }
      refresh();
    }

    function handleHisDeptCheck(event, treeId, treeItem) {
      if (treeItem.id != 'empty') {
        oaTreeSelectEmpty();
        $('#hisDeptMapping #mappingTableSearchForm input').val(treeItem.name);
      } else {
        $('#hisDeptMapping #mappingTableSearchForm input').val('');
      }
      refresh();
    }

    /**@desc 渲染 OA科室树 HIS科室树 */
    function renderOADeptTree() {
      $.ajax({
        method: 'get',
        url: '/ts-basics-bottom/organization/getTree',
        async: false,
        success: function(data) {
          oaTreeData = [
            {
              id: 'empty',
              name: '全部'
            }
          ].concat(data.object);
          $.fn.zTree.init(
            $('#hisDeptMapping #oaDeptTreeContent'),
            {
              view: {
                dblClickExpand: false,
                showTitle: true
              },
              callback: {
                onClick: handleOADeptCheck
              },
              //勾选时，关联父，不关联子，取消时，关联父子
              check: {
                autoCheckTrigger: true,
                chkStyle: 'checkbox',
                chkboxType: {
                  Y: '',
                  N: ''
                }
              },
              data: {
                simpleData: {
                  enable: true
                }
              }
            },
            oaTreeData
          );
          //获取ztree对象
          oaTree = $.fn.zTree.getZTreeObj('oaDeptTreeContent');
          oaTreeSelectEmpty();
        }
      });
    }

    function renderHisDeptTree() {
      $.ajax({
        method: 'get',
        url: '/ts-external/api/basOrgDepartment/getZTree',
        async: false,
        success: function(data) {
          hisTreeData = [
            {
              id: 'empty',
              name: '全部'
            }
          ].concat(data.object);
          $.fn.zTree.init(
            $('#hisDeptMapping #hisDeptTreeContent'),
            {
              view: {
                dblClickExpand: false,
                showTitle: true
              },
              callback: {
                onClick: handleHisDeptCheck
              },
              //勾选时，关联父，不关联子，取消时，关联父子
              check: {
                autoCheckTrigger: true,
                chkStyle: 'checkbox',
                chkboxType: {
                  Y: '',
                  N: ''
                }
              },
              data: {
                simpleData: {
                  enable: true
                }
              }
            },
            hisTreeData
          );
          //获取ztree对象
          hisTree = $.fn.zTree.getZTreeObj('hisDeptTreeContent');
          hisTreeSelectEmpty();
        }
      });
    }

    function renderTable() {
      tableNode = new $.trasenTable('deptAddressTable', {
        url: '/ts-external/api/deptMapping/list',
        pager: 'deptAddressPage',
        mtype: 'get',
        shrinkToFit: true,
        colModel: [
          {
            label: 'OA科室名称',
            name: 'oaDeptName',
            sortable: false,
            width: 160
          },
          {
            label: 'HIS科室名称',
            name: 'deptMappingChildList',
            sortable: false,
            width: 160,
            formatter: function(cell = [], opt, row) {
              let domList = cell.map(dept => {
                let className = renderClassTag ? 'dark-line' : '';
                renderClassTag = !renderClassTag;
                return `<div class="his-dept-table-item ${className}" title="${dept.hisDeptName}">${dept.hisDeptName ||
                  ''}</div>`;
              });
              return `<div>${domList.join('')}</div>`;
            }
          },
          {
            label: '创建日期',
            name: 'createDate',
            align: 'center',
            sortable: false,
            width: 117,
            fixed: true,
            formatter: function(cell) {
              if(cell) {
                return dayjs(cell).format('YYYY-MM-DD')
              }
              return ''
            }
          },
          {
            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
            name: '',
            sortable: false,
            width: 40,
            fixed: true,
            editable: false,
            resizable: false,
            align: 'center',
            title: false,
            classes: 'visible jqgrid-rownum ui-state-default',
            formatter: function(cell, opt, row) {
              return `<div class="table-more-btn">
                <div class="more-btn">
                  <i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i>
                </div>
                <div class="more-box">
                  <button
                    name="editTableRow"
                    title="编辑"
                    class="layui-btn"
                    row-id="${row.id}"
                  >
                    <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>
                    编辑
                  </button>
                  <button
                    name="deleteTableRow"
                    title="删除"
                    class="layui-btn"
                    row-id="${row.id}"
                  >
                    <i class="fa fa-trash deal_icon"  aria-hidden="true"></i>
                    删除
                  </button>
                </div>
              </div>`;
            }
          }
        ],
        buidQueryParams: function() {
          let data = form.val('mappingTableSearchForm'),
            oaSelectedNodes = (oaTree.getSelectedNodes() || []).filter(
              item => item.id != 'empty'
            ),
            hisSelectedNodes = (hisTree.getSelectedNodes() || []).filter(
              item => item.id != 'empty'
            );

          !hisSelectedNodes.length &&
            oaSelectedNodes[0] &&
            oaSelectedNodes[0].name != data.condition &&
            oaTreeSelectEmpty();

          !oaSelectedNodes.length &&
            hisSelectedNodes[0] &&
            hisSelectedNodes[0].name != data.condition &&
            hisTreeSelectEmpty();

          renderClassTag = false;
          return data;
        }
      });
    }

    $('body')
      .off('click', '#hisDeptMapping [name="deleteTableRow"]')
      .on('click', '#hisDeptMapping [name="deleteTableRow"]', function() {
        let id = $(this).attr('row-id');
        layer.confirm(
          '删除后数据将无法恢复，确定要删除吗？',
          {
            btn: ['确定', '取消'],
            title: '提示',
            closeBtn: 0
          },
          function(index) {
            $.ajax({
              url: `/ts-external/api/deptMapping/delete/${id}`,
              method: 'post',
              async: false,
              success: function(res) {
                if (!res.success) {
                  layer.msg(res.message || '删除失败');
                  return;
                }
                layer.msg('删除成功');
                refresh();
              }
            });
          }
        );
      });
    $('body')
      .off('click', '#hisDeptMapping [name="editTableRow"]')
      .on('click', '#hisDeptMapping [name="editTableRow"]', function() {
        let id = $(this).attr('row-id'),
          data = tableNode.getSourceRowData(id);
        $.quoteFun('/system/hisDeptMapping/add', {
          title: '编辑映射',
          data,
          ref: function() {
            $('#hisDeptMapping #mappingTableSearchForm input').val('');
            oaTreeSelectEmpty();
            hisTreeSelectEmpty();
            refresh();
          }
        });
      });

    function oaTreeSelectEmpty() {
      if (!oaTree) {
        return;
      }
      let oaEpmNode = oaTree.getNodeByParam('id', 'empty');
      oaTree.selectNode(oaEpmNode);
    }

    function hisTreeSelectEmpty() {
      if (!hisTree) {
        return;
      }
      let hisEmpNode = hisTree.getNodeByParam('id', 'empty');
      hisTree.selectNode(hisEmpNode);
    }
  }
});
