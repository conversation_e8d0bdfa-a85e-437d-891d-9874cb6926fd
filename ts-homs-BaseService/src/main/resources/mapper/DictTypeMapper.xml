<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.DictTypeMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.DictType">
		<!-- WARNING - @mbg.generated -->
		<id column="ID" jdbcType="VARCHAR" property="id" />
		<result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
		<result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
		<result column="REMARK" jdbcType="VARCHAR" property="remark" />
		<result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode" />
		<result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
		<result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
		<result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
		<result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
		<result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
		<result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
		<result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
		<result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
		<result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
	</resultMap>
</mapper>