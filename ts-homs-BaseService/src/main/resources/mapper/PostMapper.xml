<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.PostMapper">
	<select id="getList" resultType="cn.trasen.homs.base.bean.PostListResp">
		SELECT a1.*, a2.post_name AS upgradePostName, a3.ITEM_NAME AS postCategoryName
		FROM comm_post AS a1
		LEFT JOIN comm_post a2 ON a2.post_id = a1.upgrade_post_id
		LEFT JOIN comm_dict_item a3 ON a1.post_category = a3.ITEM_CODE and a3.DIC_TYPE_ID='post_category'
		WHERE a1.is_deleted = 'N'
		<if test="postName != null and postName != ''">
		 	AND a1.post_name LIKE CONCAT('%',#{postName},'%')
		</if>
		<if test="postCategory != null and postCategory != ''">
		 	AND a1.post_category=#{postCategory}
		</if>
		<if test="isEnable != null and isEnable != ''">
		 	AND a1.is_enable=#{isEnable}
		</if>
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			AND a1.sso_org_code = #{ssoOrgCode}
		</if>
		order by a1.sort_no
		asc,a1.create_date desc
	</select>

	<select id="getPostType" resultType="java.util.Map">
		SELECT item_code as postId,item_name as postName
		 FROM comm_dict_item
		where DIC_TYPE_ID = 'post_category' and is_deleted = 'N'
	</select>
	<select id="getPostLevel" resultType="java.util.Map">
		select post_category as postId,post_id as postLevelId,post_name as postName
		 from comm_post where IS_DELETED = 'N' and is_enable = '1'
	</select>
</mapper>