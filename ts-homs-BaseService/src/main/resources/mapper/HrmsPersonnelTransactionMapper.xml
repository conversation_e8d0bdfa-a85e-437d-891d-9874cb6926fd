<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.HrmsPersonnelTransactionMapper">
	<resultMap id="BaseResultMap"
		type="cn.trasen.homs.base.model.HrmsPersonnelTransaction">
		<!--
          WARNING - @mbg.generated
        -->
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
		<result column="employee_name" jdbcType="VARCHAR"
			property="employeeName" />
		<result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
		<result column="old_org_id" jdbcType="VARCHAR" property="oldOrgId" />
		<result column="old_org_name" jdbcType="VARCHAR" property="oldOrgName" />
		<result column="new_org_id" jdbcType="VARCHAR" property="newOrgId" />
		<result column="new_org_name" jdbcType="VARCHAR" property="newOrgName" />
		<result column="effective_date" jdbcType="VARCHAR"
			property="effectiveDate" />
		<result column="cause" jdbcType="VARCHAR" property="cause" />
		<result column="execute" jdbcType="VARCHAR" property="execute" />
		<result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR"
			property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="org_name" jdbcType="VARCHAR" property="orgName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>

	<insert id="batchInsert"> INSERT INTO comm_personnel_transaction (
		employee_no, employee_name, old_org_id, old_org_name, new_org_id,
		new_org_name, batch_number, effective_date, cause, execute, remark,
		create_date, create_user, create_user_name, org_id, org_name, is_deleted
		) VALUES <foreach collection="list" item="item" index="index"
			separator=",">
			(
			#{item.employeeNo},
			#{item.employeeName},
			#{item.oldOrgId},
			#{item.oldOrgName},
			#{item.newOrgId},
			#{item.newOrgName},
			#{item.batchNumber},
			#{item.effectiveDate},
			#{item.cause},
			#{item.execute},
			#{item.remark},
			#{item.createDate},
			#{item.createUser},
			#{item.createUserName},
			#{item.orgId},
			#{item.orgName},
			#{item.isDeleted}
			)
		</foreach>

	</insert>

	<!-- 列表查询带权限 -->
	<select id="getDataList"
		parameterType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"
		resultType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"> SELECT
		id,employee_no, employee_name ,employee_id, old_org_id ,old_org_name,
		new_org_id, new_org_name, effective_date, cause, execute ,batch_number,
		remark, enterprise_id ,create_date, create_user, create_user_name,
		update_date ,update_user, update_user_name, org_id , org_name,
		is_deleted FROM comm_personnel_transaction where is_deleted = 'N' <if
			test="sarchDate != null and sarchDate != ''">
			AND effective_date like CONCAT('%',#{sarchDate},'%')
		</if>

		<if
			test="cause != null and cause != ''">
			AND cause =#{cause}
		</if>

		<if
			test="htOrgIdList != null and htOrgIdList != ''">
			AND ((old_org_id in ${htOrgIdList}) or (new_org_id in
		${htOrgIdList}))
		</if>

	</select>


	<select id="getBatchNumber" resultType="java.lang.String"
		parameterType="java.lang.String">
		SELECT MAX(batch_number) FROM comm_personnel_transaction WHERE
		batch_number like CONCAT('%',#{ym},'%')
	</select>

	<select id="loadChangeSelect" resultType="java.lang.String">
		SELECT cause FROM comm_personnel_transaction
		GROUP BY cause
	</select>

	<select id="getList"
		parameterType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"
		resultType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"> SELECT
		id,employee_no, employee_name ,employee_id, old_org_id ,old_org_name,
		new_org_id, new_org_name, effective_date, cause, execute ,batch_number,
		remark, enterprise_id ,create_date, create_user, create_user_name,
		update_date ,update_user, update_user_name, org_id , org_name,
		is_deleted FROM comm_personnel_transaction where is_deleted = 'N' <if
			test="employeeId != null and employeeId != ''">
			AND employee_id =#{employeeId}
		</if>
	</select>

	<select id="getCycleList"
		parameterType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"
		resultType="cn.trasen.homs.base.model.HrmsPersonnelTransaction"> SELECT
		id,employee_no, employee_name ,employee_id, old_org_id ,old_org_name,
		new_org_id, new_org_name, effective_date, cause, execute ,batch_number,
		remark, enterprise_id ,create_date, create_user, create_user_name,
		update_date ,update_user, update_user_name, org_id , org_name,
		is_deleted FROM comm_personnel_transaction where is_deleted = 'N' and
		cause !='新员工入职' <if test="employeeId != null and employeeId != ''">
			AND employee_id =#{employeeId}
		</if>

	</select>


</mapper>