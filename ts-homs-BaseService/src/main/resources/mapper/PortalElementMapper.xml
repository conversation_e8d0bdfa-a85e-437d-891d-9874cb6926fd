<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.dao.PortalElementMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.PortalElement">
		<!--
      WARNING - @mbg.generated
    -->
		<id column="id" jdbcType="VARCHAR" property="id" />
		<result column="theme_id" jdbcType="VARCHAR" property="themeId" />
		<result column="element_type" jdbcType="VARCHAR" property="elementType" />
		<result column="element_column" jdbcType="VARCHAR"
			property="elementColumn" />
		<result column="element_show" jdbcType="VARCHAR" property="elementShow" />
		<result column="element_channel" jdbcType="VARCHAR"
			property="elementChannel" />
		<result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
		<result column="sord" jdbcType="INTEGER" property="sord" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="width_type" jdbcType="VARCHAR" property="widthType" />
		<result column="height_type" jdbcType="VARCHAR" property="heightType" />
	</resultMap>

	<insert id="bacthInsert" parameterType="java.util.List"> 
		insert into toa_portal_element 
		(id, theme_id, element_type, element_column,element_show,element_medical_business,element_channel,is_deleted,sord,create_time,
		create_user,create_user_name,width_type,height_type) 
		values 
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.id,jdbcType=VARCHAR}, #{item.themeId,jdbcType=VARCHAR},
			#{item.elementType,jdbcType=VARCHAR},#{item.elementColumn,jdbcType=VARCHAR},
			#{item.elementShow,jdbcType=VARCHAR},#{item.elementMedicalBusiness},
			#{item.elementChannel,jdbcType=VARCHAR},
			#{item.isDeleted,jdbcType=VARCHAR}, #{item.sord,jdbcType=INTEGER},
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.createUser,jdbcType=VARCHAR},
			#{item.createUserName,jdbcType=VARCHAR},#{item.widthType,jdbcType=VARCHAR}
			,#{item.heightType,jdbcType=VARCHAR})
		</foreach>
	</insert>

</mapper>