<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.CustomEmployeeMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.DictType">
		<!-- WARNING - @mbg.generated -->
		<id column="ID" jdbcType="VARCHAR" property="id" />
		<result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
		<result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
		<result column="REMARK" jdbcType="VARCHAR" property="remark" />
		<result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode" />
		<result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
		<result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
		<result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
		<result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
		<result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
		<result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
		<result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />
		<result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
		<result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
	</resultMap>

	<select id="findByEmployeeInfoId" resultType="java.util.Map" parameterType="cn.trasen.homs.base.model.CustomEmployeeFieldModel">
		select * from ${tableName} s where s.is_deleted = 'N'
		<if test="employeeId!=null and employeeId!=''">

			and s.employee_id = #{employeeId}

		</if>

		<if test="defaultCondition!=null and defaultCondition!=''">
			and ${defaultCondition}
		</if>

		<!-- 工作经历表按生效时间倒序排 -->
		<choose>
			<when test="sortField != null  and sortField != '' ">
				ORDER BY ${sortField} DESC
			</when>
			<otherwise>
				ORDER BY CREATE_DATE DESC
			</otherwise>
		</choose>
		<!-- <if test="tableName =='hrms_work_experience_hospital' "> ORDER BY change_start_date DESC </if> -->
	</select>

	<select id="getEmployeePageListByCustom" resultType="java.util.Map"
			parameterType="cn.trasen.homs.base.model.HrmsEmployee">
		SELECT 
			s.*,
			a1.jobtitleLevelName,
			c.position_name AS positionName ,
			a2.jobtitleName,
			a2.jobtitleCategoryLevel,
			a3.jobtitleCategoryName,
			a1.assessment_date,
			o.name AS orgName,
			a4.start_time AS edStartTime,
			a4.end_time AS edEndTime,
			a4.education_type,
			a4.education_type_name,
			a4.school_name,a4.professional,
			a4.degree_number AS degreeNumber,
			a4.certificate_number AS xlCertificateNumber,
			a2.professional_name AS
			zcProfessionalName,
			a2.certificate_number AS zcCertificateNumber,
			a2.qitazhichen AS zcQitazhichen,
			post.post_name AS
			postName,
			wf.wfCreateUserName,
			wf.wfCreateUserDeptName,
			wf.wfStatus ,
			wf.operationId,
			wf.wfCurrentStepName,
			wf.currentStepNo,
			wf.wfInstanceId,
			wf.workflowNumber
		from hrms_employee s
		left join comm_organization o on s.org_id =	o.organization_id
		left join comm_position c on s.position_id = c.position_id
		left join comm_post post on post.post_id = s.gwdj
		left join (	SELECT info.employee_id,MIN(info.highest_level)
		highest_level,info.jobtitle_level,b.jobtitle_basic_name as jobtitleLevelName,info.assessment_date
		FROM hrms_jobtitle_info info
		inner join comm_jobtitle_basic b on info.jobtitle_level = b.jobtitle_basic_id
		WHERE info.is_deleted = 'N' and info.highest_level=1
		GROUP BY info.employee_id
		)a1 on a1.employee_id = s.employee_id

		left join (	SELECT info.employee_id,MIN(info.highest_level) highest_level,info.jobtitle_name,b.jobtitle_basic_name AS
		jobtitleName,c.jobtitle_basic_name AS jobtitleCategoryLevel , professional_name,certificate_number,qitazhichen
		FROM hrms_jobtitle_info info
		inner join comm_jobtitle_basic b on info.jobtitle_name = b.jobtitle_basic_id
		INNER JOIN comm_jobtitle_basic c ON info.jobtitle_level = c.jobtitle_basic_id
		WHERE info.is_deleted = 'N' and info.highest_level=1
		GROUP BY info.employee_id
		)a2 on a2.employee_id = s.employee_id
		left join (	SELECT info.jobtitle_name,
		info.employee_id,MIN(info.highest_level) highest_level,info.jobtitle_category,b.jobtitle_basic_name as jobtitleCategoryName
		FROM hrms_jobtitle_info info
		inner join comm_jobtitle_basic b on info.jobtitle_category = b.jobtitle_basic_id
		WHERE info.is_deleted = 'N' and info.highest_level=1
		GROUP BY info.employee_id
		)a3 on a3.employee_id= s.employee_id
		left join (	SELECT ed.employee_id,
		ed.school_name,ed.education_type,dict.ITEM_NAME AS education_type_name
		,ed.start_time,ed.end_time,ed.professional,	ed.degree_number,ed.certificate_number
		FROM hrms_education_info ed
		left join comm_dict_item dict on ed.education_type = dict.item_code and dict.dic_type_id = 'education_type'
		WHERE ed.is_deleted ='N' and ed.highest_level=1
		GROUP BY ed.employee_id
		)a4 on a4.employee_id = s.employee_id
		left join (	SELECT	ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,	ceuo.create_user_dept_name AS wfCreateUserDeptName,
		wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,	wf.CURRENT_step_no AS currentStepNo,
		wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
		FROM comm_employee_update_operation ceuo
		INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
		WHERE ceuo.is_deleted='N' AND ceuo.create_date = (	SELECT MAX(t2.create_date)
		FROM comm_employee_update_operation t2
		WHERE t2.is_deleted='N' AND	t2.employee_id = ceuo.employee_id )
		) wf on wf.employee_id=s.employee_id
		where s.is_deleted = 'N' and s.employee_name!='admin' and s.employee_name !='ts' and s.sso_org_code = #{ssoOrgCode}
		<if test="employeeNo!=null and employeeNo!=''">
			and s.employee_no like concat('',#{employeeNo},'%')
		</if>

		<if test="birthdayStartTime!=null and birthdayStartTime!=''">
			and s.birthday >= #{birthdayStartTime}
		</if>

		<if test="birthdayEndTime!=null and birthdayEndTime!=''">
			and #{birthdayEndTime} >=s.birthday

		</if>

		<if test="positiveTimeStartTime!=null and positiveTimeStartTime!=''">
			and s.positive_time >= #{positiveTimeStartTime}
		</if>

		<if test="positiveTimeEndTime!=null and positiveTimeEndTime!=''">
			and #{positiveTimeEndTime} >=s.positive_time
		</if>

		<if test="entryDateStartTime!=null and entryDateStartTime!=''">
			and s.entry_date >= #{entryDateStartTime}
		</if>

		<if test="entryDateEndTime!=null and entryDateEndTime!=''">
			and #{entryDateEndTime} >=s.entry_date
		</if>

		<if test="employeeName!=null and employeeName!='' ">
			and s.employee_name like concat('',#{employeeName},'%')
		</if>

		<if test="gender!=null and gender!=''">
			and s.gender =#{gender}
		</if>

		<if test="identityNumber!=null and identityNumber!=''">
			and s.identity_number like concat('',#{identityNumber},'%')
		</if>

		<if test="phoneNumber!=null and phoneNumber!=''">
			and s.phone_number like concat('',#{phoneNumber},'%')
		</if>

		<if test="empPayroll!=null and empPayroll!=''">
			and s.emp_payroll like concat('',#{empPayroll},'%')
		</if>

		<if test="employeeStatuses != null and employeeStatuses.size() > 0">
			and (s.employee_status in
			<foreach collection="employeeStatuses" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>


		<if test="establishmentTypes != null and establishmentTypes.size() > 0">
			and (s.establishment_type in
			<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="orgAttributestypes != null and orgAttributestypes.size() > 0">
			and (s.org_attributes in
			<foreach collection="orgAttributestypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="employeeCategorys != null and employeeCategorys.size() > 0">
			and (s.employee_category in
			<foreach collection="employeeCategorys" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="politicalStatuses != null and politicalStatuses.size() > 0">
			and (s.political_status in
			<foreach collection="politicalStatuses" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="nationalityes != null and nationalityes.size() > 0">
			and (s.nationality in
			<foreach collection="nationalityes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>


		<if test="marriageStatuses != null and marriageStatuses.size() > 0">
			and (s.marriage_status in
			<foreach collection="marriageStatuses" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>


		<if test="positionNames != null and positionNames.size() > 0">
			and (s.position_id in
			<foreach collection="positionNames" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="personalIdentitys != null and personalIdentitys.size() > 0">
			and (s.personal_identity in
			<foreach collection="personalIdentitys" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="plgws != null and plgws.size() > 0">
			and (s.plgw in
			<foreach collection="plgws" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="operationTypes != null and operationTypes.size() > 0">
			and (s.operation_type in
			<foreach collection="operationTypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="zhuanyeyingcai!=null and zhuanyeyingcai!=''">
			and s.zhuanyeyingcai = #{zhuanyeyingcai}
		</if>
		<if test="shifouzhongcengganbu!=null and shifouzhongcengganbu!=''">
			and s.shifouzhongcengganbu = #{shifouzhongcengganbu}
		</if>
		<if test="shifouxinglinrencai!=null and shifouxinglinrencai!=''">
			and s.shifouxinglinrencai = #{shifouxinglinrencai}
		</if>
		<if test="shifouguipeirenyuan!=null and shifouguipeirenyuan!=''">
			and s.shifouguipeirenyuan = #{shifouguipeirenyuan}
		</if>


		<if test="gwdjs != null and gwdjs.size() > 0">
			and (s.gwdj in
			<foreach collection="gwdjs" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="jobtitleCategory != null and jobtitleCategory.size() > 0">
			and (a3.jobtitle_category in
			<foreach collection="jobtitleCategory" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="jobtitleName != null and jobtitleName.size() > 0">
			and (a3.jobtitle_name in
			<foreach collection="jobtitleName" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="educationTypes != null and educationTypes.size() > 0">
			and (a4.education_type in
			<foreach collection="educationTypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>


		<if test="startAge!=null and startAge!=''">
			and s.emp_age >= #{startAge}
		</if>

		<if test="endAge!=null and endAge !=''">
			and #{endAge} >=s.emp_age
		</if>


		<if test="condition!=null and condition!=''">

			and (
			s.identity_number like concat('%',#{condition},'%')
			or s.phone_number like
			concat('%',#{condition},'%')
			or s.emp_payroll like concat('%',#{condition},'%')
			or s.employee_name like
			concat('%',#{condition},'%')
			or s.employee_no like concat('%',#{condition},'%')
			or s.name_spell like
			concat('%',#{condition},'%')
			or s.name_stroke like concat('%',#{condition},'%')
			)

		</if>


		<if test="createUser!=null and createUser!=''">
			and employee_no =#{createUser}
		</if>

		<if test="orgCodeList != null and orgCodeList.size() > 0">
			and (org_id in
			<foreach collection="orgCodeList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<if test="orgIds != null and orgIds.size() > 0">
			and (org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>

		<!-- 处理单独看某个岗位类别 -->
		<if test="roleByUserCode != null and roleByUserCode.size() > 0">
			and (s.personal_identity in
			<foreach collection="roleByUserCode" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		Group by s.employee_id
	</select>
	
	<select id="getGroupJurisdictionListById" resultType="cn.trasen.homs.base.model.CommEmployeeFieldGroup"
		parameterType="cn.trasen.homs.base.model.CustomEmployeeFieldModel">
		select s.*,a1.is_edit as isEdit from comm_employee_field_group s inner
		join ( select g.field_group_id,c.is_edit from
		comm_group_jurisdiction
		g
		inner join comm_group_jurisdiction_field c on g.id =
		c.group_jurisdiction_id where g.is_deleted
		= 'N' and c.is_deleted='N'
		<if test="userCode !=null and userCode !=''">
			and (g.emp_ids like concat('%',#{userCode},'%') or
			g.emp_ids like
			'%BENREN%' )
		</if>
		and c.field_id is null and c.is_edit = 1 and c.is_process =1 ) a1 on
		s.id = a1.field_group_id
		<if test="groupId!=null and groupId!=''">
			and s.id = #{groupId}
		</if>
		where 1=1 and s.is_deleted='N' order
		by -s.seq DESC ,s.create_date asc
	</select>

	<!-- 获取编辑需要走流程个人信息的字段 -->
	<select id="getEmployeeFieldJurisdictionList" resultType="cn.trasen.homs.base.model.CommEmployeeField"
		parameterType="cn.trasen.homs.base.model.CustomEmployeeFieldModel">
		select s.* from comm_employee_field s inner join ( select
		c.field_id,c.is_edit from comm_group_jurisdiction g inner
		join
		comm_group_jurisdiction_field c on g.id = c.group_jurisdiction_id
		where
		g.is_deleted = 'N' and c.is_deleted='N'
		<if test="userCode !=null and userCode !=''">
			and (g.emp_ids like concat('%',#{userCode},'%') or
			g.emp_ids like
			'%BENREN%')
		</if>
		and c.is_edit = 1 and c.is_process =1 ) a1 on s.id = a1.field_id inner
		join comm_employee_field_group g on s.group_id =
		g.id where 1 =1 and
		s.is_deleted='N' and s.is_disabled = 0 and s.is_hide = 0
		<if test="groupId!=null and groupId!=''">
			and s.group_id = #{groupId}
		</if>
		and (g.is_detailed is null or g.is_detailed = 0)
		order by -s.seq DESC ,s.create_date asc
	</select>


	<select id="findEmployeeDetailInfoByEmployeeId" resultType="java.util.Map"
		parameterType="cn.trasen.homs.base.model.CustomEmployeeFieldModel">
		select * from ${tableName} s where s.is_deleted = 'N'
		<if test="employeeId!=null and employeeId!=''">
			and s.employee_id = #{employeeId}
		</if>
	</select>

	<!-- 由我办理 -->
	<select id="getDataWorkflowByNoDoList" resultType="cn.trasen.homs.base.bean.CustomEmployeeResp"
		parameterType="cn.trasen.homs.base.bean.CustomEmployeeResp">
		select s.id	as operationId, e.employee_no,e.employee_name,e.gender,	o.name as
		orgName, e.identity_number,	e.birthday,e.entry_date,s.create_user_name,s.create_date,wf.CURRENT_STEP_NAME,
		wf.STATUS as wfStatus,s.create_user_dept_name,wf.wf_instance_id,wt.task_id,wt.wf_step_no,wt.wf_step_name 
		from comm_employee_update_operation s 
		inner join hrms_employee e on s.employee_id = e.employee_id 
		left join comm_organization o on e.org_id = o.organization_id 
		inner join wf_instance_info wf on s.id =wf.BUSINESS_ID 
		inner join	wf_task wt on wf.WF_INSTANCE_ID	=wt.WF_INSTANCE_ID 
		where s.is_deleted='N' and s.sso_org_code =	#{ssoOrgCode} and	e.is_deleted ='N'
		<if test="userCode!=null and userCode!=''">
			and wt.ASSIGNEE_NO = #{userCode}
		</if>
		<if test="condition!=null and condition!=''">
			and (e.employee_no like concat('%',#{condition},'%')
			or
			e.employee_name like concat('%',#{condition},'%')
			)
		</if>
		<if test="createUser!=null and createUser!=''">
			and (s.create_user like concat('%',#{createUser},'%')
			or
			s.create_user_name like
			concat('%',#{createUser},'%'))
		</if>
		<if test="startTime!=null and startTime!=''">
			and s.create_date >= #{startTime}
		</if>
		<if test="orgIds != null and orgIds.size()>0">
			and (e.org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
	</select>

	<!-- 我已办理 -->
	<select id="getDataWorkflowIDoList" resultType="cn.trasen.homs.base.bean.CustomEmployeeResp"
		parameterType="cn.trasen.homs.base.bean.CustomEmployeeResp">
		select s.id
		as operationId, e.employee_no,e.employee_name,e.gender,
		e.identity_number,o.name as orgName,
		e.birthday,e.entry_date,s.create_user_name,s.create_date,wf.wf_instance_id,
		wf.current_step_name,wf.status as wfStatus,
		s.create_user_dept_name,wf.handleTime from
		comm_employee_update_operation s inner join hrms_employee e on
		s.employee_id
		= e.employee_id left join comm_organization o on
		e.org_id
		= o.organization_id inner join ( select
		wf.*,whis.FINISHED_DATE as
		handleTime from wf_instance_info wf inner
		join ( select
		his.WF_INSTANCE_ID,his.FINISHED_DATE
		from WF_TASK_HIS his
		where 1=1
		<if test="userCode!=null and userCode!=''">
			and his.ACT_ASSIGNEE_NO = #{userCode}
		</if>
		<if test="handleTime!=null and handleTime!=''">
			and his.FINISHED_DATE >= #{handleTime}
		</if>
		group by
		his.WF_INSTANCE_ID ) whis on wf.WF_INSTANCE_ID =
		whis.WF_INSTANCE_ID )
		wf on s.id = wf.BUSINESS_ID where
		s.is_deleted='N' and s.sso_org_code =
		#{ssoOrgCode} and e.is_deleted
		='N'
		<if test="condition!=null and condition!=''">
			and (e.employee_no like concat('%',#{condition},'%')
			or
			e.employee_name like concat('%',#{condition},'%'))
		</if>
		<if test="createUser!=null and createUser!=''">
			and (s.create_user like concat('%',#{createUser},'%')
			or
			s.create_user_name like
			concat('%',#{createUser},'%'))
		</if>
		<if test="startTime!=null and startTime!=''">
			and s.create_date >= #{startTime}
		</if>
		<if test="wfStatus!=null and wfStatus!=''">
			and wf.status = #{wfStatus}
		</if>
		<if test="orgIds != null and orgIds.size()>0">
			and (e.org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
	</select>

	<!-- 由我发起 -->
	<select id="getDataWorkflowIApplyList" resultType="cn.trasen.homs.base.bean.CustomEmployeeResp"
		parameterType="cn.trasen.homs.base.bean.CustomEmployeeResp">
		select s.id
		as operationId, e.employee_no,e.employee_name,e.gender,
		e.identity_number,o.name as orgName,
		e.birthday,e.entry_date,s.create_user_name,s.create_date,wf.CURRENT_STEP_NAME,wf.STATUS
		as
		wfStatus,s.create_user_dept_name from
		comm_employee_update_operation
		s inner join hrms_employee e on
		s.employee_id =
		e.employee_id left join
		comm_organization o on e.org_id
		= o.organization_id inner join
		wf_instance_info wf on s.id =
		wf.BUSINESS_ID where s.is_deleted='N' and
		s.sso_org_code =
		#{ssoOrgCode} and e.is_deleted ='N'
		<if test="userCode!=null and userCode!=''">
			and s.create_user = #{userCode}
		</if>
		<if test="condition!=null and condition!=''">
			and (e.employee_no like concat('%',#{condition},'%')
			or
			e.employee_name like concat('%',#{condition},'%'))
		</if>
		<if test="createUser!=null and createUser!=''">
			and (s.create_user like concat('%',#{createUser},'%')
			or
			s.create_user_name like
			concat('%',#{createUser},'%'))
		</if>
		<if test="startTime!=null and startTime!=''">
			and s.create_date >= #{startTime}
		</if>
		<if test="wfStatus!=null and wfStatus!=''">
			and wf.status = #{wfStatus}
		</if>
		<if test="orgIds != null and orgIds.size()>0">
			and (e.org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
	</select>

	<select id="getEmployeeMessage" resultType="cn.trasen.homs.base.model.HrmsEmployee"
		parameterType="cn.trasen.homs.base.model.HrmsEmployee">
		SELECT
			s.employee_id,
			i.work_start_date,
			s.birthday,
			s.entry_date 
		FROM
			cust_emp_base s 
			left join cust_emp_info i on s.employee_id = i.info_id
		WHERE
			s.is_deleted = 'N'
	</select>

	<!-- 经开离职原因 -->
	<select id="getJklzyy" resultType="java.util.Map">
		SELECT
		inc.employee_id AS
		employeeId,
		DATE_FORMAT(inc.incident_time, "%Y-%m-%d")
		AS jk_lzsj,
		CONCAT('[',inc.incident_type,']',inc.cause,'/',inc.remark) AS remark
		FROM
		hrms_personnel_incident inc
		WHERE
		inc.is_deleted = 'N'
		AND
		inc.incident_category ='1'
		AND inc.approval_status='4'
	</select>
	<!-- 退休时间 -->
	<select id="getJkTx" resultType="java.util.Map">
		SELECT
		inc.employee_id AS
		employeeId,
		DATE_FORMAT(inc.incident_time, "%Y-%m-%d") AS
		tx_date
		FROM
		hrms_personnel_incident inc
		WHERE inc.is_deleted = 'N'
		AND
		inc.incident_category ='2'
		AND
		inc.approval_status='4'
	</select>

	<!-- 技术档案 -->
	<select id="getEmployeeJsda" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT
		t1.WORKFLOW_NAME,
		t1.WF_FINISHED_DATE,
		t1.WF_INSTANCE_ID,
		t1.WORKFLOW_NO,
		t1.WF_DEFINITION_ID,
		t1.BUSINESS_ID
		FROM
		wf_instance_info t1
		LEFT
		JOIN wf_definition_info t2 ON
		t1.WF_DEFINITION_ID =
		t2.WF_DEFINITION_ID
		LEFT JOIN hrms_employee t3 ON
		t1.CREATE_USER =
		t3.employee_no
		WHERE
		FIND_IN_SET( '8',
		t2.EXPLOIT_CONFIGURATION )
		AND t3.employee_id = #{employeeId}
		ORDER BY
		WORKFLOW_NAME
	</select>

	<select id="getPersonalIdentityValBayEmployeeId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT 
			t2.ITEM_NAME
		FROM
		hrms_employee t1
		LEFT JOIN comm_dict_item t2 ON t1.personal_identity = t2.item_code AND t2.DIC_TYPE_ID='personal_identity' AND t2.IS_DELETED='N'
		WHERE t1.is_deleted = 'N' AND t1.employee_id = #{employeeId} 
		LIMIT 1
	</select>

	<select id="getEmployeeNoByemployeeId" resultType="java.util.Map" parameterType="java.lang.String">
		SELECT
		t1.STATUS,t1.BUSINESS_ID
		FROM wf_instance_info t1
		WHERE WORKFLOW_NO =
		'L_00001'
		AND t1.CREATE_USER = (
		SELECT employee_no FROM cust_emp_base
		WHERE
		IS_DELETED ='N' AND employee_id =#{employeeId}
		)
		ORDER BY
		CREATE_DATE DESC LIMIT 1
	</select>
	<select id="getStorage" resultType="cn.trasen.homs.base.bean.EmployeeStorageData">
		select * from hrms_employee_storage where employee_id =
		#{employeeId}
	</select>

	<!-- 删除暂存的信息 -->
	<delete id="deltedStorage" parameterType="java.lang.String">
		DELETE FROM
		hrms_employee_storage WHERE employee_id= #{employeeId}
	</delete>
	<!-- 保存暂存的信息 -->
	<insert id="storage" parameterType="java.lang.String">
		INSERT INTO
		hrms_employee_storage(id,employee_id,content,storage_date)
		VALUE(#{id},#{employeeId},#{content},DATE_FORMAT(NOW(),'%Y-%m-%d
		%H:%i'))
	</insert>
	<!-- 根据人员id查询流程审核到哪里了 -->
	<select id="getEmployeeTask" resultType="java.util.Map">
		SELECT wf_step_name AS stepName, GROUP_CONCAT(assignee_name) AS
		assigneeNames
		FROM (
		SELECT wf_step_name, assignee_name
		FROM wf_task
		WHERE IS_DELETED='N' AND WF_INSTANCE_ID IN (
		SELECT WF_INSTANCE_ID
		FROM wf_instance_info
		WHERE is_deleted='N' AND create_user=#{employeeNo} AND workflow_no='L_00001'
		)
		) subquery
		GROUP BY wf_step_name LIMIT 1
	</select>
	<select id="getEmpFlowStatus" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT wf.STATUS as
		status,wf.CREATE_DATE as createDate FROM wf_instance_info wf
		WHERE wf.WORKFLOW_NO = 'L_00001'
		AND wf.CREATE_USER = (
			SELECT h.employee_no FROM hrms_employee h
			WHERE h.IS_DELETED ='N' AND h.employee_id =#{employeeId}
		)
		ORDER BY wf.CREATE_DATE DESC
		LIMIT 1
	</select>
	<select id="getAuditInfo" resultType="cn.trasen.homs.base.customEmployee.model.CustomEmployeeUpdateDetail">
		SELECT DISTINCT eud.audit_status,eud.field_name,eud.show_name,eud.create_date as createDate from
		cust_emp_update_detail eud where 1 = 1
		<if test="employeeId !=null and employeeId !=''">
			and eud.employee_id =#{employeeId}
		</if>
		<if test="wfInstanceId!=null and wfInstanceId!=''">
			and eud.workflow_id =#{wfInstanceId}
		</if>

		 and eud.is_deleted = "N" order by eud.create_date asc
	</select>
	<select id="getEmployeePageListGroup" resultType="java.util.Map" parameterType="cn.trasen.homs.base.model.HrmsEmployee">
		SELECT DISTINCT s.*,o.name AS orgName,
		wf.wfCreateUserName,
		wf.wfCreateUserDeptName,
		wf.wfStatus ,
		wf.operationId,
		wf.wfCurrentStepName,
		wf.currentStepNo,
		wf.wfInstanceId,
		wf.workflowNumber
		from
		cust_emp_base s
		<choose>
			<when test="auditStatus !=null and auditStatus!='' and auditStatus != 0">
				left join comm_organization o on s.org_id = o.organization_id
				inner join
				( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
				ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,
				wf.CURRENT_step_no AS currentStepNo,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
				FROM cust_emp_update_op ceuo
				INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
				WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
				FROM comm_employee_update_operation t2
				WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
				<if test="auditStatus !=null and auditStatus!=''" >
					and wf.STATUS = #{auditStatus}
				</if>
				) wf on wf.employee_id=s.employee_id
				<if test="groupIds != null and groupIds.size() > 0">
					INNER join ( SELECT ud.employee_id from cust_emp_update_detail ud where
					ud.group_id in
					<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
					and ud.is_deleted='N'
					)eud on s.employee_id = eud.employee_id
				</if>
			</when>
			<!-- 查询未发起的数据列表-->
			<when test="auditStatus !=null and auditStatus!='' and auditStatus == 0">
				left join comm_organization o on s.org_id = o.organization_id
				left join ( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
				ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,
				wf.CURRENT_step_no AS currentStepNo,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
				FROM cust_emp_update_op ceuo
				INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
				WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
				FROM cust_emp_update_op t2
				WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
				) wf on wf.employee_id=s.employee_id
			</when>
			<otherwise>
				left join comm_organization o on s.org_id = o.organization_id
				left join ( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
				ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,
				wf.CURRENT_step_no AS currentStepNo,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
				FROM cust_emp_update_op ceuo
				INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
				WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
				FROM cust_emp_update_op t2
				WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
				) wf on wf.employee_id=s.employee_id
				<if test="groupIds != null and groupIds.size() > 0">
					left join ( SELECT ud.employee_id from comm_employee_update_detail ud where
					ud.group_id in
					<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
						#{item}
					</foreach>
					and ud.is_deleted='N'
					)eud on s.employee_id = eud.employee_id
				</if>
			</otherwise>
		</choose>

		<!-- 查询审核结果-->
		<if test="auditResult!=null and auditResult!=''">
			inner join
			( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
			ceuo.create_user_dept_name AS wfCreateUserDeptName,
			wfi.STATUS AS wfStatus,ceuo.id AS operationId,wfi.CURRENT_STEP_NAME AS wfCurrentStepName,
			wfi.CURRENT_step_no AS currentStepNo,
			wfi.WF_INSTANCE_ID AS wfInstanceId, wfi.WORKFLOW_NUMBER AS workflowNumber
			FROM cust_emp_update_op ceuo
			INNER JOIN wf_instance_info wfi ON ceuo.id = wfi.BUSINESS_ID and wfi.IS_DELETED = 'N' and wfi.STATUS = 2
			INNER JOIN cust_emp_update_detail ud on ud.employee_id = ceuo.employee_id and ud.audit_status = #{auditResult}
			WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
			FROM cust_emp_update_op t2
			WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
			) wfi on wfi.employee_id=s.employee_id
		</if>
		where s.is_deleted = 'N' and employee_status in ('1','5','6','9','12','99') and s.employee_no not in ('admin','ts') and s.sso_org_code = #{ssoOrgCode}
		<!-- 0表示查询未发起的数据列表-->
		<if test="auditStatus !=null and auditStatus!='' and auditStatus == 0">
			and s.employee_id not in(
			SELECT ceuo.employee_id
			FROM comm_employee_update_operation ceuo
			INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
			WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
			FROM cust_emp_update_op t2
			WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id
			))
		</if>
		<if test="orgId !=null and orgId !=''">
			and s.org_id = #{orgId} 
		</if>
	</select>
	<select id="getHistoricalRecords" resultType="cn.trasen.homs.base.bean.HistoricalListResp">
		SELECT	ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,	ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,	wf.WORKFLOW_NAME AS workflowName,
				wf.CREATE_DATE createDate,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
			FROM comm_employee_update_operation ceuo
			INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
			WHERE ceuo.is_deleted='N' AND ceuo.create_date = (	SELECT MAX(t2.create_date)
				FROM comm_employee_update_operation t2
				WHERE t2.is_deleted='N' AND	t2.employee_id = ceuo.employee_id ) and ceuo.employee_id= #{employeeId}
	</select>
	<select id="getUnFlowHistoricalRecords" resultType="cn.trasen.homs.base.bean.HistoricalListResp">
		SELECT create_date createDate,field_name text from cust_emp_update_detail where employee_id = #{employeeId} and table_name is not null
	</select>
	<select id="getCompleteProgress" resultType="cn.trasen.homs.base.model.CommEmployeeFieldGroup">
		SELECT DISTINCT fg.id,fg.group_name,fg.table_name from cust_emp_group fg
		LEFT JOIN cust_emp_field ef on fg.id=ef.group_id where
		fg.is_deleted = 'N'
	</select>
	<select id="getEmployeePageListReport" resultType="java.util.Map" parameterType="cn.trasen.homs.base.model.HrmsEmployee">
		SELECT DISTINCT s.*,
		o.name AS orgName
		<!-- 查询未发起的数据列表-->
		<if test="auditStatus =='' or auditStatus != 0">
		,
		wf.wfCreateUserName,
		wf.wfCreateUserDeptName,
		wf.wfStatus ,
		wf.operationId,
		wf.wfCurrentStepName,
		wf.currentStepNo,
		wf.wfInstanceId,
		wf.workflowNumber
		</if>
		from cust_emp_base s
		<choose>
			<when test="auditStatus !=null and auditStatus!='' and auditStatus != 0">
				left join comm_organization o on s.org_id = o.organization_id
				inner join
				( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
				ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,
				wf.CURRENT_step_no AS currentStepNo,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
				FROM cust_emp_update_op ceuo
				INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
				WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
				FROM cust_emp_update_op t2
				WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
				<if test="auditStatus !=null and auditStatus!=''" >
					and wf.STATUS = #{auditStatus}
				</if>
				) wf on wf.employee_id=s.employee_id
			</when>
			<!-- 查询未发起的数据列表-->
			<when test="auditStatus !=null and auditStatus!='' and auditStatus == 0">
				left join comm_organization o on s.org_id = o.organization_id
			</when>
			<otherwise>
				left join comm_organization o on s.org_id = o.organization_id
				left join ( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
				ceuo.create_user_dept_name AS wfCreateUserDeptName,
				wf.STATUS AS wfStatus,ceuo.id AS operationId,wf.CURRENT_STEP_NAME AS wfCurrentStepName,
				wf.CURRENT_step_no AS currentStepNo,
				wf.WF_INSTANCE_ID AS wfInstanceId, wf.WORKFLOW_NUMBER AS workflowNumber
				FROM cust_emp_update_op ceuo
				INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
				WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
				FROM cust_emp_update_op t2
				WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
				) wf on wf.employee_id=s.employee_id
			</otherwise>
		</choose>
		<!-- 查询审核结果-->
		<if test="auditResult!=null and auditResult!=''">
			inner join
			( SELECT ceuo.employee_id,ceuo.create_User_Name AS wfCreateUserName,
			ceuo.create_user_dept_name AS wfCreateUserDeptName,
			wfi.STATUS AS wfStatus,ceuo.id AS operationId,wfi.CURRENT_STEP_NAME AS wfCurrentStepName,
			wfi.CURRENT_step_no AS currentStepNo,
			wfi.WF_INSTANCE_ID AS wfInstanceId, wfi.WORKFLOW_NUMBER AS workflowNumber
			FROM cust_emp_update_op ceuo
			INNER JOIN wf_instance_info wfi ON ceuo.id = wfi.BUSINESS_ID and wfi.IS_DELETED = 'N' and wfi.STATUS = 2
			INNER JOIN cust_emp_update_detail ud on ud.employee_id = ceuo.employee_id and ud.audit_status = #{auditResult}
			WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
			FROM cust_emp_update_op t2
			WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id )
			) wfi on wfi.employee_id=s.employee_id
		</if>
		where s.is_deleted = 'N' and employee_status in ('1','5','6','9','12','99') and s.employee_no not in ('admin','ts') and s.sso_org_code = #{ssoOrgCode}
		<if test="employeeName!=null and employeeName!=''">
			and s.employee_name like
			concat('%',#{employeeName},'%')
		</if>
		<if test="orgIds != null and orgIds.size() > 0">
			and (s.org_id in
			<foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<if test="orgId !=null and orgId !=''">
			and s.org_id = #{orgId} 
		</if>
		<!-- 0表示查询未发起的数据列表-->
		<if test="auditStatus !=null and auditStatus!='' and auditStatus == 0">
		   and s.employee_id not in(
			SELECT ceuo.employee_id
			FROM cust_emp_update_op ceuo
			INNER JOIN wf_instance_info wf ON ceuo.id = wf.BUSINESS_ID and wf.IS_DELETED = 'N'
			WHERE ceuo.is_deleted='N' AND ceuo.create_date = ( SELECT MAX(t2.create_date)
			FROM cust_emp_update_op t2
			WHERE t2.is_deleted='N' AND t2.employee_id = ceuo.employee_id
			))
		</if>
	</select>
	<select id="getUnFinishList" resultType="java.lang.Integer">
		SELECT SUM(numbers) AS total FROM (SELECT count(DISTINCT ud.field_name) as numbers
		from comm_employee_update_detail ud where ud.group_id
		in( SELECT DISTINCT fg.id from comm_employee_field_group fg
		LEFT JOIN comm_employee_field ef on fg.id=ef.group_id where
		fg.is_deleted = 'N' and ef.is_must = 1)
		and ud.employee_id =#{employeeId} and ud.audit_status = '1' and
		ud.is_deleted='N' and ud.group_id =#{groupId} and ud.table_name is not null
		<if test="wfInstanceId!=null and wfInstanceId!=''">
			and ud.workflow_id =#{wfInstanceId}
		</if>
		union all SELECT sum(case when ef.show_name is null then 1 else 0 end ) as numbers from
		comm_employee_field ef where ef.group_id in (SELECT DISTINCT fg.id from comm_employee_field_group fg
		LEFT JOIN comm_employee_field ef on fg.id=ef.group_id where
		fg.is_deleted = 'N' and ef.is_must = 1 AND fg.table_name = 'hrms_employee') and ef.is_must =1 and ef.is_hide = '0' and ef.is_deleted='N' AND ef.is_disabled = 0 and
		ef.show_name not in( SELECT ud.show_name from comm_employee_update_detail ud where ud.group_id in
		(SELECT DISTINCT fg.id from comm_employee_field_group fg
		LEFT JOIN comm_employee_field ef on fg.id=ef.group_id where
		fg.is_deleted = 'N' and ef.is_must = 1 AND fg.table_name = 'hrms_employee')
		and ud.audit_status = '1' and ud.employee_id =#{employeeId} and ud.group_id in(SELECT fg.id from comm_employee_field_group fg
		where  fg.table_name = 'hrms_employee')
		<if test="wfInstanceId!=null and wfInstanceId!=''">
			and ud.workflow_id =#{wfInstanceId}
		</if>) )tt
	</select>
	<select id="getEmployeeCloum" resultType="java.util.Map">
		select
		<if test="fieldList != null and fieldList.size() > 0">
			<foreach collection="fieldList" index="index" item="item" open="" separator="," close="">
				case when ${item.fieldName} is not NULL THEN '1' ELSE #{item.showName} END as #{item.fieldName}
			</foreach>
		</if>
		from ${tableName} where employee_id = #{employeeId} and is_deleted = 'N'

	</select>
</mapper>
