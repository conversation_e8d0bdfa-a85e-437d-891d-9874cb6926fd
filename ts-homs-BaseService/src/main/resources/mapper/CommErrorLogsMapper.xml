<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.CommErrorLogsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.homs.base.model.CommErrorLogs">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SERVICE_NAME" jdbcType="VARCHAR" property="serviceName" />
    <result column="CLASS_NAME" jdbcType="VARCHAR" property="className" />
    <result column="METHOD_NAME" jdbcType="VARCHAR" property="methodName" />
    <result column="EXCEPTION_MSG" jdbcType="VARCHAR" property="exceptionMsg" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="STACK_TRACE" jdbcType="LONGVARCHAR" property="stackTrace" />
  </resultMap>
  
   <select id="selectPageList" resultType="cn.trasen.homs.base.model.CommErrorLogs" parameterType="cn.trasen.homs.base.model.CommErrorLogs">
	  	
		  select  *from  comm_error_logs where 1=1
		  <if test="serviceName !=null and serviceName !=''">
		  	and SERVICE_NAME like concat('%',#{serviceName},'%') 
		  </if>
		  <if test="className !=null and className !=''">
		  	and CLASS_NAME like concat('%',#{className},'%') 
		  </if>
		  <if test="methodName !=null and methodName !=''">
		  	and METHOD_NAME like concat('%',#{methodName},'%') 
		  </if>
		  <if test="exceptionMsg !=null and exceptionMsg !=''">
		  	and EXCEPTION_MSG like concat('%',#{exceptionMsg},'%') 
		  </if>
		  <if test="stackTrace !=null and stackTrace !=''">
		  	and STACK_TRACE like concat('%',#{stackTrace},'%') 
		  </if>
		   <if test="createTimeBegin !=null and createTimeBegin !='' and createTimeEnd !=null and createTimeEnd !='' ">
		  	and CREATE_TIME  between  #{createTimeBegin} and  #{createTimeEnd}
		  </if>
	  </select>
	  
</mapper>