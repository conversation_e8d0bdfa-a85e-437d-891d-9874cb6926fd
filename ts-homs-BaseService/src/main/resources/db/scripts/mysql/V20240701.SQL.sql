set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_employee_field' and COLUMN_NAME = 'is_salary' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_employee_field` ADD column `is_salary` int default 0  COMMENT ''是否在薪酬计算中显示'' ',
    'select ''INFO: is_salary 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'TOA_BOARDROOM_APPLY' and COLUMN_NAME = 'attend_employee_input' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`TOA_BOARDROOM_APPLY` ADD column `attend_employee_input` VARCHAR(2000)  COMMENT ''参会人员(手动输入)'' ',
    'select ''INFO: attend_employee_input 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_boardroom' and COLUMN_NAME = 'sort_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_boardroom` ADD column `sort_code` INT  COMMENT ''排序字段'' ',
    'select ''INFO: sort_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_disability_certificate' and COLUMN_NAME = 'storage_location' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_disability_certificate` ADD column `storage_location` VARCHAR(50)  COMMENT ''存放位置'' ',
    'select ''INFO: storage_location 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'dp_table_field' and COLUMN_NAME = 'is_remind' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`dp_table_field` ADD column `is_remind` INT default 0 COMMENT ''是否提醒项 0否  1是'' ',
    'select ''INFO: is_remind 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS  `ts_base_oa`.`toa_sys_access_month`  (
  `id` varchar(50) NOT NULL,
  `login_month` varchar(20)  NULL DEFAULT NULL,
  `login_numbers` int(11) NULL DEFAULT NULL,
  `logins_pc_numbers` int(11) NULL DEFAULT NULL,
  `logins_wx_numbers` int(11) NULL DEFAULT NULL,
  `sso_org_code` varchar(50)  DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_field_set' and COLUMN_NAME = 'sum_field' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_field_set` ADD column `sum_field` VARCHAR(50) COMMENT ''汇总字段'' ',
    'select ''INFO: sum_field 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_account_management' and COLUMN_NAME = 'account_type' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_account_management` ADD column `account_type` VARCHAR(10)  COMMENT ''账号类型'' ',
    'select ''INFO: account_type 字段已存在.''');

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'hrms_employee' and COLUMN_NAME = 'org_attributes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD column `org_attributes` INT default 0 COMMENT ''人员类别'' ',
    'select ''INFO: org_attributes 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'hrms_employee' and COLUMN_NAME = 'job_attributes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD column `job_attributes` INT default 0 COMMENT ''岗位属性'' ',
    'select ''INFO: job_attributes 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_personnel_info' and COLUMN_NAME = 'object_type_attribute' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_personnel_info` ADD column `object_type_attribute` VARCHAR(20)  COMMENT ''对象类型属性:寄养对象(1家庭、2区县、3一次性,7其他),民政对象(4民政、5一次性、6其他)'' ',
    'select ''INFO: object_type_attribute 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS `ts_base_oa`.civil_affairs_account_balance(
    id VARCHAR(36) NOT NULL COMMENT '主键',
    account_management_id VARCHAR(36) COMMENT '代管账号主键',
	  account_balance_before DECIMAL(32,2) COMMENT '变更前账户余额',
    account_balance_after DECIMAL(32,2) COMMENT '变更后账户余额',
		change_date DATE COMMENT '变更日期',
    remarks VARCHAR(100) COMMENT '备注',
    create_date DATETIME COMMENT '创建日期',
    create_user VARCHAR(50) COMMENT '创建人',
    create_user_name VARCHAR(50) COMMENT '创建人姓名',
    update_date DATETIME COMMENT '更新日期',
    update_user VARCHAR(50) COMMENT '更新人',
    update_user_name VARCHAR(50) COMMENT '更新人姓名',
    is_deleted CHAR(1) COMMENT '是否删除',
    sso_org_code VARCHAR(50) COMMENT '机构编码',
    PRIMARY KEY (id)
)ENGINE=InnoDB  COMMENT='余额变更记录表';

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'org_contact_type' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD column `org_contact_type` INT default 1 COMMENT ''科室通讯录类型'' ',
    'select ''INFO: job_attributes 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'comm_organization_contacts' and COLUMN_NAME = 'external_org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_organization_contacts` ADD column `external_org_name` VARCHAR(100) COMMENT ''科室通讯科室名称'' ',
    'select ''INFO: external_org_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_attachment' and COLUMN_NAME = 'collect_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_attachment` ADD column `collect_id` VARCHAR(100) COMMENT ''收藏的附件ID'' ',
    'select ''INFO: collect_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'start_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `start_date` VARCHAR(50) COMMENT ''协议开始日期'' ',
    'select ''INFO: start_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'partya_sign_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `partya_sign_name` VARCHAR(50) COMMENT ''甲方签字代表'' ',
    'select ''INFO: partya_sign_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'partyb_representative_sign' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `partyb_representative_sign` VARCHAR(50) COMMENT ''乙方签字代表'' ',
    'select ''INFO: partyb_representative_sign 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'partyb_relation' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `partyb_relation` VARCHAR(50) COMMENT ''与乙方关系'' ',
    'select ''INFO: partyb_relation 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'partyb_identity_number' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `partyb_identity_number` VARCHAR(50) COMMENT ''乙方签字代表身份证号码'' ',
    'select ''INFO: partyb_identity_number 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'partyb_telephone' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `partyb_telephone` VARCHAR(50) COMMENT ''乙方签字代表联系方式'' ',
    'select ''INFO: partyb_telephone 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_contract_information' and COLUMN_NAME = 'nursing_level' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_contract_information` ADD column `nursing_level` VARCHAR(50) COMMENT ''生活护理级别'' ',
    'select ''INFO: nursing_level 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_change_manage' and COLUMN_NAME = 'sort_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_manage` ADD column `sort_id` int COMMENT ''排序号'' ',
    'select ''INFO: sort_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_residence_information' and COLUMN_NAME = 'remarks' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_residence_information` ADD column `remarks` VARCHAR(1000) COMMENT ''备注'' ',
    'select ''INFO: remarks 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'lockscreen_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD column `lockscreen_time` INT default 30 COMMENT ''锁屏时间（分）'' ',
    'select ''INFO: lockscreen_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_document' and COLUMN_NAME = 'SHARETO_USER_NAME' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document` ADD column `SHARETO_USER_NAME` VARCHAR(2000) COMMENT ''科室文档分享名称'' ',
    'select ''INFO: SHARETO_USER_NAME 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_document' and COLUMN_NAME = 'SHARETO_USER' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document` ADD column `SHARETO_USER` VARCHAR(2000) COMMENT ''科室文档分享人编号'' ',
    'select ''INFO: SHARETO_USER 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_document' and COLUMN_NAME = 'SHARETO_DEPT' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_document` ADD column `SHARETO_DEPT` VARCHAR(2000) COMMENT ''科室文档分享部门编号'' ',
    'select ''INFO: SHARETO_DEPT 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'password_expire' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD column `password_expire` INT default 0 COMMENT ''密码有效期限开关 0关 1开'' ',
    'select ''INFO: password_expire 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'toa_sys_setting' and COLUMN_NAME = 'password_expire_days' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_sys_setting` ADD column `password_expire_days` INT COMMENT ''密码有效期限时间（天）'' ',
    'select ''INFO: password_expire_days 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'hrms_employee' and COLUMN_NAME = 'password_expire_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee` ADD column `password_expire_date` Date COMMENT ''密码到期日期'' ',
    'select ''INFO: password_expire_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns 
		where table_name = 'civil_affairs_change_expenditure' and COLUMN_NAME = 'files' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_expenditure` ADD column `files` VARCHAR(1000) COMMENT ''附件'' ',
    'select ''INFO: files 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('6A756F1AEB814E8FBF7FF6285DA51103', 3, 'C5592BD7EEC7454381AA483F6D06021C', '公文库', '/govDocument/officeDocumentLibrary', 6, 1, 1, '', 'admin', '2024-08-01 07:13:52', 'admin', '2024-08-01 07:13:52', 'ts-platform', '', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('16CEBAD4CD34495D90CFFF4BF411350A', 11, 'DD6F86F734C942D58D7AC8415F6147BF', '流程干预', '/process/intervene', 81, 1, 1, 'oa-icon-banlichayue', 'admin', '2024-06-17 02:54:16', 'admin', '2024-06-20 03:34:16', 'ts-platform', '', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('A24C4B0FAF4448ED8C74096C2921F6C1', 21, 'EC5B866430304A78843C8F66CCA8F368', '匿名意见箱', '/ts-web-hrm/suggestions-box', 6, 1, 0, '', 'admin', '2023-06-29 02:25:04', 'admin', '2023-06-29 02:25:21', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('0B0AB38F5972414AAF69966F6A3B71E0', 2, '7DA1C9606FEA45DBAADAC7C6153830A9', '全部任务', '/pages/task/task-all', 5, 1, 1, 'main-icon-jingfeiluru;#30a15b', 'admin', '2024-07-18 02:07:31', 'admin', '2024-07-18 02:07:31', 'ts-platform-phone', 'ts-mobile-oa', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('1AD1AB8A282740EC849C602351CE6439', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '任务登记', '/ts-web-oa/task/task-registration', 1, 1, 0, '', 'admin', '2024-04-16 08:55:47', 'admin', '2024-04-16 08:58:18', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('1CBFFC66236548BE8799BD4817AF51DE', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '任务督办', '/ts-web-oa/task/task-supervision', 2, 1, 0, '', 'admin', '2024-04-16 08:56:44', 'admin', '2024-04-16 08:57:35', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('51B4ABFF33554E37BBB03836AA5E854F', 2, '7DA1C9606FEA45DBAADAC7C6153830A9', '任务登记', '/pages/task/task-registration', 20, 1, 1, 'main-icon-faqihuodong;#0079fe', 'admin', '2024-07-08 09:20:21', 'admin', '2024-07-09 03:14:20', 'ts-platform-phone', 'ts-mobile-oa', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('5F7F9B7307F14CD98B785FEA8B517BDB', 2, '38F890F5CEBF4FB2A6716A3D187559F3', '任务督办', '#', 18, 1, 0, 'oa-icon-anquanducha', 'admin', '2024-04-16 08:50:58', 'admin', '2024-08-01 07:28:02', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('6E72CD086B3E43768A707C2E16C82D61', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '抄送给我的', '/ts-web-oa/task/task-to-my', 4, 1, 0, '', 'admin', '2024-04-16 08:59:14', 'admin', '2024-04-16 08:59:14', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('6FF5A73E4E494E58A4FF93693D9E5CAD', 2, '7DA1C9606FEA45DBAADAC7C6153830A9', '抄送给我', '/pages/task/task-copy', 23, 1, 1, 'main-icon-hudongjilu;#fe9400', 'admin', '2024-07-08 09:24:25', 'admin', '2024-07-09 03:16:41', 'ts-platform-phone', 'ts-mobile-oa', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('7AB00C141792417CA6BBF6B2DF1B9FFE', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '基础配置', '/ts-web-oa/task/task-setting', 6, 1, 0, '', 'admin', '2024-04-16 09:00:18', 'admin', '2024-04-16 09:00:18', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('7DA1C9606FEA45DBAADAC7C6153830A9', 1, '45ADBFF058CE4982B9DE405D641A42A2', '任务督办', '#', 15, 1, 1, '', 'admin', '2024-07-09 03:14:07', 'admin', '2024-07-09 03:14:07', 'ts-platform-phone', '', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('BDCBE92773564651A884B7B4CE3BE37C', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '全部任务', '/ts-web-oa/task/all-tasks', 5, 1, 0, '', 'admin', '2024-04-16 08:59:40', 'admin', '2024-05-10 08:54:52', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('C78D26F4BC94431CBB9E2878215DE149', 2, '7DA1C9606FEA45DBAADAC7C6153830A9', '任务办理', '/pages/task/task-handling', 22, 1, 1, 'main-icon-huiyishishenqing1;#ff3b30', 'admin', '2024-07-08 09:23:39', 'admin', '2024-07-09 03:16:03', 'ts-platform-phone', 'ts-mobile-oa', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('C96F038E833C40FDA53BAC3681AA4DBF', 21, '5F7F9B7307F14CD98B785FEA8B517BDB', '任务办理', '/ts-web-oa/task/task-handling', 3, 1, 0, '', 'admin', '2024-04-16 08:58:08', 'admin', '2024-04-16 08:58:08', 'ts-platform', '', 'Y', NULL, NULL, NULL, '0', '0', 'N', NULL);
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('D000196A2C514319B4C7B2718F646EB8', 2, '7DA1C9606FEA45DBAADAC7C6153830A9', '任务督办', '/pages/task/task-supervision', 21, 1, 1, 'main-icon-jingfeishiyong;#48d863', 'admin', '2024-07-08 09:22:35', 'admin', '2024-07-09 03:15:44', 'ts-platform-phone', 'ts-mobile-oa', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');
-- INSERT ignore INTO `ts_thps`.`thps_sysmenu` (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('347965F405C746C6959B10DE8316A648', 3, 'F571117165DF4136B5B0BF14E50A3A3F', '附件库', '/document/library', 6, 1, 1, '', 'admin', '2024-07-22 06:12:25', 'admin', '2024-07-25 10:04:15', 'ts-platform', '', 'Y', 'Y', NULL, '', '0', '0', 'N', '01');


-- update `ts_thps`.`thps_sysmenu` set menuname = '我的收文' where id = '0D0AE597A4F541B5BA4819EBEB37A813';

CREATE TABLE IF NOT EXISTS `hrms_newsalary_report_total`  (
  `id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
  `report_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '汇总报表id',
  `col_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段名称',
  `col_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段编码',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `create_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `create_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否删除 N 正常   Y 删除',
  `sso_org_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sso_org_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sort_no` int(11) NULL DEFAULT NULL COMMENT '排序号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UNIQ_ID`(`id`) USING BTREE,
  INDEX `IS_DELETED_IDX`(`is_deleted`) USING BTREE
) ENGINE = InnoDB  COMMENT = '薪酬汇总报表字段配置' ROW_FORMAT = COMPACT;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_report_total' and COLUMN_NAME = 'sort_no' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_report_total` ADD `sort_no` int null default null COMMENT ''排序号'' ',
                   'select ''INFO: bankcardno 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_zpgl_employee' and COLUMN_NAME = 'personnel_category' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_zpgl_employee` ADD `personnel_category` int null default null COMMENT ''人员类别'' ',
                   'select ''INFO: personnel_category 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'comm_employee_field_group' and COLUMN_NAME = 'show_open_by' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_employee_field_group` ADD `show_open_by` int default 0 COMMENT ''员工档案是否默认展开'' ',
                   'select ''INFO: show_open_by 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_field_set' and COLUMN_NAME = 'wrap' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_field_set` ADD `wrap` VARCHAR(2) DEFAULT \'N\' COMMENT ''表单字段换行'' ',
                   'select ''INFO: wrap 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


CREATE TABLE IF NOT EXISTS  ts_base_oa.toa_pis_zb_collection
    (
        id VARCHAR(100) NOT NULL COMMENT '主键',
        org_code VARCHAR(50) COMMENT '机构编码',
        org_name VARCHAR(100) COMMENT '机构名称',
        zbcode VARCHAR(100) COMMENT '指标CODE',
        pname VARCHAR(100) COMMENT '指标名称',
        pdate VARCHAR(100) COMMENT '指标产生日期',
        pvalue VARCHAR(100) COMMENT '指标汇总值',
        zb_count VARCHAR(100) COMMENT '指标产生数据条数',
        yqid VARCHAR(100) COMMENT '院区ID',
        create_date DATETIME COMMENT '创建时间',
        update_date DATETIME COMMENT '更新时间',
        is_deleted VARCHAR(100) COMMENT '删除标志',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='决策系统指标同步详情表';
	
CREATE TABLE  IF NOT EXISTS  ts_base_oa.toa_pis_zb_monitor
    (
        id VARCHAR(100) NOT NULL,
        org_code VARCHAR(50),
        org_name VARCHAR(100),
        content VARCHAR(100),
        zb_count VARCHAR(100),
        create_date VARCHAR(100),
        is_deleted VARCHAR(100),
        pdate VARCHAR(50),
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='决策系统指标同步汇总表';
	
CREATE TABLE   IF NOT EXISTS  ts_base_oa.toa_druid_monitoring_records
    (
        id VARCHAR(36) NOT NULL COMMENT '主键',
        druid_id VARCHAR(36) COMMENT 'druidID',
        name VARCHAR(50) COMMENT '数据源标识',
        url VARCHAR(100) COMMENT 'url',
        db_type VARCHAR(50) COMMENT '数据库类型',
        data_source VARCHAR(100) COMMENT '数据源',
        sql_script text COMMENT 'sql脚本',
        execute_count INT COMMENT '执行数',
        total_time bigint COMMENT '执行时间毫秒',
        max_timespan bigint COMMENT '最慢耗时',
        in_transaction_count INT COMMENT '事务运行次数',
        error_count INT COMMENT '错误数',
        effected_row_count INT COMMENT '更新行数',
        fetch_row_count INT COMMENT '读取行数',
        running_count INT COMMENT '执行中',
        concurrent_max INT COMMENT '最大并发',
        histogram VARCHAR(100) COMMENT '执行时间分布',
        execute_and_result_hold_time_histogram VARCHAR(100) COMMENT '执行+RS时分布',
        fetch_row_count_histogram VARCHAR(100) COMMENT '读取行分布',
        effected_row_count_histogram VARCHAR(100) COMMENT '更新行分布',
        max_timespan_occur_time VARCHAR(50) COMMENT '最后更新时间',
        last_time VARCHAR(50) COMMENT '最后使用时间',
        create_date DATETIME COMMENT '创建日期',
        update_date DATETIME COMMENT '更新日期',
        is_deleted CHAR(1) COMMENT '是否删除',
        service_name VARCHAR(50) COMMENT '服务名称',
        org_code VARCHAR(50) COMMENT '机构编码',
        org_name VARCHAR(50) COMMENT '机构名称',
        PRIMARY KEY (id)
    )
    ENGINE=InnoDB DEFAULT CHARSET=utf8 DEFAULT COLLATE=utf8_general_ci COMMENT='sql监控记录表';
    
CREATE TABLE IF NOT EXISTS `toa_total_duty_schedule`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `duty_one_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人1工号',
  `duty_one_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人1名称',
  `schedule_two_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人2:日程消息id',
  `schedule_leader_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '领导:日程消息id',
  `schedule_one_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人1:日程消息id',
  `leader_person` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班领导',
  `duty_date` date NULL DEFAULT NULL COMMENT '值班时间',
  `leader_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班领导工号',
  `duty_two_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人2名称',
  `duty_two_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班人2工号',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除标示',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人编码',
  `update_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `sso_org_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构名称',
  `leader_two_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班领导2',
  `leader_two_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值班领导2工号',
  `schedule_leader_two_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '领导2:日程消息id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '总值班' ROW_FORMAT = Compact;
	
set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_total_duty_schedule' and COLUMN_NAME = 'leader_two_person' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_total_duty_schedule` ADD `leader_two_person` VARCHAR(50) COMMENT ''值班领导2'' ',
                   'select ''INFO: leader_two_person 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_total_duty_schedule' and COLUMN_NAME = 'leader_two_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_total_duty_schedule` ADD `leader_two_code` VARCHAR(50) COMMENT ''值班领导2工号'' ',
                   'select ''INFO: leader_two_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_total_duty_schedule' and COLUMN_NAME = 'schedule_leader_two_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_total_duty_schedule` ADD `schedule_leader_two_id` VARCHAR(50) COMMENT ''领导2:日程消息id'' ',
                   'select ''INFO: schedule_leader_two_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_employee_become' and COLUMN_NAME = 'syksrq' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_employee_become` ADD `syksrq` Date COMMENT ''试用期开始时间'' ',
                    'select ''INFO: syksrq 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'civil_affairs_change_return' and COLUMN_NAME = 'files' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`civil_affairs_change_return` ADD `files` VARCHAR(1000) COMMENT ''附件'' ',
                    'select ''INFO: files 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'height' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `height` INT default 1 COMMENT ''高度'' ',
                    'select ''INFO: height 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'width' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `width` INT default 1 COMMENT ''宽度'' ',
                    'select ''INFO: width 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'min_height' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `min_height` INT default 1 COMMENT ''最小高度'' ',
                    'select ''INFO: min_height 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'min_width' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `min_width` INT default 1 COMMENT ''最小宽度'' ',
                    'select ''INFO: min_width 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'max_height' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `max_height` INT default 1 COMMENT ''最大高度'' ',
                    'select ''INFO: max_height 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'max_width' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `max_width` INT default 1 COMMENT ''最大宽度'' ',
                    'select ''INFO: max_width 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'xpos' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `xpos` INT default 0 COMMENT ''X坐标'' ',
                    'select ''INFO: xpos 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'ypos' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `ypos` INT default 0 COMMENT ''Y坐标'' ',
                    'select ''INFO: ypos 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'is_draggable' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `is_draggable` INT default 1 COMMENT ''元素是否拖拽  0否  1是'' ',
                    'select ''INFO: is_draggable 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_portal_element' and COLUMN_NAME = 'is_resizable' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_portal_element` ADD `is_resizable` INT default 1 COMMENT ''元素是否缩放  0否 1是'' ',
                    'select ''INFO: is_resizable 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_officaldiction' and COLUMN_NAME = 'IS_HIDE_VALUE' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_officaldiction` ADD `IS_HIDE_VALUE` VARCHAR(2) COMMENT ''是否隐藏（1：是、0：否）'' ',
                    'select ''INFO: IS_HIDE_VALUE 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_boardroom' and COLUMN_NAME = 'message_push' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_boardroom` ADD `message_push` VARCHAR(1000) COMMENT ''消息推送人员'' ',
                    'select ''INFO: message_push 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'toa_boardroom' and COLUMN_NAME = 'message_push_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`toa_boardroom` ADD `message_push_name` VARCHAR(1000) COMMENT ''消息推送人员'' ',
                    'select ''INFO: message_push_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'comm_version' and COLUMN_NAME = 'IS_PUSH' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`comm_version` ADD `IS_PUSH` INT COMMENT ''是否推送消息'' ',
                    'select ''INFO: IS_PUSH 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- set @exist := (select count(1) from information_schema.columns
--               where table_name = 'zt_gov_sendfile' and COLUMN_NAME = 'old_page_office_id' and table_schema = database());
-- set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`zt_gov_sendfile` ADD `old_page_office_id` VARCHAR(50) COMMENT '''' ',
--                     'select ''INFO: old_page_office_id 字段已存在.''');
-- PREPARE stmt FROM @sqlstmt;
-- EXECUTE stmt;


