<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.trasen</groupId>
        <artifactId>ts-homs</artifactId>
        <relativePath/>
        <version>1.0.0</version>
    </parent>
    <groupId>cn.trasen.homs</groupId>
    <artifactId>ts-ams</artifactId>
    <packaging>jar</packaging>

    <name>ts-ams</name>
    <url>http://maven.apache.org</url>
    <properties>
        <!-- 文件拷贝时的编码-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 编译时的编码 这里就是你运行项目，会给你的文件进行编码-->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <project.build.locales>zh_CN</project.build.locales>
        <project.build.jdk>1.8</project.build.jdk>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.trasen</groupId>
            <artifactId>ts-homs-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.trasen</groupId>
            <artifactId>ts-homs-feign</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>cn.trasen</groupId>
            <artifactId>ts-homs-tools</artifactId>
        </dependency>-->
        <dependency>
            <groupId>cn.trasen</groupId>
            <artifactId>ts-homs-workflow</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.trasen</groupId>
            <artifactId>ts-homs-form</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-websockets-jsr</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>
        <!--	<dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
            </dependency>-->

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!--nacos-discovery -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>
        <!-- 图片缩略图 -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <!-- 添加PageOffice依赖（必须  -->
        <dependency>
            <groupId>com.zhuozhengsoft</groupId>
            <artifactId>pageoffice</artifactId>
            <version>4.6.0.4</version>
        </dependency>
        <!--<dependency>
            <groupId>com.zhuozhengsoft</groupId>
            <artifactId>pageoffice</artifactId>
            <version>6.0.0.13-javax</version>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!--人大金仓数据库驱动-->
        <dependency>
            <groupId>com.kingbase8</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>
        <!-- WebService -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.2.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.2.4</version>
        </dependency>
        <!--		<dependency>-->
        <!--			<groupId>nz.ac.waikato.cms.weka</groupId>-->
        <!--			<artifactId>weka-stable</artifactId>-->
        <!--			<version>3.8.6</version>-->
        <!--		</dependency>-->
    </dependencies>
    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>/template/**</include>
                    <include>/templates/**</include>
                    <include>/pageoffice/**</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- 统一UTF-8编码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${maven.compiler.encoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--			<plugin>-->
            <!--				<groupId>org.mybatis.generator</groupId>-->
            <!--				<artifactId>mybatis-generator-maven-plugin</artifactId>-->
            <!--				<version>1.3.6</version>-->
            <!--				<configuration>-->
            <!--					<configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>-->
            <!--					<overwrite>true</overwrite>-->
            <!--					<verbose>true</verbose>-->
            <!--				</configuration>-->
            <!--				<dependencies>-->
            <!--					<dependency>-->
            <!--						<groupId>mysql</groupId>-->
            <!--						<artifactId>mysql-connector-java</artifactId>-->
            <!--						<version>${mysql.version}</version>-->
            <!--					</dependency>-->
            <!--					<dependency>-->
            <!--						<groupId>cn.trasen</groupId>-->
            <!--						<artifactId>mapper-generate-plugin</artifactId>-->
            <!--						<version>0.0.3</version>-->
            <!--					</dependency>-->
            <!--				</dependencies>-->
            <!--			</plugin>-->
            <!--			&lt;!&ndash;打包jar文件时，配置manifest文件，加入lib包的jar依赖 &ndash;&gt;-->
            <!--			<plugin>-->
            <!--				<groupId>org.apache.maven.plugins</groupId>-->
            <!--				<artifactId>maven-jar-plugin</artifactId>-->
            <!--				<version>2.6</version>-->
            <!--				<configuration>-->
            <!--					<archive>-->
            <!--						<addMavenDescriptor>false</addMavenDescriptor>-->
            <!--						<manifest>-->
            <!--							<mainClass>cn.trasen.ams.AMSApplication</mainClass>-->
            <!--							<addClasspath>true</addClasspath>-->
            <!--							<classpathPrefix>../lib</classpathPrefix>-->
            <!--							<useUniqueVersions>false</useUniqueVersions>-->
            <!--						</manifest>-->
            <!--						<manifestEntries>-->
            <!--							<Class-Path>.</Class-Path>-->
            <!--							<Built-By>${user.name}</Built-By>-->
            <!--							<Build-Jdk>${java.version}</Build-Jdk>-->
            <!--						</manifestEntries>-->
            <!--					</archive>-->
            <!--				</configuration>-->
            <!--			</plugin>-->
            <!--			&lt;!&ndash; 拷贝依赖的jar包到lib目录 &ndash;&gt;-->
            <!--			<plugin>-->
            <!--				<groupId>org.apache.maven.plugins</groupId>-->
            <!--				<artifactId>maven-dependency-plugin</artifactId>-->
            <!--				<version>3.2.0</version>-->
            <!--				<executions>-->
            <!--					<execution>-->
            <!--						<id>copy</id>-->
            <!--						<phase>package</phase>-->
            <!--						<goals>-->
            <!--							<goal>copy-dependencies</goal>-->
            <!--						</goals>-->
            <!--						<configuration>-->
            <!--							<outputDirectory>${project.build.directory}/lib</outputDirectory>-->
            <!--						</configuration>-->
            <!--					</execution>-->
            <!--				</executions>-->
            <!--			</plugin>-->
        </plugins>
    </build>
</project>
