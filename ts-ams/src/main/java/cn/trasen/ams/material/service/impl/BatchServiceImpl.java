package cn.trasen.ams.material.service.impl;

import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.homs.core.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.BatchMapper;
import cn.trasen.ams.material.model.Batch;
import cn.trasen.ams.material.service.BatchService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BatchServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午11:55:09
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class BatchServiceImpl implements BatchService {

    @Autowired
    private BatchMapper mapper;

    @Autowired
    private SerialNoGenService serialNoGenService;

    @Transactional(readOnly = false)
    @Override
    public Integer save(Batch record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Batch record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Batch record = new Batch();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Batch selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Batch> getDataSetList(Page page, Batch record) {
        Example example = new Example(Batch.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Batch> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    public String genFlowNo() {
        return serialNoGenService.genByDate("PC");
    }

    @Transactional(readOnly = false)
    @Override
    public void batchInsert(List<Batch> batchList) {
        // 参数校验
        if (batchList == null || batchList.isEmpty()) {
            throw new IllegalArgumentException("批次列表不能为空");
        }

        // 获取当前用户信息
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        Date currentDate = new Date();

        // 为每个批次对象设置必要的属性
        for (Batch batch : batchList) {
            // 设置主键ID（如果为空）
            if (StringUtil.isEmpty(batch.getBatchNo())) {
                batch.setId(IdGeneraterUtils.nextId());
            }
            if (StringUtil.isEmpty(batch.getBatchNo())) {
                batch.setBatchNo(genFlowNo());
            }
            if (batch.getInbId() == null) {
                throw new IllegalArgumentException("创建批次必须填充入库单号");
            }
            // 设置创建和更新时间
            batch.setInbId(batch.getInbId());
            batch.setCreateDate(currentDate);
            batch.setUpdateDate(currentDate);

            // 设置删除标志
            batch.setIsDeleted(Contants.IS_DELETED_FALSE);

            // 设置用户相关信息
            if (user != null) {
                batch.setCreateUser(user.getUsercode());
                batch.setCreateUserName(user.getUsername());
                batch.setUpdateUser(user.getUsercode());
                batch.setUpdateUserName(user.getUsername());
                batch.setSsoOrgCode(user.getCorpcode());
                batch.setSsoOrgName(user.getOrgName());
                batch.setDeptId(user.getDeptId());
                batch.setDeptName(user.getDeptname());
            }
        }

        // 执行批量插入
        mapper.batchInsert(batchList);
    }

    @Transactional(readOnly = false)
    @Override
    public void deleteByInbId(String inbId) {
        // 物理删除
        Example example = new Example(Batch.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inbId", inbId);
        mapper.deleteByExample(example);
    }
}
