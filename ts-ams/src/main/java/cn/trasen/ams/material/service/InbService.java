package cn.trasen.ams.material.service;

import cn.trasen.ams.material.bean.inb.InbInsertReq;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Inb;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbService
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 */
public interface InbService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    Integer save(Inb record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    Integer update(Inb record);


    void insert(InbInsertReq record);

    void edit(InbInsertReq record);

    void rollbackConfirm(String inbId);

    void rollbackBatchConfirm(List<String> inbIdList);

    void confirm(String inbId);

    void batchConfirm(List<String> inbIdList);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Inb
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    Inb selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Inb>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    DataSet<Inb> getDataSetList(Page page, Inb record);

    void dataFmt(Inb record);
}
