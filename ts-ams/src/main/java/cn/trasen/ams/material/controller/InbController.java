package cn.trasen.ams.material.controller;

import cn.trasen.ams.material.bean.inb.InbInsertReq;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.service.InbDtlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.service.InbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbController
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 */
@RestController
@Api(tags = "InbController")
public class InbController {

    private transient static final Logger logger = LoggerFactory.getLogger(InbController.class);

    @Autowired
    private InbService inbService;

    @Autowired
    private InbDtlService inbDtlService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveInb
     * @Description 新增
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/material/inb/save")
    public PlatformResult<String> saveInb(@RequestBody InbInsertReq record) {
        try {
            inbService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateInb
     * @Description 编辑
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/material/inb/update")
    public PlatformResult<String> updateInb(@RequestBody InbInsertReq record) {
        try {
            inbService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Inb>
     * @Title selectInbById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/material/inb/{id}")
    public PlatformResult<InbInsertReq> selectInbById(@PathVariable String id) {
        try {
            InbInsertReq record = new InbInsertReq();
            Inb inb = inbService.selectById(id);
            List<InbDtl> inbDtlList = inbDtlService.getInbDtlListByInbId(inb.getId());

            record.setInb(inb);
            record.setInbDtlList(inbDtlList);


            return PlatformResult.success(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteInbById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/material/inb/delete/{id}")
    public PlatformResult<String> deleteInbById(@PathVariable String id) {
        try {
            inbService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Inb>
     * @Title selectInbList
     * @Description 查询列表
     * @date 2025年7月31日 上午9:49:53
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/material/inb/list")
    public DataSet<Inb> selectInbList(Page page, Inb record) {
        return inbService.getDataSetList(page, record);
    }
}
