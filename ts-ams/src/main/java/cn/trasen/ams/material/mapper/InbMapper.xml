<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.InbMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.Inb">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="ware_id" jdbcType="VARCHAR" property="wareId" />
    <result column="supply_id" jdbcType="VARCHAR" property="supplyId" />
    <result column="purch_id" jdbcType="VARCHAR" property="purchId" />
    <result column="purch_name" jdbcType="VARCHAR" property="purchName" />
    <result column="purch_date" jdbcType="TIMESTAMP" property="purchDate" />
    <result column="inv_no" jdbcType="VARCHAR" property="invNo" />
    <result column="inv_date" jdbcType="TIMESTAMP" property="invDate" />
    <result column="inb_date" jdbcType="TIMESTAMP" property="inbDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_consume" jdbcType="CHAR" property="isConsume" />
    <result column="apply_dept_id" jdbcType="VARCHAR" property="applyDeptId" />
    <result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="stat" jdbcType="CHAR" property="stat" />
    <result column="return_stat" jdbcType="CHAR" property="returnStat" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="print_stat" jdbcType="CHAR" property="printStat" />
    <result column="doer_id" jdbcType="VARCHAR" property="doerId" />
    <result column="doer_name" jdbcType="VARCHAR" property="doerName" />
    <result column="doer_time" jdbcType="TIMESTAMP" property="doerTime" />
    <result column="cancel_user_id" jdbcType="VARCHAR" property="cancelUserId" />
    <result column="cancel_user_name" jdbcType="VARCHAR" property="cancelUserName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_date" jdbcType="DATE" property="createDate" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <select id="getList">
    
  </select>
</mapper>