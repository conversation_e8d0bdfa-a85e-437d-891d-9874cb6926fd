import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  getAllDeptList() {
    return request.get(`${apiConfig.system()}/dept/deptlist`, {
      params: datas,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc  获取科室**/
  getDeptList(api, params) {
    return request.post(`${apiConfig.basics()}${api}`, params);
  },
  getPersonByDept(params) {
    return request.post(
      `${apiConfig.basics()}/employee/getEmployeeAllPageList`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
