import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取巡查分类列表**/
  getPatrolTypeList(params) {
    return request.post(`${apiConfig.oa()}/api/patrol/listTree`, params);
  },
  /**@desc  获取巡查表单**/
  getPatrolForm(id) {
    return request.get(`${apiConfig.oa()}/api/patrol/${id}`, {
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc  保存、编辑巡查表单（save/update）**/
  savePatrolForm(type, params) {
    return request.post(`${apiConfig.oa()}/api/patrolStart/${type}`, params, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      },
      custom: {
        showLoading: true,
        loadingText: '提交中...'
      }
    });
  },
  /**@desc  获取巡查管理列表**/
  getPatrolManagementList(params) {
    return request.get(`${apiConfig.oa()}/api/patrolStart/list`, {
      params: params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc  获取巡查管理详情**/
  getPatrolDetail(id) {
    return request.get(`${apiConfig.oa()}/api/patrolStart/selectById/${id}`, {
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc  获取巡查管理详情日志**/
  getPatrolLogList(id) {
    return request.get(
      `${apiConfig.oa()}/api/getCheckOperationLogsByCheckId/${id}`,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  deletePatrol(id) {
    return request.post(`${apiConfig.oa()}/api/patrolStart/delete/${id}`);
  }
};
