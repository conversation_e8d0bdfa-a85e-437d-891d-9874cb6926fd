import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取巡查分类列表**/
  getPoliticalTypeList(params) {
    return request.post(`${apiConfig.oa()}/api/political/listTree`, params);
  },
  /**@desc  获取巡查表单**/
  getPoliticalForm(id) {
    return request.get(`${apiConfig.oa()}/api/political/${id}`, {
      custom: {
        showLoading: true
      }
    });
  },
  /**@desc  保存、编辑巡查表单（save/update）**/
  savePoliticalForm(type, params) {
    return request.post(
      `${apiConfig.oa()}/api/politicalStart/${type}`,
      params,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        },
        custom: {
          showLoading: true,
          loadingText: '提交中...'
        }
      }
    );
  },
  /**@desc  获取巡查管理列表**/
  getPoliticalManagementList(params) {
    return request.get(`${apiConfig.oa()}/api/politicalStart/list`, {
      params: params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc  获取巡查管理详情**/
  getPoliticalDetail(id) {
    return request.get(
      `${apiConfig.oa()}/api/politicalStart/selectById/${id}`,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  /**@desc  获取巡查管理详情日志**/
  getPoliticalLogList(id) {
    return request.get(
      `${apiConfig.oa()}/api/getCheckOperationLogsByCheckId/${id}`,
      {
        custom: {
          showLoading: true
        }
      }
    );
  },
  deletePolitical(id) {
    return request.post(`${apiConfig.oa()}/api/politicalStart/delete/${id}`);
  }
};
