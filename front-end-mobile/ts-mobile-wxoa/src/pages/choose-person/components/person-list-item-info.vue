<template>
  <view class="person-list-item-info">
    <view class="left">
      <image
        class="person-head-image"
        v-if="person.empHeadImg"
        :src="person.empHeadImg"
        mode="aspectFill"
      ></image>
      <view
        v-else
        class="person-head-image"
        :class="person.sex | sexClassFilter"
      >
        {{ person.name | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view class="person-name">{{ person.name }}</view>
      <view class="person-description">
        {{ person.deptName }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'person-list-item-info',
  props: {
    person: {
      type: Object,
      default() {
        return {};
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/ellipsis.scss';
.person-list-item-info {
  display: flex;
}
.right {
  flex: 1;
  padding-left: $uni-spacing-row-base;
}
.person-head-image {
  width: $uni-img-size-lg;
  height: $uni-img-size-lg;
  border-radius: 50%;
  background-color: $u-bg-color;
  text-align: center;
  line-height: $uni-img-size-lg;
  font-size: $uni-font-size-base;
  color: $uni-text-color-inverse;
}
.sex-man {
  background-color: $sexman-color;
}
.sex-woman {
  background-color: $sexwoman-color;
}
.person-name {
  font-size: $uni-font-size-base;
  color: $u-main-color;
}
.person-description {
  color: $u-content-color;
  font-size: $uni-font-size-sm;
  @include ellipsis;
}
</style>
