export default {
  data() {
    return {
      title: '发起活动',
      fromPage: '',
      politicalLocation: '',
      politicalFormList: [],
      formList: [
        {
          title: '活动日期',
          propVal: 'checkDate',
          type: 'select',
          mode: 'time',
          params: {
            year: true,
            month: true,
            day: true
          },
          filed: 'yy-MM-dd',
          placeholder: '请选择活动日期',
          required: true
        }
      ],
      form: {
        status: 0,
        politicalName: '',
        politicalId: '',
        checkDate: ''
      },
      rules: {
        checkDate: [
          {
            required: true,
            message: '请选择活动日期',
            trigger: ''
          }
        ],
        inspectedDepartmentName: [
          {
            required: true,
            message: '请选择处理科室',
            trigger: ''
          }
        ]
      },
      operationButtons: {
        cancel: {
          name: '取消',
          type: 'politicalCancel',
          colorClass: 'gray-color',
          isShow: false
        },
        emporaryStorage: {
          name: '暂存',
          type: 'politicalSubmit',
          status: '0',
          colorClass: 'normal-color',
          isShow: false
        },
        submit: {
          name: '提交',
          type: 'politicalSubmit',
          status: '2',
          colorClass: 'normal-color',
          isShow: false
        }
      },
      showButtons: [],
      isSubmit: false
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    if (opt && opt.politicalId) {
      this.getPoliticalForm(opt.politicalId);
    } else {
      this.title = '编辑活动信息';
      this.getPoliticalDetail(opt.formId);
    }
  },
  methods: {
    //查询活动设置详情，用于新增
    async getPoliticalForm(id) {
      await this.ajax.getPoliticalForm(id).then(async res => {
        this.form.politicalName = res.object.politicalName;
        this.form.politicalId = res.object.id;
        this.form.checkDate = this.$dayjs().format('YYYY-MM-DD');
        this.politicalLocation = res.object.location;
        await this.initData(res.object.itemList);
        this.setOperationButtons(res.object);
      });
    },
    //查询活动详情，用于编辑
    async getPoliticalDetail(id) {
      await this.ajax.getPoliticalDetail(id).then(async res => {
        this.form.politicalName = res.object.politicalName;
        this.form.politicalId = res.object.politicalId;
        this.form.id = res.object.id;
        this.form.checkDate =
          res.object.checkDate || this.$dayjs().format('YYYY-MM-DD');
        this.politicalLocation = res.object.location;
        await this.initData(res.object.itemList);
        this.setOperationButtons(res.object);
      });
    },
    async initData(itemList) {
      this.politicalFormList = await Promise.all(
        itemList.map(async item => {
          let itemObj = {};
          itemObj.politicalItemName = item.politicalItemName;
          itemObj.politicalItemId = item.id;
          itemObj.rectificationFollowUp = item.rectificationFollowUp;
          Object.assign(itemObj, await this.formatForm(item.itemChildList));
          return itemObj;
        })
      );
      this.$nextTick(() => {
        this.$refs.baseForm.$refs.uForm.setRules(this.rules);
        //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
        this.politicalFormList.map(item => {
          this.$refs[
            `baseForm_${item.politicalItemId}`
          ][0].$refs.uForm.setRules(item.formRule);
        });
      });
    },
    //格式化活动表单数据
    async formatForm(list) {
      let formData = {},
        formRule = {},
        formList = await Promise.all(
          list.map(async item => {
            let obj = await this.formatField(item),
              fieldObj = {
                title: item.itemChildDescribe,
                id: item.id,
                required: item.itemChildRequired == 'Y'
              };
            Object.assign(fieldObj, obj.fieldAttr);
            Object.assign(formData, obj.fieldData);
            Object.assign(formRule, obj.fieldRule);
            return fieldObj;
          })
        );
      return {
        formList,
        formData,
        formRule
      };
    },
    async formatField(item) {
      let _self = this,
        fieldAttr = {},
        fieldData = {},
        fieldRule = {};
      fieldAttr.labelPosition = 'top';
      fieldAttr.itemChildId = item.itemChildId || item.id;
      switch (item.itemChildType) {
        case 'DX':
          fieldAttr.type = 'select';
          fieldAttr.mode = 'select'; //选择模式，“select”-弹出层单选模式，“checkbox”-下拉多选模式
          fieldAttr.placeholder = `请选择${item.itemChildDescribe}`;
          //单选
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = `${item.id}`;
          fieldAttr.optionList = [];
          let selectOption = item.itemChildContent.split(',');
          fieldAttr.optionList = selectOption.map(one => {
            return {
              label: one,
              value: one
            };
          });

          fieldData[`${item.id}_nameVal`] = item.checkValue || '';
          fieldData[`${item.id}`] = item.checkValue || '';

          //设置字段校验规则
          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'FX':
          fieldAttr.type = 'select';
          fieldAttr.mode = 'checkbox';
          fieldAttr.placeholder = `请选择${item.itemChildDescribe}`;
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = `${item.id}`;
          fieldAttr.optionList = [];
          let checkboxOption = item.itemChildContent.split(',');
          fieldAttr.optionList = checkboxOption.map(one => {
            return {
              label: one,
              value: one,
              checked: item.checkValue
                ? item.checkValue.split(',').some(i => {
                    return one == i;
                  })
                : false,
              disabled: false
            };
          });

          fieldData[`${item.id}_nameVal`] = item.checkValue || '';
          fieldData[`${item.id}`] = item.checkValue || '';

          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'SRK':
          fieldAttr.type = 'text';
          fieldAttr.placeholder = `请输入${item.itemChildDescribe}`;
          fieldAttr.propVal = item.id;

          fieldData[`${item.id}`] = item.checkValue || '';

          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'FJ':
          fieldAttr.baseType = 'base'; //附件上传模式，“base”-businessId关联文件模式，“fileList”-上传文件列表模式
          fieldAttr.type = 'file';
          fieldAttr.mode = 'image'; //文件类型，“all”-全部文件类型，“image”-仅图片类型
          fieldAttr.placeholder = `${item.itemChildDescribe}` || '请上传附件';
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = item.id;
          fieldAttr.fileList = `${item.id}_fileVal`;
          fieldAttr.name = 'file'; //上传文件的字段名，供后端获取使用
          fieldAttr.header = {
            token: _self.$store.state.common.token
          };
          fieldAttr.action = '/ts-basics-bottom/fileAttachment/upload?moduleName=oa';
          fieldAttr.deletAction =
            '/ts-basics-bottom/fileAttachment/deleteFileId';
          let businessId = item.checkValue || _self.$tools.guid();
          fieldAttr.formData = {
            businessId
          };

          fieldData[`${item.id}`] = businessId;
          let list = await _self.getFileList(businessId);
          fieldData[`${item.id}_nameVal`] = [...list.fileList, ...list.imgList];
          fieldData[`${item.id}_fileVal`] = [...list.fileList, ...list.imgList];

          fieldRule[`${item.id}_nameVal`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}_nameVal`].push({
              type: 'array',
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
      }
      return {
        fieldAttr,
        fieldData,
        fieldRule
      };
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { fileList: [], imgList: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(async res => {
          await Promise.all(
            res.object.map(i => {
              let item = {
                url: `${this.$store.state.common.baseHost}${i.realPath}`,
                fileUrl: i.realPath,
                fkFileId: i.id,
                fkFileName: i.originalName
              };
              if (/gif|jpg|jpeg|png|bmp/i.test(i.fileExtension.toLowerCase())) {
                listObj.imgList.push(item);
              } else listObj.fileList.push(item);
            })
          );
        });
      return listObj;
    },
    setOperationButtons(info) {
      if (info.status == 0) {
        this.$set(this.operationButtons['cancel'], 'isShow', true);
        this.$set(this.operationButtons['emporaryStorage'], 'isShow', true);
        this.$set(this.operationButtons['submit'], 'isShow', true);
      } else if (info.status == 1 || info.status == 2) {
        this.$set(this.operationButtons['cancel'], 'isShow', true);
        this.$set(this.operationButtons['submit'], 'isShow', true);
      }
      this.showButtons = [];
      Object.keys(this.operationButtons).forEach(item => {
        if (this.operationButtons[item]['isShow']) {
          this.showButtons.push(this.operationButtons[item]);
        }
      });
    },
    async onClick(item) {
      await this[item.type](item.status);
    },
    politicalCancel() {
      uni.navigateBack();
    },
    async politicalSubmit(status) {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      let datas = this.submitFormatDatas(status);
      if (datas) {
        datas.status = status;
        let type = datas.id ? 'update' : 'save';
        await this.ajax
          .savePoliticalForm(type, datas)
          .then(res => {
            uni.reLaunch({
              url: `/pages/political/political-management-list/index?status=${status}`
            });
          })
          .catch(e => {
            this.isSubmit = false;
          });
      }
    },
    submitFormatDatas(status) {
      let list = this.politicalFormList;
      let itemList = [];
      for (var i = 0; i < list.length; i++) {
        if (status == 2) {
          let valid = this.$refs[
            `baseForm_${list[i].politicalItemId}`
          ][0].validate();
          if (!valid) {
            this.isSubmit = false;
            return false;
          }
        }
        let itemObj = {
          politicalItemId: list[i].politicalItemId,
          politicalItemName: list[i].politicalItemName
        };
        itemObj.itemChildList = list[i].formList.map(item => {
          return {
            itemChildId: item.itemChildId,
            itemChildName: item.title,
            checkValue: list[i]['formData'][item.id]
          };
        });
        itemList.push(itemObj);
      }
      return {
        ...this.form,
        ...{ itemList }
      };
    }
  }
};
