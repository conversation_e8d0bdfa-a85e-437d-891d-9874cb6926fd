export default {
  data() {
    return {
      fromPage: '',
      keywords: '',
      scrollPage: {
        num: 0,
        size: 10000
      },
      list: []
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
  },
  methods: {
    search(val) {
      this.keywords = val;
      this.$nextTick(() => {
        this.datasInit();
        this.$refs.mescroll.downCallback();
      });
    },
    clear() {},
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getPoliticalTypeList({
          condition: this.keywords
        })
        .then(res => {
          successCallback(res.object);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    clickCollapseItem(id) {
      uni.navigateTo({
        url: `/pages/political/political-reporting/index?politicalId=${id}`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    }
  }
};
