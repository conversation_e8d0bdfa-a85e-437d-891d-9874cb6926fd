<template>
  <view class="calender-content">
    <div class="calender-title">
      <u-icon name="arrow-left" @click="handleCheckLastMonth"></u-icon>
      <text @click="handleShowYearCheckModal">
        {{ calenderTitle }}
      </text>
      <u-icon name="arrow-right" @click="handleCheckNextMonth"></u-icon>
    </div>
    <div class="calender-line-title">
      <div
        v-for="(item, index) of columnList"
        :key="index"
        class="calender-item"
      >
        {{ item }}
      </div>
    </div>
    <div
      ref="thisMonthCalender"
      class="calender-box"
      :class="{ 'shrink-calender-box': isShrink }"
      :style="{ '--marginTop': shrinkTop + 'px' }"
    >
      <div
        v-for="(list, index) of thisMonthCalender"
        :key="index"
        class="calender-line"
      >
        <div
          v-for="item of list"
          :key="item.value"
          @click="checkThisDay(index, item)"
          class="calender-item"
          :class="{
            'selected-item': selectTime == item.value,
            'not-this-month': item.month != month,
            'is-today': $dayjs().format('YYYY-MM-DD') == item.value
          }"
        >
          {{ $dayjs().format('YYYY-MM-DD') == item.value ? '今' : item.label }}
        </div>
      </div>
    </div>
    <div class="shrink-row" v-if="showShrink" @click="isShrink = !isShrink">
      <u-icon name="arrow-up" :class="{ 'not-shrink': isShrink }"></u-icon>
    </div>

    <u-picker
      mode="time"
      v-model="showYearMonthPicker"
      :default-time="defaultTime"
      :params="{ year: true, month: true, day: false }"
      :safe-area-inset-bottom="true"
      :end-year="
        $dayjs()
          .add(50, 'year')
          .format('YYYY')
      "
      @confirm="handleComfirm"
    ></u-picker>
  </view>
</template>

<script>
export default {
  name: 'trasen-calender',
  props: {
    type: {
      type: String,
      default: () => 'date'
    },
    showShrink: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {
      year: 1997,
      month: '01',
      showYearMonthPicker: false,

      columnList: ['日', '一', '二', '三', '四', '五', '六'],
      selectTime: '',
      thisMonthCalender: [],
      lastMonthCalender: [],
      nextMonthCalender: [],

      isShrink: false,
      shrinkTop: 0
    };
  },
  computed: {
    calenderTitle: function() {
      return this.year + '年' + this.month + '月';
    },
    defaultTime: function() {
      return this.year + '-' + this.month;
    }
  },
  watch: {
    year: function() {
      this.handleYearOrMonthChange();
    },
    month: function() {
      this.handleYearOrMonthChange();
    },
    selectTime: function(val) {
      this.resetActiveWeekLine();

      this.$emit('change', val);
    },
    isShrink(val) {
      if (val) {
        let activeLine = this.$refs.thisMonthCalender.querySelector(
          '.calender-line .selected-item'
        ).parentNode;
        this.shrinkTop = -activeLine.offsetTop;
      } else {
        this.shrinkTop = 0;
      }
    }
  },
  created() {
    let dateList = this.$dayjs()
      .format('YYYY-MM-DD')
      .split('-');
    this.year = dateList[0];
    this.month = dateList[1];
    this.selectTime = this.$dayjs().format('YYYY-MM-DD');
  },
  methods: {
    // 打开年月选择
    handleShowYearCheckModal() {
      this.showYearMonthPicker = true;
    },
    // 确认年月选择
    handleComfirm({ year, month }) {
      this.year = year;
      this.month = month;
    },
    //上一月
    handleCheckLastMonth() {
      let newDate = this.$dayjs(this.year + '-' + this.month)
        .subtract(1, 'month')
        .format('YYYY-MM')
        .split('-');
      this.year = newDate[0];
      this.month = newDate[1];
    },
    // 下一月
    handleCheckNextMonth() {
      let newDate = this.$dayjs(this.year + '-' + this.month)
        .add(1, 'month')
        .format('YYYY-MM')
        .split('-');
      this.year = newDate[0];
      this.month = newDate[1];
    },
    //处理年或者月改变，重新计算应该显示的东西
    handleYearOrMonthChange() {
      const nowDayLabel = this.year + '-' + this.month,
        lastMonthList = this.$dayjs(nowDayLabel)
          .subtract(1, 'month')
          .format('YYYY-MM')
          .split('-'),
        nextMonthList = this.$dayjs(nowDayLabel)
          .add(1, 'month')
          .format('YYYY-MM')
          .split('-');

      let thisMonthCalender = this.computedCalender(this.year, this.month),
        lastMonthCalender = this.computedCalender(
          lastMonthList[0],
          lastMonthList[1]
        ),
        nextMonthCalender = this.computedCalender(
          nextMonthList[0],
          nextMonthList[1]
        );
      this.thisMonthCalender = thisMonthCalender;
      this.lastMonthCalender = lastMonthCalender;
      this.nextMonthCalender = nextMonthCalender;

      //重置样式
      this.resetActiveWeekLine();
    },
    resetActiveWeekLine() {
      this.$nextTick(() => {
        let dayLineList = this.$refs.thisMonthCalender.querySelectorAll(
          '.calender-line'
        );
        for (let i = 0; i < dayLineList.length; i++) {
          dayLineList[i].classList.remove('active-line');
        }
        this.$refs.thisMonthCalender
          .querySelector('.calender-line .selected-item')
          .parentNode.classList.add('active-line');
      });
    },
    //计算日历
    computedCalender(year, month) {
      const nowDayLabel = year + '-' + month,
        thisMonthDays = this.$dayjs(nowDayLabel).daysInMonth(),
        firstDayWeekend = this.$dayjs(nowDayLabel + '-' + '01').day();
      let thisMonthDaysList = [];
      for (let i = 1; i <= thisMonthDays; i++) {
        let dateLabel = this.$dayjs(nowDayLabel + '-' + i).format('YYYY-MM-DD');
        thisMonthDaysList.push({
          value: dateLabel,
          label: i,
          year: this.year,
          month: this.month,
          day: i,
          weekend: this.$dayjs(dateLabel).day()
        });
      }
      if (firstDayWeekend > 0) {
        let count = firstDayWeekend;
        for (let i = 1; i <= count; i++) {
          let date = this.$dayjs(nowDayLabel + '-01')
              .subtract(i, 'day')
              .format('YYYY-MM-DD'),
            dateList = date.split('-');
          thisMonthDaysList.unshift({
            value: date,
            label: Number(dateList[2]),
            year: dateList[0],
            month: dateList[1],
            day: Number(dateList[2])
          });
        }
      }
      if (this.$dayjs(nowDayLabel + '-' + thisMonthDays).day() < 6) {
        let count = 6 - this.$dayjs(nowDayLabel + '-' + thisMonthDays).day();
        for (let i = 1; i <= count; i++) {
          let date = this.$dayjs(nowDayLabel + '-' + thisMonthDays)
              .add(i, 'day')
              .format('YYYY-MM-DD'),
            dateList = date.split('-');
          thisMonthDaysList.push({
            value: date,
            label: Number(dateList[2]),
            year: dateList[0],
            month: dateList[1],
            day: Number(dateList[2])
          });
        }
      }

      thisMonthDaysList;
      let calenderList = [];
      thisMonthDaysList.forEach((item, index) => {
        let count = Math.ceil((index + 1) / 7) - 1;
        calenderList[count]
          ? calenderList[count].push(item)
          : (calenderList[count] = [item]);
      });

      return calenderList;
    },
    checkThisDay(index, item) {
      this.selectTime = item.value;

      if (item.month != this.month || this.year != item.year) {
        this.year = item.year;
        this.month = item.month;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.calender-content {
  background-color: #fff;
}
.calender-title {
  height: 44px;
  line-height: 44px;
  font-weight: 600;
  color: $u-main-color;
  display: flex;
  justify-content: center;
  font-size: 36rpx;
  .u-icon {
    font-size: 22px;
    color: $u-tips-color;
    &:first-child {
      margin-right: 40rpx;
    }
    &:last-child {
      margin-left: 40rpx;
    }
  }
}
.calender-box {
  max-height: 100vh;
  overflow: hidden;
  position: relative;
  transition: all 0.5s;
  > .calender-line:first-child {
    margin-top: var(--marginTop);
  }
}
.shrink-calender-box {
  max-height: 14.285vw;
  .active-line {
    background-color: transparent;
  }
}
.calender-line-title {
  display: flex;
  font-weight: 600;
  font-size: 30rpx;
}
.calender-line {
  display: flex;
  font-size: 36rpx;
  transition: all 0.5s;
}
.active-line {
  background-color: #e5e7ff;
}
.calender-item {
  width: 14.285vw;
  height: 14.285vw;
  display: flex;
  align-items: center;
  justify-content: center;
}
.not-this-month {
  color: $u-light-color;
}
.is-today {
  color: $u-type-primary;
  font-weight: 600;
  background-color: #e5e7ff;
  border-radius: 2px;
}
.selected-item {
  background-color: $u-type-primary;
  color: #fff;
  border-radius: 2px;
  font-weight: 400;
}
.shrink-row {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a0a8b4;
  .u-icon {
    transition: all 0.3s;
  }
}
.not-shrink {
  transform: rotate(180deg);
}
</style>
