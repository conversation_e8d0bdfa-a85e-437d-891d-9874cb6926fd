export default {
  data() {
    return {
      showDayPick: true,
      showDeptChoose: false,
      dept: {},
      deptSearchName: '',
      deptList: [],
      personList: []
    };
  },
  onLoad() {
    Object.keys(this.dept).length
      ? null
      : (this.dept = {
          name: this.$store.state.common.userInfo.orgName,
          id: this.$store.state.common.userInfo.orgId
        });
  },
  methods: {
    async refresh() {
      let personRes = await this.ajax.getDeptPersonList({
        orgId: this.dept.id
      });
      this.personList = personRes.object.map(item => {
        return { ...item, shiftList: [] };
      });

      let date =
        (this.$refs.calender && this.$refs.calender.selectTime) ||
        this.$dayjs().format('YYYY-MM-DD');
      let scheduleSearchData = {
        employeeIds: personRes.object.map(item => item.employeeId),
        empOrgId: this.dept.id,
        startDate: date,
        endDate: date
      };

      let scheduleRes = await this.ajax.getScheduleList(scheduleSearchData);
      let scheduleList = scheduleRes.object;
      scheduleList.forEach(item => {
        let personIndex = this.personList.findIndex(
          person => person.employeeId == item.employeeId
        );

        if (personIndex >= 0) {
          this.personList[personIndex].shiftList.push(item);
        }
      });
    },
    handleShowDeptCheck() {
      this.showDeptChoose = true;
      this.deptList = [];
      this.deptSearchName = '';
      this.handleSearchDept();
    },
    handleSearchDept() {
      this.ajax
        .getDeptSimpleList({
          eqName: this.deptSearchName
        })
        .then(res => {
          this.deptList = res.rows
            .filter(item => item.employeeNum > 0)
            .map(item => {
              return { ...item, id: item.code };
            });
        });
    },
    handleSelectDept(item) {
      this.dept = item;
      this.showDeptChoose = false;
      this.refresh();
    }
  }
};
