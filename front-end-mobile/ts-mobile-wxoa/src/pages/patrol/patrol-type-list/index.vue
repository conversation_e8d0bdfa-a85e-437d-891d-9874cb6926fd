<template>
  <view class="ts-container">
    <u-navbar title="选择巡查" title-bold :custom-back="goBack"></u-navbar>
    <view class="search-box">
      <u-search
        shape="square"
        v-model="keywords"
        :animation="true"
        action-text="取消"
        placeholder="输入关键词搜索"
        @search="search"
        @clear="clear"
      ></u-search>
    </view>
    <view class="patrol-list-box">
      <mescroll
        ref="mescroll"
        :page="scrollPage"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <u-collapse
          :item-style="{
            'background-color': '#ffffff',
            'border-bottom': '1px solid #eeeeee'
          }"
          :head-style="{
            padding: '20rpx 10rpx 20rpx 30rpx'
          }"
        >
          <u-collapse-item v-for="item in list" :key="item.id" :open="true">
            <template #title>
              <view class="collapse-patrol-title">
                {{ item.name }}
              </view>
            </template>
            <view class="collapse-patrol-body">
              <view
                class="collapse-patrol-item"
                v-for="one in item.children"
                :key="`${item.pid}-${one.id}`"
                @click="clickCollapseItem(one.id)"
              >
                {{ one.name }}
              </view>
            </view>
          </u-collapse-item>
        </u-collapse>
      </mescroll>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  name: 'patrol-list',
  mixins: [index],
  components: {
    mescroll
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
@import '@/assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.search-box {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.patrol-list-box {
  flex: 1;
  position: relative;
}
.collapse-patrol-title {
  border-left: $uni-spacing-row-xsm solid $uni-color-primary;
  padding-left: $uni-spacing-row-base;
}
.collapse-patrol-body {
  border-top: 1px solid $uni-border-color;
  color: $uni-text-color;
  font-size: $uni-font-size-base;
}
.collapse-patrol-item {
  padding: 20rpx 56rpx;
  position: relative;
  color: $uni-text-content-color;
  font-size: $uni-font-size-base;
  line-height: 1;
  &::after {
    position: absolute;
    bottom: 0;
    left: $uni-spacing-row-lg;
    right: 0;
    height: 1px;
    transform: scaleY(0.5);
    background-color: $uni-border-color;
    content: '';
  }
  &:last-child {
    &::after {
      height: 0;
    }
  }
}
</style>
