<script>
export default {
  onLaunch: function() {
    this.$onGlobalStateChange((state = {}, prev = {}) => {
      let { event, data: newVal } = state,
        oldVal = prev.data || {};
      if (event == 'baseInformChange') {
        this.$store.commit('changeState', newVal);
      }
    });
  },
  onShow: function() {},
  onHide: function() {}
};
</script>

<style lang="scss">
@import '@trasen-oa/trasen-uview-ui/index.scss';
@import './common/css/uni.css';
@import './common/css/iconfont.css';
page {
  background-color: #f7f8f8;
  height: 100%;
  font-size: 28rpx;
  line-height: 1.8;
}

.uni-page-head-ft {
  margin-right: 20rpx;
}
.uni-toast {
  font-size: 28rpx;
}
.uni-icon {
  font-size: 40rpx;
}
.uni-list .uni-navigate-right {
  padding-right: 60rpx;
}
.uni-tabbar .uni-tabbar__icon {
  width: 20px;
  height: 20px;
}
.uni-page-head .uni-btn-icon {
  overflow: unset;
}
uni-button[type='primary'] {
  background-color: #005bac;
}
.uni-input-placeholder,
.uni-textarea-placeholder {
  color: #bbb;
}
.oa-icon-xlsx,
.oa-icon-xls {
  color: #207245;
}
.oa-icon-html,
.oa-icon-txt {
  color: #0071c5;
}
.oa-icon-doc,
.oa-icon-docx {
  color: #2a5699;
}
.oa-icon-png,
.oa-icon-img,
.oa-icon-jpg,
.oa-icon-jpeg,
.oa-icon-video,
.oa-icon-gif {
  color: #ea9518;
}
.oa-icon-rar,
.oa-icon-zip {
  color: #733781;
}
.oa-icon-pdf {
  color: #a33639;
}
.oa-icon-ppt,
.oa-icon-pptx {
  color: #d24625;
}
.oa-icon-unfile {
  color: #999;
}
.oa-icon-wenjianjia {
  color: #ffd641;
}
</style>
