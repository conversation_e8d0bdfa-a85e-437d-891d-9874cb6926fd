<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsKnowledgeLikeMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsKnowledgeLike">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_knowledge_like_id" jdbcType="VARCHAR" property="pkKnowledgeLikeId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_by_name" jdbcType="VARCHAR" property="createByName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_by_name" jdbcType="VARCHAR" property="updateByName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="fk_knowledge_base_id" jdbcType="VARCHAR" property="fkKnowledgeBaseId"/>
        <result column="fk_user_id" jdbcType="VARCHAR" property="fkUserId"/>
    </resultMap>

    <sql id="KnowledgeLikeColums">
        pk_knowledge_like_id,
        create_by,
        create_by_name,
        create_time,
        update_by,
        update_by_name,
        update_time,
        delete_status,
        fk_knowledge_base_id,
        fk_user_id
    </sql>

    <update id="updateKnowledgeLike">
        update ws_knowledge_like set
        update_by = #{updateBy},
        update_time = #{updateTime},
        delete_status = #{deleteStatus}
        where pk_knowledge_like_id = #{pkKnowledgeLikeId}
    </update>

    <select id="selectOneByIdAndFkUserId" resultType="cn.trasen.worksheet.module.entity.WsKnowledgeLike">
        select
        <include refid="KnowledgeLikeColums"/>
        from ws_knowledge_like
        where
        delete_status = 0 and
        fk_user_id = #{fkUserId} and
        fk_knowledge_base_id = #{fkKnowledgeBaseId}

    </select>
</mapper>