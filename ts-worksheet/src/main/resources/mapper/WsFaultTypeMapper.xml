<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsFaultTypeMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsFaultType">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="PK_FAULT_TYPE_ID" jdbcType="VARCHAR" property="pkFaultTypeId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DELETA_STATUS" jdbcType="TINYINT" property="deletaStatus"/>
        <result column="FULL_PATH" jdbcType="VARCHAR" property="fullPath"/>
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId"/>
        <result column="CATEGORY_NAME" jdbcType="VARCHAR" property="categoryName"/>
        <result column="FAULT_STATUS" jdbcType="VARCHAR" property="faultStatus"/>
    </resultMap>

    <sql id="falutTypeColums">
        pk_fault_type_id
        ,
        create_by,
        create_time,
        update_by,
        update_time,
        delete_status,
        full_path,
        parent_id,
        category_name,
        fault_status,
        fk_dept_id

    </sql>
    <insert id="insertFaultType">
        insert into ws_fault_type(<include refid="falutTypeColums"/>)
        values(
        #{pkFaultTypeId},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{fullPath},
        #{parentId},
        #{categoryName},
        #{faultStatus},
        #{fkDeptId}
        )

    </insert>

    <update id="updateFaultType">
        update ws_fault_type
        set update_by     = #{updateBy},
            update_time   = #{updateTime},
            full_path     = #{fullPath},
            parent_id     = #{parentId},
            category_name = #{categoryName},
            fault_status  = #{faultStatus},
            fk_dept_id = #{fkDeptId}
        where pk_fault_type_id = #{pkFaultTypeId}

    </update>

    <update id="updateBatchStatus">
        UPDATE ws_fault_type
        set fault_status = #{faultStatus}
        where pk_fault_type_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <update id="deleteBatch">
        UPDATE ws_fault_type
        set delete_status = 1
        where pk_fault_type_id in
        (
        <foreach collection="list" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>


    <delete id="deleteFaultType">
        update ws_fault_type
        set delete_status = 1
        where pk_fault_type_id = #{pkFaultTypeId}

    </delete>

    <select id="selectFaultTypePageList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultTypeListOutVo">
        select
        <include refid="falutTypeColums"/>
        ,b.fk_user_id
        from ws_fault_type a
        left join ( select GROUP_CONCAT(fk_user_id) fk_user_id,fk_fault_type_id from ws_fault_man GROUP BY
        fk_fault_type_id) b
        on a.pk_fault_type_id = b.fk_fault_type_id
        where delete_status = 0 and a.fk_dept_id = #{fkDeptId}
        <if test="null != categoryName and '' != categoryName">
            and category_name like concat('%',#{categoryName},'%')
        </if>
        <if test="null != list ">
            and pk_fault_type_id in
            (<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
    </select>

    <select id="selectOneById" resultType="cn.trasen.worksheet.module.entity.WsFaultType">
        select
        <include refid="falutTypeColums"/>
        ,b.fk_user_id
        from ws_fault_type a
        left join ( select GROUP_CONCAT(fk_user_id) fk_user_id,fk_fault_type_id from ws_fault_man GROUP BY
        fk_fault_type_id) b
        on a.pk_fault_type_id = b.fk_fault_type_id
        where pk_fault_type_id = #{pkFaultTypeId}
    </select>

    <select id="selectFaultTypeAllList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo">
        select
        a.pk_fault_type_id id,
        a.pk_fault_type_id code,
        a.full_path fullPath,
        a.parent_id pid,
        a.category_name name,
        GROUP_CONCAT( b.fk_user_id ) peopleIds
        from ws_fault_type a
        LEFT JOIN ws_fault_man b ON a.pk_fault_type_id = b.fk_fault_type_id
        where delete_status = 0

        <if test="null != fkDeptId and '' != fkDeptId">
            and a.fk_dept_id = #{fkDeptId}
        </if>
        <if test=' "0" !=status'>
            and fault_status = #{status}
        </if>
        <if test="null != categoryName and '' != categoryName">
            and a.category_name like concat('%',#{categoryName},'%')
        </if>
        GROUP BY a.pk_fault_type_id
    </select>
    <select id="faultTypePeopleIsNullCounts" resultType="java.lang.Integer">
        SELECT case when count( 1 ) = count( c.fk_user_id ) then 1 else 0 end
        FROM ws_fault_type b
                 LEFT JOIN ws_fault_man c ON b.pk_fault_type_id = c.fk_fault_type_id
        WHERE b.fk_dept_id = #{fkDeptId} and b.delete_status = 0
    </select>
    <select id="selectFaultTypeAllListContainsDisable"
            resultType="cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo">
        select pk_fault_type_id id,
               pk_fault_type_id code,
               full_path        fullPath,
               parent_id        pid,
               category_name    name
        from ws_fault_type
        where delete_status = 0
    </select>
    <select id="selectOneByCategoryNameAndParentId" resultType="cn.trasen.worksheet.module.entity.WsFaultType">
        select
        <include refid="falutTypeColums"/>
        from ws_fault_type a
        <where>
            delete_status = 0 and
            category_name = #{categoryName} and
            fk_dept_id = #{fkDeptId} and
            <choose>
                <when test="null != parentId and '' != parentId ">
                    parent_id = #{parentId}
                </when>
                <otherwise>
                    parent_id is null
                </otherwise>
            </choose>
        </where>
    </select>
    <select id="getFaultTypeAllList"
            resultType="cn.trasen.worksheet.module.entity.WsFaultType">
        select
        <include refid="falutTypeColums"/>
        from ws_fault_type a
        <where>
            delete_status = 0
        </where>

    </select>
</mapper>