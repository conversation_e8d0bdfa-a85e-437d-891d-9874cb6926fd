<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsFileFileMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsFileFile">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_ws_file_id" jdbcType="VARCHAR" property="pkWsFileId"/>
        <result column="work_number" jdbcType="VARCHAR" property="workNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="fk_file_id" jdbcType="VARCHAR" property="fkFileId"/>
        <result column="file_suffix" jdbcType="VARCHAR" property="fileSuffix"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
    </resultMap>

    <sql id="fileColums">
        pk_ws_file_id
        ,
        work_number,
    create_by,
    create_time,
    update_by,
    update_time,
    delete_status,
    remark,
    fk_file_id,
    fk_file_name,
    file_suffix,
    file_url,
    fk_customet_log_id,
    fk_ws_task_id
    </sql>
    <insert id="insertFile">
        insert into ws_file_file (<include refid="fileColums"/>)
        values(
        #{pkWsFileId},
        #{workNumber},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{remark},
        #{fkFileId},
        #{fkFileName},
        #{fileSuffix},
        #{fileUrl},
        #{fkCustometLogId},
        #{fkWsTaskId}
        )

    </insert>
    <insert id="insertBatchFile">
        insert into ws_file_file (<include refid="fileColums"/>)
        VALUES
        <foreach collection="list" item="file" separator=",">
            (
            #{file.pkWsFileId},
            #{file.workNumber},
            #{file.createBy},
            #{file.createTime},
            #{file.updateBy},
            #{file.updateTime},
            #{file.deleteStatus},
            #{file.remark},
            #{file.fkFileId},
            #{file.fkFileName},
            #{file.fileSuffix},
            #{file.fileUrl},
            #{file.fkCustometLogId},
            #{file.fkWsTaskId}
            )
        </foreach>

    </insert>
    <update id="deleteFile">
        update ws_file_file
        set delete_status = 1
        where pk_ws_file_id = #{pkWsFileId}

    </update>
    <update id="updateBatchFile">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE ws_file_file
            set
            work_number = #{item.workNumber}
            WHERE fk_customet_log_id = #{item.fkCustometLogId}
        </foreach>
    </update>
    <delete id="deleteFileByWorkNumber">
        update ws_file_file
        set delete_status = 1
        where work_number = #{workNumber} and fk_customet_log_id is null

    </delete>
    <select id="selectAllList" resultType="cn.trasen.worksheet.module.dto.outVo.WsFileOutVo">
        select
        <include refid="fileColums"/>
        from ws_file_file
        where work_number = #{workNumber} and delete_status = 0
    </select>
    <select id="selectAllByTaskIdList" resultType="cn.trasen.worksheet.module.dto.outVo.WsFileOutVo">
        select
        <include refid="fileColums"/>
        from ws_file_file
        where fk_ws_task_id in(<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
          and delete_status = 0
    </select>
    <select id="selectAllByCustometLogIdList" resultType="cn.trasen.worksheet.module.entity.WsFileFile">
        select
        <include refid="fileColums"/>
        from ws_file_file
        where fk_customet_log_id in(<foreach collection="list" index="index" item="item" separator=",">#{item}</foreach>)
        and delete_status = 0
    </select>
</mapper>