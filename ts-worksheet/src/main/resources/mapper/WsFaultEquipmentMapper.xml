<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.worksheet.module.mapper.WsFaultEquipmentMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.worksheet.module.entity.WsFaultEquipment">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="pk_fault_equipment_id" jdbcType="VARCHAR" property="pkFaultEquipmentId"/>
        <result column="equipment_number" jdbcType="VARCHAR" property="equipmentNumber"/>
        <result column="equipment_name" jdbcType="VARCHAR" property="equipmentName"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="TINYINT" property="deleteStatus"/>
        <result column="fk_dept_id" jdbcType="VARCHAR" property="fkDeptId"/>
        <result column="registration_date" jdbcType="TIMESTAMP" property="registrationDate"/>
        <result column="equipment_code" jdbcType="VARCHAR" property="equipmentCode"/>
        <result column="fk_dept_name" jdbcType="VARCHAR" property="fkDeptName"/>
    </resultMap>
    <sql id="equimentColums">
        pk_fault_equipment_id
        ,
    equipment_number,
    equipment_name,
    equipment_remark,
    create_by,
    create_time,
    update_by,
    update_time,
    delete_status,
    fk_dept_id,
    fk_dept_name,
    registration_date,
    equipment_code
    </sql>


    <insert id="insertEquipment">
        insert into ws_fault_equipment (<include refid="equimentColums"/>)
        values (
        #{pkFaultEquipmentId},
        #{equipmentNumber},
        #{equipmentName},
        #{equipmentRemark},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{deleteStatus},
        #{fkDeptId},
        #{fkDeptName},
        #{registrationDate},
        #{equipmentCode}
        )

    </insert>
    <update id="updateEquipment">
        update ws_fault_equipment
        set equipment_number  = #{equipmentNumber},
            equipment_name    = #{equipmentName},
            equipment_remark  = #{equipmentRemark},
            update_by         = #{updateBy},
            update_time       = #{updateTime},
            fk_dept_id        = #{fkDeptId},
            fk_dept_name      = #{fkDeptName},
            registration_date = #{registrationDate},
            equipment_code    = #{equipmentCode}
        where pk_fault_equipment_id = #{pkFaultEquipmentId}


    </update>
    <update id="deleteEquipmenById">
        update ws_fault_equipment
        set delete_status = 1
        where pk_fault_equipment_id = #{pkFaultEquipmentId}

    </update>
    <select id="selectPageList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo">
        SELECT
        pk_fault_equipment_id,
        equipment_number,
        equipment_name,
        equipment_remark,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.delete_status,
        fk_dept_id,
        fk_dept_name,
        registration_date,
        equipment_code,
        IFNULL( b.counts, 0 ) counts
        FROM
        ws_fault_equipment a
        LEFT JOIN ( SELECT fk_fault_equipment_id, count( 1 ) counts FROM ws_ws_sheet GROUP BY fk_fault_equipment_id ) b
        ON a.pk_fault_equipment_id = b.fk_fault_equipment_id
        WHERE a.delete_status = 0
        <if test="null != equipmentName and '' != equipmentName">
            and equipment_name like concat('%',#{equipmentName},'%')
        </if>

    </select>

    <select id="selectEquipmenById" resultType="cn.trasen.worksheet.module.entity.WsFaultEquipment">
        select
        <include refid="equimentColums"/>
        from ws_fault_equipment
        where delete_status = 0 and pk_fault_equipment_id = #{pkFaultEquipmentId}
    </select>
    <select id="selectAllList" resultType="cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo">
        select
        <include refid="equimentColums"/>
        from ws_fault_equipment
        where delete_status = 0
        and fk_dept_id = #{fkDeptId}
        <if test="null != equipmentName and '' != equipmentName">
            and equipment_name like concat('%',#{equipmentName},'%')
        </if>
    </select>
    <select id="selectEquipmenByEquipmentNumber"
            resultType="cn.trasen.worksheet.module.entity.WsFaultEquipment">
        select
        <include refid="equimentColums"/>
        from ws_fault_equipment
        where delete_status = 0
        and equipment_number = #{equipmentNumber}
    </select>

</mapper>