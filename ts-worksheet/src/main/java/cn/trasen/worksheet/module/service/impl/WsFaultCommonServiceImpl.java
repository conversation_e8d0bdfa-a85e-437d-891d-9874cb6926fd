package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.inputVo.FaultCommonInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultCommonOutVo;
import cn.trasen.worksheet.module.entity.WsFaultCommon;
import cn.trasen.worksheet.module.mapper.WsFaultCommonMapper;
import cn.trasen.worksheet.module.service.WsFaultCommonService;

/**
 * <AUTHOR>
 * @date: 2021/7/1 15:39
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
public class WsFaultCommonServiceImpl implements WsFaultCommonService {

    @Autowired
    private WsFaultCommonMapper wsFaultCommonMapper;

    @Transactional
    @Override
    public PlatformResult save(FaultCommonInputVo faultCommonInputVo) {
        WsFaultCommon wsFaultCommon;
        if (StringUtil.isEmpty(faultCommonInputVo.getPkFaultCommonId())) {
            if (CollectionUtils.isEmpty(selectFaultCommonAllList(faultCommonInputVo.getFalutDeion()))){
                wsFaultCommon = new WsFaultCommon();
                MyBeanUtils.copyBeanNotNull2Bean(faultCommonInputVo, wsFaultCommon);
                wsFaultCommon.setFkUserId(UserInfoHolder.getCurrentUserId());
                wsFaultCommon.setPkFaultCommonId(IdUtils.getId());
                wsFaultCommonMapper.insertFaultCommn(wsFaultCommon);
            }else{
                throw new BusinessException("该故障描述已添加，请勿重复添加");
            }
        } else {
            if (CollectionUtils.isEmpty(selectFaultCommonAllList(faultCommonInputVo.getFalutDeion()))){
                wsFaultCommon = wsFaultCommonMapper.selectOneById(faultCommonInputVo.getPkFaultCommonId());
                MyBeanUtils.copyBeanNotNull2Bean(faultCommonInputVo, wsFaultCommon);
                wsFaultCommon.setUpdateTime(new Date());
                wsFaultCommonMapper.updateById(wsFaultCommon);
            }else{
                throw new BusinessException("该故障描述已添加，请勿重复添加");
            }

        }
        return PlatformResult.success("操作成功");
    }


    /**
     * 批量逻辑删除
     * @param ids 多个参数以英文逗号拼接
     * @return
     */
    @Transactional
    @Override
    public int deleteAllByIds(String ids) {
        if (CommonlyConstants.YesOrNo.NO == wsFaultCommonMapper.deleteAllByIds(Arrays.asList(ids.split(CuttingOperatorEnum.COMMA.getValue())))) {
            throw new BusinessException("删除失败");
        }
        return IndexEnum.ONE.getValue();
    }
    @Transactional
    @Override
    public PlatformResult deleteById(String id) {
        if (CommonlyConstants.YesOrNo.NO == wsFaultCommonMapper.deleteById(id)) {
            throw new BusinessException("删除失败");
        }
        return PlatformResult.success("删除成功");
    }

    @Override
    public List<FaultCommonOutVo> selectFaultCommonAllList(String falutDeion) {
        return wsFaultCommonMapper.selectFaultCommonAllList(UserInfoHolder.getCurrentUserId(), falutDeion);
    }

    @Override
    public List<FaultCommonOutVo> selectFaultCommonPageList(Page page, String falutDeion) {
        return wsFaultCommonMapper.selectFaultCommonPageList(page, falutDeion,UserInfoHolder.getCurrentUserId());
    }
}
