package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/11/4 16:26
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Data
public class WsEvaluationOutVo {

    @ApiModelProperty(value = "用户综合评分")
    private float avgScore;

    @ApiModelProperty(value = "非常满意")
    private int fcmy;

    @ApiModelProperty(value = "不满意")
    private int my;

    @ApiModelProperty(value = "一般")
    private int yb;

    @ApiModelProperty(value = "不满意")
    private int bmy;

    @ApiModelProperty(value = "很不满意")
    private int hbmy;

    @ApiModelProperty(value = "用户综合评分")
    private List<WsEvaluationTopOutVo> wsEvaluationTopOutVo;
}
