package cn.trasen.worksheet.module.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsCustometLogListInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogCallRecordsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogWorkSheetOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportCallsListOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2021/7/26 17:52
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsCustometLogService {

    void insertWsCustometLog(WsCustometLog wsCustometLog);

    int updateWsCustometLog(WsCustometLog wsCustometLog);

    int updateBatchWsCustometLog(List<WsCustometLog> wsCustometLog);

    /**
     * 查询通话记录未建单数量、未读数量
     * @param map
     * @return
     */
    Map<String,Object> workOrderNotProcessedCounts(Map<String,Object> map);

    /**
     * 服务台今日统计
     * @return
     */
    Map<String,Object> serviceDeskStatisticsToday();

    /**
     * 指定服务台今日统计
     * @param fkDeptId
     * @param beginTime
     * @param endTime
     * @return
     */
    Map<String, Object> serviceDeskStatisticsToday(@Param("fkDeptId") String fkDeptId,
    			@Param("beginTime") String beginTime,
    			@Param("endTime") String endTime);

    WsCustometLogOutVo selectOneWsCustometLogOutVoById(@Param("pkCustometLogId") String pkCustometLogId);

    WsCustometLog selectOneById(@Param("pkCustometLogId") String pkCustometLogId);

    WsCustometLog selectOneByWorkNumber(@Param("workNumber") String workNumber);

    List<WsCustometLog> selectAllById(List<String> pkCustometLogIds);

    /**
     * 查询除当前通话中，该坐席人员所有未接来电
     * @return
     */
    List<WsCustometLogOutVo> selectAllList(WsCustometLog wsCustometLog);

    List<WsCustometLogOutVo> selectListByCustometLog(WsCustometLog wsCustometLog);

    List<WsCustometLogCallRecordsOutVo> selectCallRecordsPageList(Page page, WsCustometLogListInputVo wsCustometLogListInputVo);

    WsCustometLogWorkSheetOutVo selectCreateWorkSheetCustometService(String pkCustometLogId);

    String modifyingCallRecords(String pkCustometLogId, int callType,int callWorkStatus);

    /**
     * 当日报修次数
     * @param phone 报修人电话
     * @return
     */
    int numberOfDailyRepairReports(String phone);

    /**
     * 将通话记录修改为已读
     * @param fkDeptId
     * @return
     */
    int updateIsReadByDeptId(@Param("fkDeptId") String fkDeptId);


    /**
     * 查看当天未接信息列表
     * @param fkDeptId
     * @return
     */
    List<WsWorkReportCallsListOutVo> selectDayByDeptId(String fkDeptId);

    /**
     * 获取需填充工单号的录音文件信息
     * @return
     */
    List<Map<String,Object>> fillFileWorkNumberInfo();

    /**
     * 导出excel
     * @param page
     * @param wsCustometLogListInputVo
     */
    void exportExcel(Page page, WsCustometLogListInputVo wsCustometLogListInputVo, HttpServletResponse response, HttpServletRequest request);

	void updateCalltype();



}
