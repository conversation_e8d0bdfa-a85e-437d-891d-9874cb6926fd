package cn.trasen.worksheet.module.controller;


import java.util.ArrayList;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import cn.smallbun.screw.core.process.ProcessConfig;
import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.CustometServiceStatusInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsCustomeServiceInputVo;
import cn.trasen.worksheet.module.service.CustometServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 坐席，接入OM硬件
 *
 * <AUTHOR>
 * @date: 2021/7/19 14:14
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Api(tags = "坐席管理")
@RestController
public class CustometServiceController {

    @Autowired
    private CustometServiceService custometServiceService;

    /**
     * OM调用应服务器入口
     * @param request
     * @throws Exception
     */
    @ApiOperation(value = "", hidden = true)
    @GetMapping(value = "/OMInfo")
    public void test(HttpServletRequest request) throws Exception {
        custometServiceService.theEntrance(request);
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="保存或修改坐席")
    @ApiOperation(value = "保存或修改坐席设置", notes = "保存或修改坐席设置")
    @PostMapping("/CustometService/saveCustometService")
    public PlatformResult<String> saveCustometService(@RequestBody @Validated
                                              @ApiParam(value = "坐席保存信息")WsCustomeServiceInputVo wsCustomeServiceInputVo){
        return PlatformResult.success(custometServiceService.saveCustometService(wsCustomeServiceInputVo));
    }

    @ControllerLog(description="查询坐席信息")
    @ApiOperation(value = "查询坐席信息", notes = "查询坐席信息")
    @GetMapping("/CustometService/getOneCustometService/{fkUserId}")
    public PlatformResult getOneCustometService(@PathVariable("fkUserId") @ApiParam(value = "坐席id")String fkUserId){
        return PlatformResult.success(custometServiceService.getOneCustometService(fkUserId));
    }

    @ControllerLog(description="查询坐席状态")
    @ApiOperation(value = "查询坐席状态", notes = "查询坐席状态")
    @GetMapping("/CustometService/getOneCustometServiceStatus")
    public PlatformResult getOneCustometServiceStatus(@Validated CustometServiceStatusInputVo custometServiceStatusInputVo){
        return PlatformResult.success(custometServiceService.getOneCustometServiceStatus(custometServiceStatusInputVo));
    }

    public static void main(String[] args) {

        //数据源
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");
        hikariConfig.setJdbcUrl("jdbc:mysql://*************:3306/ts_wo");
        hikariConfig.setUsername("root");
        hikariConfig.setPassword("123456");
        //设置可以获取tables remarks信息
        hikariConfig.addDataSourceProperty("useInformationSchema", "true");
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setMaximumPoolSize(5);
        DataSource dataSource = new HikariDataSource(hikariConfig);
        //生成配置
        EngineConfig engineConfig = EngineConfig.builder()
                //生成文件路径
                .fileOutputDir("D:\\Clxini\\Work\\结项文档")
                //打开目录
                .openOutputDir(true)
                //文件类型
                .fileType(EngineFileType.HTML)
                //生成模板实现
                .produceType(EngineTemplateType.freemarker)
                //自定义文件名称
                .fileName("工单系统数据库设计文档").build();

        //忽略表
        ArrayList<String> ignoreTableName = new ArrayList<>();
        ignoreTableName.add("test_user");
        ignoreTableName.add("test_group");
        //忽略表前缀
        ArrayList<String> ignorePrefix = new ArrayList<>();
        ignorePrefix.add("test_");
        //忽略表后缀
        ArrayList<String> ignoreSuffix = new ArrayList<>();
        ignoreSuffix.add("_test");
        ProcessConfig processConfig = ProcessConfig.builder()
                //指定生成逻辑、当存在指定表、指定表前缀、指定表后缀时，将生成指定表，其余表不生成、并跳过忽略表配置
                //根据名称指定表生成
                .designatedTableName(new ArrayList<>())
                //根据表前缀生成
                .designatedTablePrefix(new ArrayList<>())
                //根据表后缀生成
                .designatedTableSuffix(new ArrayList<>())
                //忽略表名
                .ignoreTableName(ignoreTableName)
                //忽略表前缀
                .ignoreTablePrefix(ignorePrefix)
                //忽略表后缀
                .ignoreTableSuffix(ignoreSuffix).build();
        //配置
        Configuration config = Configuration.builder()
                //版本
                .version("1.0.0")
                //描述
                .description("数据库设计文档生成")
                //数据源
                .dataSource(dataSource)
                //生成配置
                .engineConfig(engineConfig)
                //生成配置
                .produceConfig(processConfig)
                .build();
        //执行生成
        new DocumentationExecute(config).execute();





//        String post = HttpUtil.post("**************", "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
//                "<Control attribute=\"Assign\">\n" +
//                "    <menu id=\"1\">\n" +
//                "        <voicefile>user_menu</voicefile>\n" +
//                "        <repeat>3</repeat>\n" +
//                "        <infolength>1</infolength>\n" +
//                "    </menu>\n" +
//                "</Control>");
//        System.out.println(post);
    }

}
