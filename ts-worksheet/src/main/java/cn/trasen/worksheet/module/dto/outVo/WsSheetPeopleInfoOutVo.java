package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date: 2021/6/24 13:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsSheetPeopleInfoOutVo {

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户工号")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "联系方式")
    private String phone;

    @ApiModelProperty(value = "用户所属科室id")
    private String deptId;

    @ApiModelProperty(value = "用户所属科室名称")
    private String deptName;

    @ApiModelProperty(value = "数据权限科室id")
    private String permissionsDeptIds;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "性别")
    private String sexText;

    @ApiModelProperty(value = "头像")
    private String url;

    @ApiModelProperty(value = "是否领导（是为全院大屏、否为科室大屏）")
    private boolean isLead;

    @ApiModelProperty(value = "是否具有服务台权限")
    private boolean isWebSocket;

    @ApiModelProperty(value = "工单-数据统计全院权限(具有该角色，则查看数据统计菜单全院数据)")
    private boolean statisticsData;

    @ApiModelProperty(value = "服务台权限")
    private boolean workSheetAdmin = true;

    @ApiModelProperty(value = "是否具有内部邮件权限")
    private boolean isEmail = true;

    @ApiModelProperty(value = "工单业务管理员（查看本科室所有业务）")
    private boolean isBusinessexception;

    @ApiModelProperty(value = "当前登录人所属科室是否是处理科室（是为true,否false）")
    private boolean isProcessorPeople;

    @ApiModelProperty(value = "处理中数量")
    private Integer processCount;

    @ApiModelProperty(value = "处理中数量拼接")
    private String processCountString;

    public WsSheetPeopleInfoOutVo(String userId, String name, String phone, String deptId, String deptName
            , boolean isWebSocket, boolean isBusinessexception, boolean isLead, boolean statisticsData,String permissionsDeptIds,boolean isProcessorPeople) {
        this.userId = userId;
        this.phone = phone;
        this.name = name;
        this.deptId = deptId;
        this.deptName = deptName;
        this.isWebSocket = isWebSocket;
        this.isBusinessexception = isBusinessexception;
        this.isLead = isLead;
        this.statisticsData = statisticsData;
        this.permissionsDeptIds = permissionsDeptIds;
        this.isProcessorPeople = isProcessorPeople;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public boolean isWebSocket() {
        return isWebSocket;
    }

    public void setWebSocket(boolean webSocket) {
        isWebSocket = webSocket;
    }

    public WsSheetPeopleInfoOutVo() {
        super();
    }
}
