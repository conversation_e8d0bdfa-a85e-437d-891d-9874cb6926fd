package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2021/12/8 15:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Data
public class WsWorkReportOutVo {

    @ApiModelProperty(value = "科室名")
    private String deptName;

    @ApiModelProperty(value = "接通电话数量")
    private String answerPhone;

    @ApiModelProperty(value = "接听率")
    private String answerPhoneRate;

    @ApiModelProperty(value = "电话解决数量")
    private String phoneSolve;

    @ApiModelProperty(value = "电话解决率")
    private String phoneSolveRate;

    @ApiModelProperty(value = "无效来电数量")
    private String invalidTelephone;

    @ApiModelProperty(value = "未接来电数量")
    private String numberOfMissedCalls;

    @ApiModelProperty(value = "未接来电具体列表")
    private List<WsWorkReportCallsListOutVo> numberOfMissedCallsList;

    @ApiModelProperty(value = "呼出数量")
    private String calls;

    @ApiModelProperty(value = "今日新增工单数量")
    private String newWorkOrderToday;

    @ApiModelProperty(value = "待派单数量")
    private String snet;

    @ApiModelProperty(value = "处理中数量")
    private String processing;

    @ApiModelProperty(value = "未建单数量")
    private String noWorkOrder;

    @ApiModelProperty(value = "一级故障分类信息")
    private List<Map<String, Object>> levelOneFaultTypeDatas;

    public WsWorkReportOutVo(String answerPhone, String answerPhoneRate, String phoneSolve, String phoneSolveRate, String invalidTelephone, String numberOfMissedCalls, List<WsWorkReportCallsListOutVo> numberOfMissedCallsList, String calls, String newWorkOrderToday, String snet, String processing, String noWorkOrder, List<Map<String, Object>> levelOneFaultTypeDatas) {
        this.answerPhone = answerPhone;
        this.answerPhoneRate = answerPhoneRate;
        this.phoneSolve = phoneSolve;
        this.phoneSolveRate = phoneSolveRate;
        this.invalidTelephone = invalidTelephone;
        this.numberOfMissedCalls = numberOfMissedCalls;
        this.numberOfMissedCallsList = numberOfMissedCallsList;
        this.calls = calls;
        this.newWorkOrderToday = newWorkOrderToday;
        this.snet = snet;
        this.processing = processing;
        this.noWorkOrder = noWorkOrder;
        this.levelOneFaultTypeDatas = levelOneFaultTypeDatas;
    }
}
