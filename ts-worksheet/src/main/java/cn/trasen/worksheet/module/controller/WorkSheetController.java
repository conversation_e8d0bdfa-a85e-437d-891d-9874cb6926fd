package cn.trasen.worksheet.module.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetHomePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetRemindOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobilleStatisticsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetSaveOutVo;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsWsMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @date: 2021/6/17 11:56
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 * 工单业务
 */

@Api(tags = "工单业务管理")
@RestController
@Log4j2
public class WorkSheetController  {

    @Autowired
    private WsSheetService wsSheetService;
    @Autowired
    private WsWsMessageService wsWsMessageService;
    @Autowired
    private WsOmMeauService wsOmMeauService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="完成工单:修改工单业务类型")
    @ApiOperation(value = "完成工单:修改工单业务类型", notes = "完成工单:修改工单业务类型")
    @PostMapping("/workSheet/updateByWorkNumber")
    public PlatformResult<String> updateByWorkNumber(@RequestBody @Validated WsWsSheet wsWsSheet) {
    	try {
    		wsSheetService.updateByWorkNumber(wsWsSheet);
			return PlatformResult.success();
		} catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
    }
    
    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="保存或修改工单业务")
    @ApiOperation(value = "保存或修改工单业务", notes = "保存或修改工单业务")
    @PostMapping("/workSheet/save")
    public PlatformResult<WsWsSheetSaveOutVo> save(@RequestBody @Validated WsWsSheetInputVo wsSheetInputVo) {
        return wsSheetService.save(wsSheetInputVo);
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="电话已解决")
    @ApiOperation(value = "电话已解决", notes = "电话已解决")
    @PostMapping("/workSheet/phoneHasBeenResolved")
    public PlatformResult phoneHasBeenResolved(@RequestBody @Validated WsWsSheetInputVo wsSheetInputVo) {
        return PlatformResult.success(wsSheetService.phoneHasBeenResolved(wsSheetInputVo));
    }

    @ControllerLog(description="工单分页列表")
    @ApiOperation(value = "工单分页列表", notes = "工单分页列表")
    @PostMapping("/workSheet/workSheetList")
    public DataSet<WsWsSheetListOutVo> workSheetList(Page page, @Validated WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
    	long start = System.currentTimeMillis();
        List<WsWsSheetListOutVo> workSheetPageList = wsSheetService.getWorkSheetPageList(page, wsSheetListSelectInputVo);
        System.err.println("0=====:"+(System.currentTimeMillis()- start));
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), workSheetPageList);
    }


    @ControllerLog(description="创建工单右侧列表")
    @ApiOperation(value = "创建工单右侧列表", notes = "创建工单右侧列表")
    @PostMapping("/workSheet/getCreateWorkSheetPageList")
    public DataSet<WsWsSheetListOutVo> getCreateWorkSheetPageList(Page page, String faultDeion,String repairManDeptId) {
        List<WsWsSheetListOutVo> workSheetPageList = wsSheetService.getCreateWorkSheetPageList(page, faultDeion,repairManDeptId);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), workSheetPageList);

    }

    @ControllerLog(description="我的工单参与过列表")
    @ApiOperation(value = "我的工单参与过列表", notes = "我的工单参与过列表")
    @PostMapping("/workSheet/workSheetTookPartPageList")
    public DataSet<WsWsSheetListOutVo> workSheetTookPartPageList(Page page, @Validated WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
        List<WsWsSheetListOutVo> workSheeTookParttPageList = wsSheetService.getTookPartPageList(page, wsSheetListSelectInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), workSheeTookParttPageList);

    }

    @ControllerLog(description="列表上各工单状态业务数量")
    @ApiOperation(value = "列表上各工单状态业务数量", notes = "列表上各工单状态业务数量")
    @GetMapping("/workSheet/workSheetListBusCounts/{type}")
    public PlatformResult workSheetListBusCounts(@PathVariable @ApiParam(value = "4为处理人、5为报修人") String type,
                                                 WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
        return wsSheetService.workSheetListBusCounts(type,wsSheetListSelectInputVo);

    }

    @ControllerLog(description="移动端-列表上各工单状态业务数量")
    @ApiOperation(value = "移动端-列表上各工单状态业务数量", notes = "移动端-列表上各工单状态业务数量")
    @GetMapping("/workSheet/mobileWorkSheetListBusCounts")
    public PlatformResult mobileWorkSheetListBusCounts() {
        return PlatformResult.success(wsSheetService.mobileWorkSheetListBusCounts());

    }

    @ControllerLog(description="移动端-工作台-我的工单状态业务数量")
    @ApiOperation(value = "移动端-工作台-我的工单状态业务数量", notes = "移动端-工作台-我的工单状态业务数量")
    @GetMapping("/workSheet/mobileWorkbenchWorkSheetBusCounts")
    public PlatformResult mobileWorkbenchWorkSheetBusCounts() {
        return PlatformResult.success(wsSheetService.mobileWorkbenchWorkSheetBusCounts());

    }

    @ApiOperation(value = "工单详情", notes = "工单详情")
    @GetMapping("/workSheet/workSheetInfo/{workNumber}")
    public PlatformResult workSheetInfo(@PathVariable @ApiParam(value = "工单编号") String workNumber,
                                        @ApiParam(value = "是否显示处理进度") String taskName) {
        return wsSheetService.workSheetInfo(workNumber,taskName);
    }

    @ApiOperation(value = "流程-工单详情", notes = "流程-工单详情")
    @GetMapping("/workSheet/workSheetInfo/workFlow/{businessId}")
    public PlatformResult workSheetInfoWf(@PathVariable @ApiParam(value = "流程业务id") String businessId) {
        return wsSheetService.workSheetInfoWf(businessId);
    }

    @ControllerLog(description="工单编辑详情")
    @ApiOperation(value = "工单编辑详情", notes = "工单编辑详情")
    @GetMapping("/workSheet/workSheetEditInfo/{workNumber}")
    public PlatformResult workSheetEditInfo(@PathVariable @ApiParam(value = "工单编号") String workNumber) {
        return wsSheetService.workSheetEditInfo(workNumber);
    }

    @ControllerLog(description="查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等")
    @ApiOperation(value = "查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等", notes = "查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等")
    @GetMapping("/workSheet/selectMobileInfo")
    public PlatformResult selectMobileInfo() {
        return PlatformResult.success(wsSheetService.selectMobileInfo());
    }

    @ControllerLog(description="待接单、待验收、待评价是否存在工单，已暂停、已终止是否存在工单")
    @ApiOperation(value = "待接单、待验收、待评价是否存在工单，已暂停、已终止是否存在工单", notes = "待接单、待验收、待评价是否存在工单，已暂停、已终止是否存在工单")
    @GetMapping({"/workSheet/workSheetremind"})
    public PlatformResult<WsSheetRemindOutVo> workSheetremind(){
        return wsSheetService.workSheetremind();
    }


    @ControllerLog(description="查询用户工单消息未读信息")
    @ApiOperation(value = "查询用户工单消息未读信息", notes = "查询用户工单消息未读信息")
    @GetMapping({"/workSheet/workSheetNumberByUserId/{userId}"})
    public PlatformResult<List<WsWsMessageListOutVo>> workSheetNumberByUserId(@PathVariable("userId")String userId){
        return PlatformResult.success(wsWsMessageService.selectMessageAllList(userId, IndexEnum.ZERO.getValue()));
    };

    @ControllerLog(description="移动端-我的工单待派单、在办、待验收、待评价数量")
    @ApiOperation(value = "移动端-我的工单待派单、在办、待验收、待评价数量", notes = "移动端-我的工单待派单、在办、待验收、待评价数量")
    @GetMapping({"/workSheet/mobileMyWorkOrderStatistics"})
    public PlatformResult<WsWsSheetMobilleStatisticsOutVo> mobileMyWorkOrderStatistics(){
        return PlatformResult.success(wsSheetService.mobileMyWorkOrderStatistics());
    }

    @ControllerLog(description="生成扫码报修的二维码")
    @ApiOperation(value = "生成扫码报修的二维码", notes = "生成扫码报修的二维码")
    @GetMapping({"/workSheet/scanQrCode"})
    public PlatformResult<String> scanQrCode(Integer scanQrCodeSize){
        return PlatformResult.success(wsSheetService.scanQrCode(scanQrCodeSize));
    }


    @ControllerLog(description="个人是否属于处理科室")
    @ApiOperation(value = "个人是否属于处理科室", notes = "个人是否属于处理科室")
    @GetMapping({"/workSheet/personalBelongToBusinessDept"})
    public PlatformResult<Boolean> personalBelongToBusinessDept(){
        return PlatformResult.success(wsOmMeauService.personalBelongToBusinessDept());
    }

    @ControllerLog(description="科室派单数量，个人接单数量")
    @ApiOperation(value = "科室派单数量，个人接单数量", notes = "科室派单数量，个人接单数量")
    @GetMapping({"/workSheet/dpdDjdCount"})
    public PlatformResult<WsSheetHomePageOutVo> dpdDjdCount(){
        return PlatformResult.success(wsSheetService.dpdDjdCount());
    }


    @ControllerLog(description="导出Excel")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @GetMapping("/workSheet/exportExcel")
    public void exportExcel(Page page, @Validated WsWsSheetListSelectInputVo wsSheetListSelectInputVo, HttpServletResponse response, HttpServletRequest request) {
        wsSheetService.exportExcel(page, wsSheetListSelectInputVo, response, request);
    }

    @ControllerLog(description="导出故障处理申请单Excel")
    @ApiOperation(value = "导出故障处理申请单Excel", notes = "导出故障处理申请单Excel")
    @GetMapping("/workSheet/print/{workNumber}")
    public void exportExcel(@PathVariable @ApiParam(value = "工单编号") String workNumber,HttpServletResponse response, HttpServletRequest request) {
        wsSheetService.printExcel(response, request,workNumber);
    }



}
