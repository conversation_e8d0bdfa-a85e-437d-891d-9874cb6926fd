package cn.trasen.worksheet.module.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.worksheet.module.dto.outVo.WsTaskWorkHoursListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsTaskInfoOutVo;
import cn.trasen.worksheet.module.entity.WsWsTask;
import tk.mybatis.mapper.common.Mapper;

public interface WsWsTaskMapper extends Mapper<WsWsTask> {

    void insertBatch(List<WsWsTask> list);

    int insertWsTask(WsWsTask wsTask);

    void updateWsTaskById(WsWsTask wsTask);

    void updateTaskTerminated(String workNumber);

    void updateTaskStop(String workNumber);

    void updateTaskOpen(String workNumber);

    void updateBatch(List<WsWsTask> wsTask);


    WsWsTask selectOneWsTaskById(String id);

    WsWsTask selectOneWsTask(String workNumber);

    List<WsWsTaskInfoOutVo> selectOneWsTaskInfo(@Param("workNumber") String workNumber,
                                                @Param("taskName") String taskName);

    WsWsTask selectOneWsTaskByuser(WsWsTask wsTask);

    List<WsWsTask> selectAssisListByWorkNumber(String workNumber);

    int assistanceToCompleted(String taskId);

    /**
     * 查看最新的一条节点信息
     * @param workNumber 工单编号
     * @return
     */
    WsWsTask selectOneMaxTimeTaskByWorkNumber(String workNumber);


    /**
     * 工时列表
     * @param workNumber
     * @return
     */
    List<WsTaskWorkHoursListOutVo> selectAllTaskWorkHoursList(String workNumber);


    /**
     * 提交知识库的解决方案信息
     * @param workNumber
     * @return
     */
    WsWsTaskInfoOutVo selectSubmitKnowledgeBaseInfo(String workNumber);

    /**
     * 查询当前节点是否完成（非协助人节点）
     * @param pkWsTaskId 节点id
     * @return
     */
    int lastTaskInfoIsComplete(String pkWsTaskId);

    /**
     * 查询工单所有协助人id
     * @param workNumber
     * @return
     */
    List<String> selectAssistIdByWorkNumber(String workNumber);



}