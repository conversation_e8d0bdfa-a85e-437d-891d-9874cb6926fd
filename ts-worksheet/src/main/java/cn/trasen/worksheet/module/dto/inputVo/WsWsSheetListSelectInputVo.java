package cn.trasen.worksheet.module.dto.inputVo;

import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/6/21 9:33
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsWsSheetListSelectInputVo {

    @NotNull(message = "页签类型不能为空")
    @ApiModelProperty(value = "页签类型 (1：待派单，2：待接单，3：处理中，4：待验收，5：待评价，6：已完成，7：已暂停，8：已终止," +
            "9：参与过，10：服务台处理中，11：服务台已完成，12：服务台待派单，13：服务台全部，0：我的工单全部)")
    private String workStatus;

    @ApiModelProperty(value = "workStatus 为10，且为PC端时，状态多选传递，多个已英文逗号拼接 ")
    private String workStatusG ;

    @ApiModelProperty(hidden = true)
    private List<String> workStatusGList ;

    @ApiModelProperty(value = "移动端类型 (1为未开始（workStatus传1），2为未进行中（workStatus传3）,3为已结束（workStatus传6）")
    private String mobileType ;

    @ApiModelProperty(value = "移动端类型 (查询报修数据为1，查询处理数据为2）")
    private String type ;

    @ApiModelProperty(value = "工单状态 待派单，2：待接单，3：处理中，4：待验收，5：待评价，6：已完成，7：已暂停，8：已终止")
    private String workStatusValue;

    @ApiModelProperty(hidden = true)
    private List<String> workStatusValueList;


    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报修时间(yyyy-MM-dd)")
    private Date createTime;

    @ApiModelProperty(value = "服务台工单号、报修人、故障描述")
    private String fuzzy;

    @ApiModelProperty(value = "查询报修人id")
    private String repairManId;

    @ApiModelProperty(value = "查询处理人id")
    private String userId;

    @ApiModelProperty(value = "工单号")
    private String workNumber;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报修开始时间(yyyy-MM-dd)")
    private Date beginTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报修结束时间(yyyy-MM-dd)")
    private Date endTime;

    @ApiModelProperty(value = "报修科室id")
    private String repairManDeptId;

    @ApiModelProperty(value = "故障类型")
    private String faultTypeId;

    @ApiModelProperty(value = "故障紧急程度。非常紧急：1,比较急：2：常规处理")
    private String faultEmergency;

    @ApiModelProperty(value = "报修方式。电话报修：1,微信报修：2,电脑报修：3,上门报修：4，补录：5")
    private String repairType;

    @ApiModelProperty(value = "故障影响范围。个人事件：1,科室事件：2,多科室事件：3,全院事件：4")
    private String faultAffectScope;

    @ApiModelProperty(value = "故障描述")
    private String faultDeion;

    @ApiModelProperty(hidden = true)
    private String fkUserId;

    @ApiModelProperty(value = "处理人")
    private String fkUserName;


    /**
     * 是否可查看本部门、跨部门数据权限 （fasle否true是）
     */
    @ApiModelProperty(hidden = true)
    private boolean admin;

    /**
     * 报修人部门id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String repairDeptId;

    /**
     * 报修人部门id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private List<String> repairDeptIdList;

    /**
     * 处理人部门id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String fkDeptId;

    /**
     * 处理人部门id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private List<String> fkDeptIdList;

    /**
     * 报修人id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String repairId;

    /**
     * 处理人id，仅用于sql查询
     */
    @ApiModelProperty(hidden = true)
    private String fkId;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "故障要求完成时间开始（yyyy-MM-dd）")
    private Date beginRequiredCompletionTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "故障要求完成时间结束（yyyy-MM-dd）")
    private Date endRequiredCompletionTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "完成时间开始（yyyy-MM-dd）")
    private Date beginActualCompletionTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "完成时间结束（yyyy-MM-dd）")
    private Date endActualCompletionTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "终止时间开始（yyyy-MM-dd）")
    private Date beginTerminationOfTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "终止时间结束（yyyy-MM-dd）")
    private Date endTerminationOfTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "确认时间开始（yyyy-MM-dd）")
    private Date beginAcknowledgingTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "确认时间结束（yyyy-MM-dd）")
    private Date endAcknowledgingTime;

    @ApiModelProperty(value = "完成状态（6：已完成，8：已终止）")
    private String completeStatus;


    /**
     * 当前登录人id
     */
    @ApiModelProperty(hidden = true)
    private String theUserId;

    public void initEndTime(){
        if(null != this.endTime){
            this.endTime = DateUtils.dateAddNDay(this.endTime, IndexEnum.ONE.getValue());
        }
        if(null != this.endRequiredCompletionTime){
            this.endRequiredCompletionTime = DateUtils.dateAddNDay(this.endRequiredCompletionTime, IndexEnum.ONE.getValue());
        }
        if(null != this.endActualCompletionTime){
            this.endActualCompletionTime = DateUtils.dateAddNDay(this.endActualCompletionTime, IndexEnum.ONE.getValue());
        }
        if(null != this.endTerminationOfTime){
            this.endTerminationOfTime = DateUtils.dateAddNDay(this.endTerminationOfTime, IndexEnum.ONE.getValue());
        }
        if(null != this.endAcknowledgingTime){
            this.endAcknowledgingTime = DateUtils.dateAddNDay(this.endAcknowledgingTime, IndexEnum.ONE.getValue());
        }
    }

}
