package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_customet_service")
@Setter
@Getter
public class WsCustometService extends WsBase implements Supplier {

    public WsCustometService() {
        super();
    }

    public WsCustometService(String phone) {
        this.phone = phone;
    }

    public WsCustometService(String pkCustometServiceId, int playScreen, String phone, int custometServiceStatus, String fkUserId) {
        this.pkCustometServiceId = pkCustometServiceId;
        this.playScreen = playScreen;
        this.phone = phone;
        this.custometServiceStatus = custometServiceStatus;
        this.fkUserId = fkUserId;
    }

    /**
     * 坐席ID
     */
    @Column(name = "pk_customet_service_id")
    @ApiModelProperty(value = "坐席ID")
    private String pkCustometServiceId;

    /**
     * 来电是否弹屏
     */
    @Column(name = "play_screen")
    @ApiModelProperty(value = "来电是否弹屏")
    private int playScreen;
    
    @Column(name = "play_voice")
    @ApiModelProperty(value = "语音播报")
    private int playVoice;

    /**
     * 坐席联系方式
     */
    @ApiModelProperty(value = "坐席联系方式")
    private String phone;

    /**
     * 坐席状态
     */
    @ApiModelProperty(value = "坐席状态")
    private int custometServiceStatus;

    /**
     * 坐席设备号
     */
    @ApiModelProperty(value = "坐席设备号")
    private String equipment;

    /**
     * 坐席人员ID
     */
    @Column(name = "fk_user_id")
    @ApiModelProperty(value = "坐席人员ID")
    private String fkUserId;

    /**
     * 坐席人员ID
     */
    @Column(name = "fk_user_dept_id")
    @ApiModelProperty(value = "坐席科室id")
    private String fkUserDeptId;

    @Override
    public WsCustometService get() {
        return this;
    }
}