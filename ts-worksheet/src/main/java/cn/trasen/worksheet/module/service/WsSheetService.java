package cn.trasen.worksheet.module.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.bean.hrms.HrmsSchedulingManage;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetHomeListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetMobileStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsEvaluationOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetHomePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetRemindOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetOderAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetSendAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoByFaultEquipmentOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobileInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobilleStatisticsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetScreenListOutVo;
import cn.trasen.worksheet.module.entity.WsWsSheet;

public interface WsSheetService {

    /**
     * 根据工单编号查询
     *
     * @param workNumber 工单编号
     * @return
     */
    WsWsSheet selectOneWsSheet(String workNumber);

    WsWsSheetInfoOutVo selectOneWsSheetInfo(String workNumber, String repairManId, String fkUserId);

    /**
     * 根据工单节点id查询
     *
     * @param taskId 工单节点id
     * @return
     */
    WsWsSheet selectOneWsSheetByTaskId(String taskId);

    /**
     * 根据工单状态查询
     *
     * @param workStatus
     * @return
     */
    List<WsWsSheet> selectWsSheetListByWorkStatus(String workStatus);

    /**
     * 查询人员所属所有工单业务
     *
     * @param fkUserId
     * @return
     */
    List<WsWsSheet> selectListWsSheetByFkUserId(String fkUserId);

    /**
     * 根据故障设备id查询工单信息
     *
     * @param faultEquipmentId
     * @return
     */
    List<WsWsSheetInfoByFaultEquipmentOutVo> selectListWsSheetByfaultEquipmentId(String faultEquipmentId);

    /**
     * 保存
     *
     * @param wsSheetInputVo 工单信息
     * @return
     */
    PlatformResult save(WsWsSheetInputVo wsSheetInputVo);
    
    /**
     * 工单上报，推送提醒
     *
     * @param deptId
     * @param date
     * @param workNumber
     * @return
     */
    Boolean pushWeChatMessage(String deptId, String date, String workNumber, String content);

    /**
     * 获取值班信息
     *
     * @param deptId 科室id
     * @param date   日期 yyyy-MM-dd
     * @return
     */
    List<HrmsSchedulingManage> informationOnDuty(String deptId, String date);

    /**
     * 电话已解决
     *
     * @param wsSheetInputVo
     * @return
     */
    int phoneHasBeenResolved(WsWsSheetInputVo wsSheetInputVo);

    /**
     * 工单详情
     *
     * @param workNumber 工单编号
     * @return
     */
    PlatformResult workSheetInfo(String workNumber,String taskName);

    /**
     * 流程-工单详情
     *
     * @param businessId 工单编号
     * @return
     */
    PlatformResult workSheetInfoWf(String businessId);

    /**
     * 工单编辑详情
     *
     * @param workNumber 工单编号
     * @return
     */
    PlatformResult workSheetEditInfo(String workNumber);

    /**
     * 工单分页列表
     *
     * @param page
     * @param wsSheetListSelectInputVo
     * @return
     */
    List<WsWsSheetListOutVo> getWorkSheetPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo);


    /**
     * 申请人首页-工单列表分页
     *
     * @param page
     * @return
     */
    List<WsWsSheetListOutVo> getApplicantWorkSheetPageList(Page page);

    /**
     * 创建工单右侧列表
     *
     * @param page
     * @param faultDeion
     * @return
     */
    List<WsWsSheetListOutVo> getCreateWorkSheetPageList(Page page, String faultDeion, String repairManDeptId);

    void updateByWorkNumber(WsWsSheet wsSheet);
    void update(WsWsSheet wsSheet);

    void insertWsSheet(WsWsSheet wsSheet);

    void updateWsSheetByWorkNumber(WsWsSheet wsSheet);

    void updateBatch(List<WsWsSheet> wsSheet);


    /**
     * 获取服务台、工单列表上各工单状态业务数据量
     *
     * @return
     */
    PlatformResult workSheetListBusCounts(String type,WsWsSheetListSelectInputVo wsSheetListSelectInputVo);

    /**
     * 指定科室 获取服务台、工单列表上各工单状态业务数据量
     *
     * @param fkUserDeptId
     * @return
     */
    Map<String, Object> selectCountGroupByWorkNumber(String fkUserDeptId);

    /**
     * 我的工单参与过列表
     *
     * @param page
     * @param wsSheetListSelectInputVo
     * @return
     */
    List<WsWsSheetListOutVo> getTookPartPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo);

    /**
     * 工单业务完成，更新工单数据
     */
    WsWsSheet workSheetComplete(WsWsSheet wsSheet, String workStatus, Date actualCompletionTime, Float workHours);

    /**
     * 科室办理业务Top榜单
     *
     * @param beginTime
     * @param endTime
     * @param fkDeptId
     * @return 有值为科室级、null为全院级
     */
    List<Map<String, Object>> getDeptCountTopDatas(Page page, String beginTime, String endTime, String fkDeptId);

    /**
     * 一级故障类型各类型工单数量
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getLevelOneFaultTypeDatas(String beginTime, String endTime, String deptId);

    /**
     * 知识库点赞Top榜单
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getKnowledgeLikeCountTopDatas(Page page, String beginTime, String endTime, String deptId);


    /**
     * 大屏工单关键数据指标
     *
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    Map<String, Object> getKeyDataIndicatorsOfWorkOrder(@Param("fkDeptId") String fkDeptId);


    /**
     * 获取今日，各科室的建单数据及总数
     *
     * @param page
     * @return
     */
    List<Map<String, Object>> getDayGroupByDept(Page page);

    /**
     * 月度划分，提单、电话提单、办结趋势数据
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> getHotTrend(String type);

    /**
     * 处理中工单
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> getProcessTheWorkOrder(String type);

    /**
     * 科室工单统计
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> getDeptWorkSheetCount(String type);

    /**
     * 科室工单质量
     *
     * @param fkDeptId 有值为科室级、null为全院级
     * @param limit    返回数据条数
     * @return
     */
    WsEvaluationOutVo getDepartmentWorkOrderQuality(String fkDeptId, String beginTime, String endTime, int limit);

    /**
     * 服务台人员页面-统计指标
     *
     * @param fkDeptId 科室id
     * @return
     */
    Map<String, Object> getServiceDeskStaffStatisticalIndicators(String fkDeptId);

    /**
     * 异常工单各种状态统计
     *
     * @return
     */
    Map<String, Object> getAbnormalWorkSheetStatisCounts(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 超期工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getExceedTimeWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 催办工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getHastenWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 今日终止/暂停工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getSuspendTerminateSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);


    /**
     * 今日终止工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getTodayTerminationSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 今日暂停工单
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getTodaySuspendedSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);


    /**
     * 差评工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getBadReviewSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 打回工单数据
     *
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getBackSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 协助工单数据
     *
     * @param page
     * @param wsWorkSheetHomeListInputVo
     * @return
     */
    List<WsWorkSheetHomeListOutVo> getAssistWorkOrder(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 处理人、报修人各状态工单数量
     *
     * @return
     */
    List<Map<String, Object>> getCountGroupByWorkStatus(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo);

    /**
     * 工单处理情况
     *
     * @return
     */
    Map<String, Object> getWorkOrderProcessing(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    Integer getWorkOrderUnfinished(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
    /**
     * 工单各状态、工单各报修方式、工单各故障紧急程度、工单各故障影响范围数据统计
     *
     * @return
     */
    List<Map> getWorkGroupByType(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

//    /**
//     * 工单各报修方式数据统计
//     *
//     * @return
//     */
//    List<Map> getWorkGroupByRepairTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
//
//    /**
//     * 工单各故障紧急程度数据统计
//     *
//     * @return
//     */
//    List<Map> getWorkGroupByFaultEmergencyDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
//
//    /**
//     * 工单各故障影响范围数据统计
//     *
//     * @return
//     */
//    List<Map> getWorkGroupByFaultAffectScopeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 科室接单统计
     *
     * @return
     */
    List<Map<String, Object>> getDeptReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    List<Map<String, Object>> getDeptUserReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);
    /**
     * 科室提单统计
     *
     * @param page
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getDeptBillOfLading(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    /**
     * 一级故障类型各类型工单数量(带时间区间查询)
     *
     * @return
     */
    List<Map<String, Object>> getLevelOneFaultTypeDatasToDate(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 工单各设备故障分类数据统计
     *
     * @return
     */
    List<Map> getFaultEquipment(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    /**
     * 科室服务平均用时排名
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getDeptQualityOfService(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    /**
     * 人员服务质量
     *
     * @param page
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getQualityOfPersonnelService(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 初始化查询参数时间
     */
    void intiTime(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 查询所有工单
     *
     * @return
     */
    List<WsWsSheet> selectAllList(String deptId, String beginTime, String endTime);

    /**
     * 移动端，工单列表，各页签数量
     *
     * @return
     */
    Map<String, Object> mobileWorkSheetListBusCounts();

    /**
     * 移动端-工作台-我的工单状态业务数量
     *
     * @return
     */
    Map<String, Object> mobileWorkbenchWorkSheetBusCounts();


    /**
     * 查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等
     *
     * @return
     */
    WsWsSheetMobileInfoOutVo selectMobileInfo();

    /**
     * 移动端-工单统计-工单状态、紧急程度、影响范围
     *
     * @param wsWorkSheetMobileStatisticalInputVo
     * @return
     */
    List<Map> getWorkOrderType(WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo);

    /**
     * 移动端-工单统计-工单情况
     *
     * @param wsWorkSheetMobileStatisticalInputVo
     * @return
     */
    Map<String, Object> getWorkOrderSituation(WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo);

    /**
     * 大屏工单处理中数据
     *
     * @param page
     * @param fkDeptId
     * @return
     */
    List<WsWsSheetScreenListOutVo> wsSheetScreenPageList(Page page, String fkDeptId);


    /**
     * 大屏，工单分配
     *
     * @param page
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    List<Map<String, Object>> wsSheetDistributionScreenPageList(Page page, 
    		@Param("fkDeptId") String fkDeptId,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime);

    /**
     * 未派单工单数量
     *
     * @param fkDeptId  有值为科室级、null为全院级
     * @param beginTime
     * @param endTime
     * @return
     */
    int noSendOrders(@Param("fkDeptId") String fkDeptId,
                     @Param("beginTime") String beginTime,
                     @Param("endTime") String endTime);


    /**
     * 待接单、待验收、待评价存在工单时normal为true
     * 已暂停、已终止存在工单时abnormal为true
     * 反之亦然
     *
     * @return
     */
    PlatformResult<WsSheetRemindOutVo> workSheetremind();


    /**
     * 移动端-我的工单待派单、在办、待验收、待评价数量
     *
     * @return
     */
    WsWsSheetMobilleStatisticsOutVo mobileMyWorkOrderStatistics();


    /**
     * 流程模块保存工单
     *
     * @param request
     */
    PlatformResult flowToWorkSheet(HttpServletRequest request);

    /**
     * 生成扫码报修的二维码
     *
     * @return
     */
    String scanQrCode(Integer scanQrCodeSize);

    /**
     * 填充处理人员信息，处理中工单数量
     *
     * @param peopleInfoVos 处理人员信息
     * @return
     */
    List<WsSheetPeopleInfoOutVo> fillPeopleProcessCount(List<WsSheetPeopleInfoOutVo> peopleInfoVos);


    /**
     * 查询科室未派单工单信息
     *
     * @param deptId
     * @return
     */
    List<Map<String, Object>> unprocessedMessageAlertsDpdDjd(String workStatus, String deptId, List<String> list);

    /**
     * 查询处理人，某工单状态数量
     *
     * @param fkUserId
     * @param workStatus
     * @return
     */
    int fkUserWorkStatusCount(String fkUserId, String fkDeptId, String workStatus);

    /**
     * OA首页 待接单、待派单数量
     *
     * @return
     */
    WsSheetHomePageOutVo dpdDjdCount();

    /**
     * 导出Excel
     * @param page
     * @param wsSheetListSelectInputVo
     * @param response
     * @param request
     */
    void exportExcel(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo, HttpServletResponse response, HttpServletRequest request);

    /**
     * 导出故障申请表单
     * @param response
     * @param request
     * @param workNumber
     */
    void printExcel(HttpServletResponse response, HttpServletRequest request,String workNumber);

    /**
     * 时效性分析-派单时效
     * @return
     */
    List<WsWorkSheetSendAgingOutVo> selectSheetSendAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 时效性分析-接单时效
     * @return
     */
    List<WsWorkSheetOderAgingOutVo> selectSheetOderAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

}
