package cn.trasen.worksheet.module.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import cn.trasen.worksheet.module.dto.inputVo.CustometServiceStatusInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsCustomeServiceInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometServiceOutVo;
import cn.trasen.worksheet.module.entity.WsCustometService;

/**
 * <AUTHOR>
 * @date: 2021/7/20 10:04
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface CustometServiceService {

    /**
     * OM调用应服务器入口
     * @param request
     */
    void theEntrance(HttpServletRequest request);

    String saveCustometService(WsCustomeServiceInputVo wsCustomeServiceInputVo);

    WsCustometService selectOneById(String pkCustometServiceId);

    int updateCustometService(WsCustometService wsCustometService);

    WsCustometServiceOutVo getOneCustometService(String fkUserId);

    /**
     * 根据用户id查询坐席
     * @param fkUserId
     * @param custometServiceStatus  来电是否弹屏（0否1是）
     * @param playScreen 坐席状态（0休息1工作中）
     * @return
     */
    List<WsCustometService> selectListByFkUserId(List<String> fkUserId, Integer custometServiceStatus, Integer playScreen);

    WsCustometService selectOneCustometServiceByPhone(String phone);

    String getOneCustometServiceStatus(CustometServiceStatusInputVo custometServiceStatusInputVo);


}
