package cn.trasen.worksheet.module.job;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.enums.WorkSheetTaskEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.FileUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsOmFile;
import cn.trasen.worksheet.module.entity.WsWrokReport;
import cn.trasen.worksheet.module.entity.WsWsEvaluation;
import cn.trasen.worksheet.module.entity.WsWsMessage;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.entity.WsWsTask;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsFileService;
import cn.trasen.worksheet.module.service.WsOmFileService;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;
import cn.trasen.worksheet.module.service.WsWorkReportService;
import cn.trasen.worksheet.module.service.WsWsMessageService;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务定时器
 *
 * <AUTHOR>
 * @date: 2021/7/8 15:40
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
@Component
@EnableScheduling
/**
 *  配置文件读取是否启用此配置
 */
@ConditionalOnProperty(prefix = "scheduling", name = "businessJob.enabled", havingValue = "true")
public class BusinessJob {

    @Autowired
    private WsSheetService wsSheetService;
    @Autowired
    private WsSheetTaskService wsSheetTaskService;
    @Autowired
    private WsEvaluationService wsEvaluationService;
    @Autowired
    private WsOmFileService wsOmFileService;
    @Autowired
    private WsFileService wsFileService;
    @Autowired
    private WsCustometLogService wsCustometLogService;
    @Autowired
    private WsOmMeauService wsOmMeauService;
    @Autowired
    private WsWorkReportService wsWorkReportService;
    @Autowired
    private InformationFeignService informationFeignService;
    @Autowired
    private WsWsMessageService wsMessageService;
    @Autowired
    private WsWsMessageService wsWsMessageService;
    @Autowired
    private GlobalSettingsFeignService globalSettingsFeignService;

    @Value("${evaluate}")
    private int evaluate;
    @Value("${superPeople.id}")
    private String id;
    @Value("${superPeople.name}")
    private String name;
    @Value("${om.filePassWord}")
    private String filePassWord;
    @Value("${oaUrl.scanQrCodeWxUrl}")
    private String scanQrCodeWxUrl;
    @Value("${oaUrl.workReportUrl}")
    private String workReportUrl;
    @Value("${oaUrl.workInfoUrl}")
    private String workInfoUrl;
    @Value("${oaUrl.dpdUrl}")
    private String dpdUrl;
    @Value("${oaUrl.djdUrl}")
    private String djdUrl;
    @Value("${unprocessedMessageAlerts.dpdTime}")
    private Long dpdTime;
    @Value("${unprocessedMessageAlerts.djdTime}")
    private Long djdTime;


    /**
     * 自动验收、评价
     */
    @Scheduled(cron = "${scheduling.businessJob.evaluation}")
    public void automaticEvaluationJob() {
        // 需要自动验收评价的数据
        List<WsWsSheet> wsSheets = wsSheetService
                .selectWsSheetListByWorkStatus(WorkSheetStatusEnum.ACCEPTANCE.getValue())
                .stream()
                .filter(wsSheetFilter -> DateUtils.compareTime(wsSheetFilter.getUpdateTime(), new Date(), evaluate))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(wsSheets)) {
            return;
        }
        // 验收操作
        List<WsWsTask> wsTaskDysList = Lists.newArrayList();
        // 验收节点
        List<WsWsTask> wsTaskYsList = Lists.newArrayList();
        // 评价
        List<WsWsEvaluation> evaluationList = Lists.newArrayList();
        // 工单业务主表
        List<WsWsSheet> wsSheetList = Lists.newArrayList();
        wsSheets.forEach(wsSheet -> {
            // 自动验收
            // 待验收节点完成
            WsWsTask wsTask = wsSheetTaskService.selectOneMaxTimeTaskByWorkNumber(wsSheet.getWorkNumber());
            if (null == wsTask) {
                return;
            }
            wsTask.setComplete(CommonlyConstants.YesOrNo.YES);
            wsTask.setUpdateTime(new DateTime());
            wsTaskDysList.add(wsTask);
            // 生成验收节点
            WsWsTask wsTaskYs = BeanUtil.copyProperties(wsTask, WsWsTask.class);
            wsTaskYs.setPkWsTaskId(IdUtils.getId());
            wsTaskYs.setCreateBy(null);
            wsTaskYs.setCreateByName("系统");
            wsTaskYs.setCreateTime(new Date());
            wsTaskYs.setUpdateTime(new DateTime());
            wsTaskYs.setUpdateBy(null);
            wsTaskYs.setUpdateByName(null);
            wsTaskYs.setTaskName(WorkSheetTaskEnum.COMPLETION_ACCEPTANCE.getValue());
            wsTaskYs.setTakeRemark("系统自动验收,默认评价" +
                    CommonlyConstants.Evaluation.PROCESS_SPEED + "5颗星，" +
                    CommonlyConstants.Evaluation.SERVICE_ATTITUUDE + "5颗星，" +
                    CommonlyConstants.Evaluation.TECHNICAL_LEVEL + "5颗星"
            );
            wsTaskYs.setFkFormerUserId(null);
            wsTaskYs.setFkFormerUserName(null);
            wsTaskYs.setWorkHours(0f);
            wsTaskYs.setComplete(CommonlyConstants.YesOrNo.YES);
            wsTaskYs.setAssist(CommonlyConstants.YesOrNo.NO);
            wsTaskYs.setWorkStatus(WorkSheetStatusEnum.COMPLETED.getValue());
            wsTaskYsList.add(wsTaskYs);


            // 评价操作
            WsWsEvaluation wsEvaluation = new WsWsEvaluation();
            wsEvaluation.setWorkNumber(wsSheet.getWorkNumber());
            wsEvaluation.setPkWsEvaluationId(IdUtils.getId());
            wsEvaluation.setProcessSpeed(CommonlyConstants.Evaluation.EVALUATION_DEFAULT);
            wsEvaluation.setServiceAttituude(CommonlyConstants.Evaluation.EVALUATION_DEFAULT);
            wsEvaluation.setTechnicalLevel(CommonlyConstants.Evaluation.EVALUATION_DEFAULT);
            evaluationList.add(wsEvaluation);

            // 工单主表
            WsWsSheet wsSheetTemp = new WsWsSheet();
            wsSheetTemp.setWorkNumber(wsSheet.getWorkNumber());
            wsSheetTemp.setWorkStatus(WorkSheetStatusEnum.COMPLETED.getValue());
            wsSheetTemp.setActualCompletionTime(new Date());
            wsSheetList.add(wsSheetTemp);

        });

        try {
            log.info("--------------------------工单自动验收、评价定时任执行开始" + new Date());
            wsSheetTaskService.updateBatch(wsTaskDysList);
            wsSheetTaskService.insertBatch(wsTaskYsList);
            // 自动评价
            wsEvaluationService.insertBatch(evaluationList);
            wsSheetService.updateBatch(wsSheetList);
            log.info("--------------------------工单自动验收、评价定时任执行完毕" + new Date());
        } catch (Exception e) {
            log.error("--------------------------工单自动验收、评价定时任执行失败：" + e.getMessage(), e);
        }
    }

    /**
     * 拉取OM服务器录音文件并上传
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Scheduled(cron = "${scheduling.businessJob.omFile}")
    public void recordingFileJob() {
    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	if(!"lyszyyy".equals(globalSetting.getOrgCode())) {
    		List<WsFileFile> wsFileFileList = Lists.newArrayList();
            List<WsOmFile> wsOmFileList = wsOmFileService.selectListOmFileByTime(new Date());
            if (CollectionUtil.isEmpty(wsOmFileList)) {
                return;
            }
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
            // 拉取OM服务器录音文件，并修改对应录音文件表状态，准备插入文件表数据
            wsOmFileList.forEach(temp -> {
                WsFileFile wsFileFile = new WsFileFile();
                WsFileOutVo wsFileOutVo = FileUtils.fileUploadByUrl(temp.getUrl(), filePassWord);
                // 确保语音已合成
                if (null == wsFileOutVo) {
                    return;
                }
                MyBeanUtils.copyBeanNotNull2Bean(wsFileOutVo, wsFileFile);
                wsFileFile.setPkWsFileId(IdUtils.getId());
                wsFileFile.setFkCustometLogId(temp.getFkCustometLogId());
                temp.setWhetherToUpload(1);
                wsFileFileList.add(wsFileFile);

            });

            if (CollectionUtil.isEmpty(wsFileFileList)) {
                return;
            }
            wsOmFileList = wsOmFileList.stream().filter(temp -> wsFileFileList.stream()
                    .map(WsFileFile::getFkCustometLogId)
                    .collect(Collectors.joining())
                    .contains(temp.getFkCustometLogId())
            ).collect(Collectors.toList());
            // 根据录音文件获取所有的通话记录
            List<WsCustometLog> wsCustometLogs = wsCustometLogService.selectAllById(
                    wsOmFileList
                            .stream()
                            .map(WsOmFile::getFkCustometLogId)
                            .collect(Collectors.toList())
            );
            // 根据通话记录填充录音文件的业务工单编号
            wsCustometLogs
                    .stream()
                    .map(custometLog -> {
                        wsFileFileList
                                .stream()
                                .filter(fileFile -> custometLog.getPkCustometLogId().equals(fileFile.getFkCustometLogId()))
                                .forEach(temp -> temp.setWorkNumber(custometLog.getWorkNumber()));
                        return null;
                    })
                    .collect(Collectors.toList());

            if (!CollectionUtil.isEmpty(wsFileFileList) && !CollectionUtil.isEmpty(wsOmFileList)) {
                wsOmFileService.updateBatchWhetherToUpload(wsOmFileList);
                wsFileService.insertBatchFile(wsFileFileList);
            }
            // 延迟处理被叫方为移动电话，已接听
            if (!CollectionUtil.isEmpty(wsCustometLogs)) {
                wsCustometLogService.updateBatchWsCustometLog(wsCustometLogs);
            }
            //报修电话系统中找不到导致接听状态不对 需要根据录音文件判断更新为1
            wsCustometLogService.updateCalltype();  
    	}
        
    }

    /**
     * 工作日报定时推送
     */
    @Scheduled(cron = "${scheduling.businessJob.workReport}")
    public void workReport() {
    	
    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	if(!"lyszyyy".equals(globalSetting.getOrgCode())) {
    		List<WsOmMeauListOutVo> meauList = wsOmMeauService.selectOmMeauAllList();
            if (CollectionUtil.isEmpty(meauList)) {
                return;
            }
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
            meauList.forEach(meau -> {
                // 该机构下所有员工
                List<EmployeeResp> employeeRespAllList = FeignInfoUitls.getEmployeeRespAllListByEqOrgId(meau.getDeptId());
                if (CollectionUtil.isEmpty(employeeRespAllList)) {
                    return;
                }
                Map<String, Object> serviceDeskStatisticsToday = wsCustometLogService.serviceDeskStatisticsToday(
                        meau.getDeptId(),
                        DateUtils.getDayStart(),
                        DateUtils.getDayEnd()
                );
                // 服务台页签业务条数
                Map<String, Object> countGroupBy = wsSheetService.selectCountGroupByWorkNumber(meau.getDeptId());

                List<Map<String, Object>> levelOneFaultTypeDatas = wsSheetService.getLevelOneFaultTypeDatas(
                        DateUtils.getDayStart(),
                        DateUtils.getDayEnd(),
                        meau.getDeptId()
                );

                // 微信卡片组装数据
                String callSituation =
                        "通话情况：接听" + serviceDeskStatisticsToday.get("yj") + "个，" +
                                "未接" + serviceDeskStatisticsToday.get("wj") + "个，" +
                                "呼出" + serviceDeskStatisticsToday.get("hc") + "个;" +
                                "（其中电话已解决" + serviceDeskStatisticsToday.get("dhjj") + "个，" +
                                "无效来电" + serviceDeskStatisticsToday.get("wxjd") + "个）";
                String workSituation =
                        "工单情况：科室新增工单" + serviceDeskStatisticsToday.get("jd") + "个，" +
                                "当前待派单" + countGroupBy.get("15") + "个，" +
                                "处理中" + countGroupBy.get("10") + "个，" +
                                "未建单" + countGroupBy.get("12") + "个";

                // 移动端工单日报详情数据，组装
                WsWorkReportOutVo workReportOutVo = new WsWorkReportOutVo(
                        serviceDeskStatisticsToday.get("yj") + "",
                        IndexEnum.ZERO.getValue() ==
                                Float.parseFloat(serviceDeskStatisticsToday.get("yj") + "") +
                                        Float.parseFloat(serviceDeskStatisticsToday.get("wj") + "") ?
                                "0" :
                                String.format(
                                        "%.2f",
                                        (Float.parseFloat(serviceDeskStatisticsToday.get("yj") + "") /
                                                (Float.parseFloat(serviceDeskStatisticsToday.get("yj") + "") +
                                                        Float.parseFloat(serviceDeskStatisticsToday.get("wj") + ""))
                                        ) * 100
                                ),
                        serviceDeskStatisticsToday.get("dhjj") + "",
                        IndexEnum.ZERO.getValue() ==
                                Float.parseFloat(serviceDeskStatisticsToday.get("yj") + "") +
                                        Float.parseFloat(serviceDeskStatisticsToday.get("wj") + "") ?
                                "0" :
                                String.format(
                                        "%.2f",
                                        (Float.parseFloat(serviceDeskStatisticsToday.get("dhjj") + "") /
                                                (Float.parseFloat(serviceDeskStatisticsToday.get("yj") + "") +
                                                        Float.parseFloat(serviceDeskStatisticsToday.get("wj") + ""))
                                        ) * 100
                                ),
                        serviceDeskStatisticsToday.get("wxjd") + "",
                        serviceDeskStatisticsToday.get("wj") + "",
                        wsCustometLogService.selectDayByDeptId(meau.getDeptId()),
                        serviceDeskStatisticsToday.get("hc") + "",
                        serviceDeskStatisticsToday.get("jd") + "",
                        countGroupBy.get("15") + "",
                        countGroupBy.get("10") + "",
                        countGroupBy.get("12") + "",
                        levelOneFaultTypeDatas

                );
                WsWrokReport wrokReport = new WsWrokReport(
                        IdUtils.getId(),
                        meau.getDeptId(),
                        meau.getDeptName(),
                        DateUtils.dateToStringFormat("yyyy-MM-dd", new Date()),
                        callSituation + "<javascript><br/></javascript>" + workSituation,
                        JSON.toJSONString(workReportOutVo),
                        new Date()
                );
                // 保存工单日报
                wsWorkReportService.insert(wrokReport);
                log.info("工单日报企业微信推送开始：" + new Date());
                // 企业微信消息推送
               PlatformResult<String> stringPlatformResult = informationFeignService.sendNotice(
                        NoticeReq.builder()
                                .content(callSituation + "<javascript><br/></javascript>" + workSituation)
                                .sender(id)
                                .senderName(name)
                                .noticeType("3")
                                .receiver(
                                        employeeRespAllList.stream()
                                                .map(EmployeeResp::getEmployeeNo)
                                                .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue()))
                                )
                                .subject("运维日报（" + DateUtils.dateToStringFormat("MM-dd", new Date()) + "）")
                                .wxSendType("2")
                                .url(scanQrCodeWxUrl + "?loginType=1&url=" + new String(Base64.encodeBase64((workReportUrl + "?pkWorkReportId=" + wrokReport.getPkWorkReportId()).getBytes()), StandardCharsets.UTF_8))
                                .build()
                );
                if (!stringPlatformResult.isSuccess()) {
                    log.error("工单日报企业微信推送失败：" + stringPlatformResult.getMessage());
                } else {
                    log.info("工单日报企业微信推送完成：" + new Date());
                }
            });
    	}
    }

    /**
     * 待派单长时间未处理，消息提醒
     */
    @Scheduled(cron = "${scheduling.businessJob.unprocessedMessageAlertsdpd}")
    public void unprocessedMessageAlertsdpd() {
    	
    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	if(!"lyszyyy".equals(globalSetting.getOrgCode())) {
    		// 所有处理科室
            List<WsOmMeauListOutVo> meauList = wsOmMeauService.selectOmMeauAllList();
            if (CollectionUtil.isEmpty(meauList)) {
                return;
            }
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
            meauList.forEach(meau -> {
                // 超时工单号
                List<String> workNumber = Lists.newArrayList();
                
                List<EmployeeResp> employeeRespAllList = null;
                
                //北海二只查当天值班员工
                if("bhsdermyy".equals(globalSetting.getOrgCode())) {
                	 employeeRespAllList = wsWsMessageService.getSchedule(meau.getDeptId());
                }else {
                	 // 该科室下所有员工
                     employeeRespAllList = FeignInfoUitls.getEmployeeRespAllListByEqOrgId(meau.getDeptId());
                    
                }
                
                
                log.info("待派单长时间未处理，消息提醒工号:" +employeeRespAllList.stream()
                .map(EmployeeResp::getEmployeeNo)
                .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue())));
               
                
                if (CollectionUtil.isEmpty(employeeRespAllList)) {
                    return;
                }
               
                // 需推送工单信息
                List<Map<String, Object>> unprocessedMessageAlertsDpd = wsSheetService.unprocessedMessageAlertsDpdDjd(
                        WorkSheetStatusEnum.SNET.getValue(),
                        meau.getDeptId(),
                        Arrays.asList("-1,-3,-5,-6".split(","))
                );
                if (CollectionUtil.isEmpty(unprocessedMessageAlertsDpd)) {
                    return;
                }
                // 已推送过消息的工单
                List<Map<String, Object>> workNumberMessages = wsMessageService.selectOneDpdDjdByWorkNumber(
                        unprocessedMessageAlertsDpd.stream()
                                .map(temp -> temp.get("workNumber") + "")
                                .collect(Collectors.toList()),
                        WorkSheetStatusEnum.SNET.getValue()
                );
                // 是否推送过，需再推送
                Optional.ofNullable(workNumberMessages).map(messageList -> {
                    messageList.forEach(message -> {
                        Date createTime = DateUtils.stringtoDate("yyyy-MM-dd HH:mm:ss", message.get("createTime") + "");
                        if (System.currentTimeMillis() - createTime.getTime() >= dpdTime) {
                            workNumber.add(message.get("workNumber") + "");
                        }
                    });
                    return null;
                });
                // 过滤无需提醒的工单
                List<Map<String, Object>> unprocessedMessageAlertsDpdFilter = unprocessedMessageAlertsDpd.stream()
                        .filter(temp -> {
                            AtomicReference<Boolean> flag = new AtomicReference<>(true);
                            workNumberMessages.forEach(workNumberMessage -> {
                                if ((temp.get("workNumber") + "").equals((workNumberMessage.get("workNumber") + ""))) {
                                    flag.set(false);
                                    return;
                                }
                            });
                            return flag.get();
                        })
                        .collect(Collectors.toList());

                // 未推送过的工单是否存在需要推送工单
                unprocessedMessageAlertsDpdFilter.forEach(temp -> {
                    Date createTime = DateUtils.stringtoDate("yyyy-MM-dd HH:mm:ss", temp.get("createTime") + "");
                    if (System.currentTimeMillis() - createTime.getTime() >= dpdTime) {
                        workNumber.add(temp.get("workNumber") + "");
                    }
                });
                // 无数据，无需推送
                if (CollectionUtil.isEmpty(workNumber)) {
                    return;
                }
                String title = "存在有新的待派工单";
                String content = "当前还有" + unprocessedMessageAlertsDpd.size() + "单没派，报修科室为" +
                        unprocessedMessageAlertsDpd.stream()
                                .map(temp -> temp.get("repairManDeptName") + "")
                                .distinct()
                                .collect(Collectors.joining("、"));
                log.info("工单待派单提醒企业微信推送开始：" + new Date());
                // 企业微信消息推送
                PlatformResult<String> stringPlatformResult = informationFeignService.sendNotice(
                        NoticeReq.builder()
                                .content(content)
                                .sender(id)
                                .senderName(name)
                                .noticeType("3")
                                .receiver(
                                        employeeRespAllList.stream()
                                                .map(EmployeeResp::getEmployeeNo)
                                                .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue()))
                                )
                                .subject(title)
                                .wxSendType("1")
                                .url(dpdUrl+"&isWeChat=1")
                                .build()
                );
                if (!stringPlatformResult.isSuccess()) {
                    log.error("工单待派单提醒企业微信推送失败：" + stringPlatformResult.getMessage());
                } else {
                    log.info("工单待派单提醒企业微信推送完成：" + new Date());

                    // 保存消息信息
                    unprocessedMessageAlertsDpd.forEach(temp -> {
                        // 存入消息推送记录
                        WsWsMessage message = new WsWsMessage(
                                IdUtils.getId(),
                                temp.get("workNumber") + "",
                                title,
                                content,
                                null,
                                ""
                        );
                        message.setWorkStatus(WorkSheetStatusEnum.SNET.getValue());
                        wsWsMessageService.insertMessage(message);
                    });

                }

            });
    	}
    }


    /**
     * 待接单长时间未处理，消息提醒
     */
    @Scheduled(cron = "${scheduling.businessJob.unprocessedMessageAlertsdjd}")
    public void unprocessedMessageAlertsdjd() {
    	
    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	if(!"lyszyyy".equals(globalSetting.getOrgCode())) {
    		// 所有处理科室
            List<WsOmMeauListOutVo> meauList = wsOmMeauService.selectOmMeauAllList();
            if (CollectionUtil.isEmpty(meauList)) {
                return;
            }
            UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);//模拟登录
            meauList.forEach(meau -> {
                // 超时工单号
                List<String> workNumber = Lists.newArrayList();
                // 该科室下所有员工
                List<EmployeeResp> employeeRespAllList = FeignInfoUitls.getEmployeeRespAllListByEqOrgId(meau.getDeptId());
                if (CollectionUtil.isEmpty(employeeRespAllList)) {
                    return;
                }
                // 需推送工单信息
                List<Map<String, Object>> unprocessedMessageAlertsDjd = wsSheetService.unprocessedMessageAlertsDpdDjd(
                        WorkSheetStatusEnum.WAITING.getValue(),
                        meau.getDeptId(),
                        Arrays.asList("-1,-5".split(","))
                );
                if (CollectionUtil.isEmpty(unprocessedMessageAlertsDjd)) {
                    return;
                }
                // 已推送过消息的工单
                List<Map<String, Object>> workNumberMessages = wsMessageService.selectOneDpdDjdByWorkNumber(
                        unprocessedMessageAlertsDjd.stream()
                                .map(temp -> temp.get("workNumber") + "")
                                .collect(Collectors.toList()),
                        WorkSheetStatusEnum.WAITING.getValue()
                );
                // 是否推送过，需再推送
                Optional.ofNullable(workNumberMessages).map(messageList -> {
                    messageList.forEach(message -> {
                        Date createTime = DateUtils.stringtoDate("yyyy-MM-dd HH:mm:ss", message.get("createTime") + "");
                        if (System.currentTimeMillis() - createTime.getTime() >= djdTime) {
                            workNumber.add(message.get("workNumber") + "");
                        }
                    });
                    return null;
                });
                
                // 过滤无需提醒的工单
                if(CollectionUtil.isNotEmpty(workNumberMessages) ) {
                	 List<Map<String, Object>> unprocessedMessageAlertsDjdFilter = unprocessedMessageAlertsDjd.stream()
                             .filter(temp -> {
                                 AtomicReference<Boolean> flag = new AtomicReference<>(true);
                                 workNumberMessages.forEach(workNumberMessage -> {
                                     if ((temp.get("workNumber") + "").equals((workNumberMessage.get("workNumber") + ""))) {
                                         flag.set(false);
                                         return;
                                     }
                                 });
                                 return flag.get();
                             })
                             .collect(Collectors.toList());
                	// 未推送过的工单是否存在需要推送工单
                     unprocessedMessageAlertsDjdFilter.forEach(temp -> {
                         Date createTime = DateUtils.stringtoDate("yyyy-MM-dd HH:mm:ss", temp.get("createTime") + "");
                         if (System.currentTimeMillis() - createTime.getTime() >= djdTime) {
                             workNumber.add(temp.get("workNumber") + "");
                         }
                     });
                }
               
                // 无数据，无需推送
                if (CollectionUtil.isEmpty(workNumber)) {
                    return;
                }
                String title = "存在有新的待接工单";
                String content = "当前还有" + unprocessedMessageAlertsDjd.size() + "单待接";
                log.info("工单待接单提醒企业微信推送开始：" + new Date());
                // 企业微信消息推送
                PlatformResult<String> stringPlatformResult = informationFeignService.sendNotice(
                        NoticeReq.builder()
                                .content(content)
                                .sender(id)
                                .senderName(name)
                                .noticeType("3")
                                .receiver(
                                        employeeRespAllList.stream()
                                                .map(EmployeeResp::getEmployeeNo)
                                                .collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue()))
                                )
                                .subject(title)
                                .wxSendType("1")
                                .url(djdUrl+"&workStatusValue=2")
                                .build()
                );
                if (!stringPlatformResult.isSuccess()) {
                    log.error("工单待接单提醒企业微信推送：" + stringPlatformResult.getMessage());
                } else {
                    log.info("工单待接单提醒企业微信推送完成：" + new Date());

                    // 保存消息信息
                    unprocessedMessageAlertsDjd.forEach(temp -> {
                        // 存入消息推送记录
                        WsWsMessage message = new WsWsMessage(
                                IdUtils.getId(),
                                temp.get("workNumber") + "",
                                title,
                                content,
                                null,
                                ""
                        );
                        message.setWorkStatus(WorkSheetStatusEnum.WAITING.getValue());
                        wsWsMessageService.insertMessage(message);
                    });

                }

            });
    	}
    }


    /**
     * 填充录音文件工单号
     */
    @Scheduled(cron = "${scheduling.businessJob.fillFileWorkNumberInfo}")
    public void fillFileWorkNumberInfo() {
    	
    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	if(!"lyszyyy".equals(globalSetting.getOrgCode())) {
    		// 获取需填充工单号的录音文件信息
            List<Map<String, Object>> fillFileWorkNumberInfo = wsCustometLogService.fillFileWorkNumberInfo();
            if (CollectionUtil.isEmpty(fillFileWorkNumberInfo)) {
                return;
            }
            List<WsFileFile> fileList = wsFileService.selectAllByCustometLogIdList(
                    fillFileWorkNumberInfo.stream()
                            .map(temp -> temp.get("pk_customet_log_id") + "")
                            .collect(Collectors.toList())
            );
            fileList.forEach(file -> {
                fillFileWorkNumberInfo.forEach(fillFileWorkNumberInfoTemp -> {
                    if (file.getFkCustometLogId().equals(fillFileWorkNumberInfoTemp.get("pk_customet_log_id") + "")) {
                        file.setWorkNumber(fillFileWorkNumberInfoTemp.get("work_number") + "");
                    }
                });

            });
            try {
                if(!CollectionUtil.isEmpty(fileList)){
                    log.info("--------------------------定时填充录音文件记录工单号开始");
                    wsFileService.updateBatchFile(fileList);
                    log.info("--------------------------定时填充录音文件记录工单号完成");
                }
            } catch (Exception e) {
                log.info("--------------------------定时填充录音文件记录工单号失败:" + e.getMessage());
            }
    	}
    }

}
