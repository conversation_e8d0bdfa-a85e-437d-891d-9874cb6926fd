package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/26 18:06
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Setter
@Getter
public class WsCustometLogModifyingCallRecordsInputVo {

    @NotNull(message = "坐席通话记录ID不能为空")
    @ApiModelProperty(value = "坐席通话记录ID")
    private String pkCustometLogId;

    @NotNull(message = "通话类型不能为空")
    @ApiModelProperty(value = "通话类型(未接0，已创建工单1)")
    private int callType;

    @NotNull(message = "通话记录业务状态不能为空")
    @ApiModelProperty(value = "通话记录业务状态(2无效来电、5回拨)")
    private int callWorkStatus;

}
