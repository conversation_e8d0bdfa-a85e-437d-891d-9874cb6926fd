package cn.trasen.worksheet.module.dto.inputVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class WsWsCostPageListInputVo {

    @ApiModelProperty(value = "处理科室id")
    private String businessDeptId;

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "费用发生时间开始(yyyy-MM-dd)")
    private Date costBeiginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "费用发生时间结束(yyyy-MM-dd)")
    private Date costEndTime;

    @ApiModelProperty(value = "费用填报科室id")
    private String fillDeptId;

    @ApiModelProperty(value = "费用填报人id")
    private String fillUserId;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "费用填报时间开始(yyyy-MM-dd)")
    private Date createBeiginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "费用填报时间结束(yyyy-MM-dd)")
    private Date createEndTime;

    @ApiModelProperty(value = "费用状态（0未报销，1审核中、已报销2） 见字典，编码为COST_STATUS")
    private Integer costStatus;

}