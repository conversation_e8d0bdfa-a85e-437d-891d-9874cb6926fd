package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/26 17:57
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Setter
@Getter
public class WsCustometLogWorkSheetOutVo {

    @ApiModelProperty(value = "当前通话中坐席记录")
    private WsCustometLogOutVo wsCustometLogOutVo;

    @ApiModelProperty(value = "未接坐席记录")
    private List<WsCustometLogOutVo> wsCustometLogOutVoList;

}
