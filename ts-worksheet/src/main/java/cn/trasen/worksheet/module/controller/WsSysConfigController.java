package cn.trasen.worksheet.module.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsSysConfigSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsSysConfigInfoOutVo;
import cn.trasen.worksheet.module.service.WsSysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date: 2021/7/22 10:51
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "工单系统设置")
@RestController
public class WsSysConfigController {

    @Autowired
    private WsSysConfigService wsSysConfigService;

    @ControllerLog(description="工单系统设置信息")
    @ApiOperation(value = "工单系统设置信息", notes = "工单系统设置信息")
    @GetMapping("/sysConfig")
    public PlatformResult<WsSysConfigInfoOutVo> getOneSysConfig(){
        return PlatformResult.success(wsSysConfigService.getOne());
    }

    @ControllerLog(description="保存、修改工单系统设置信息")
    @ApiOperation(value = "保存、修改工单系统设置信息", notes = "保存、修改工单系统设置信息")
    @PostMapping("/sysConfig")
    public PlatformResult<Integer> sysConfig(@RequestBody WsSysConfigSaveInputVo sysConfigSaveInputVo){
        return PlatformResult.success(wsSysConfigService.sysConfig(sysConfigSaveInputVo));
    }
}
