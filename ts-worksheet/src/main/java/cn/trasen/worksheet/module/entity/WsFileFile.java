package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;

import javax.persistence.*;
import lombok.*;

@Table(name = "ws_file_file")
@Setter
@Getter
public class WsFileFile extends WsBase{
    /**
     * 文件id
     */
    @Column(name = "pk_ws_file_id")
    @ApiModelProperty(value = "文件id")
    private String pkWsFileId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 文件id
     */
    private String fkFileId;

    private String fkFileName;

    /**
     * 文件后缀
     */
    @ApiModelProperty(value = "文件后缀")
    private String fileSuffix;

    /**
     * 文件访问URL
     */
    @Column(name = "file_url")
    @ApiModelProperty(value = "文件访问URL")
    private String fileUrl;


    @ApiModelProperty(value = "通话记录id")
    private String fkCustometLogId;

    @ApiModelProperty(value = "节点id")
    private String fkWsTaskId;


}