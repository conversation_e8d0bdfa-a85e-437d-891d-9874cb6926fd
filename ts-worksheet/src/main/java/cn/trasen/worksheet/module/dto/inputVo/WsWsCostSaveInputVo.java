package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class WsWsCostSaveInputVo {

    @ApiModelProperty(value = "费用id")
    private String pkWsCostId;

    @NotBlank(message = "工单编号不能为空")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @NotNull(message = "金额（元）不能为空")
    @ApiModelProperty(value = "金额（元），最多输入两位小数")
    private Float money;

    @NotBlank(message = "费用描述不能为空")
    @ApiModelProperty(value = "费用描述")
    private String costDeion;

    @ApiModelProperty(value = "发生时间(yyyy-mm-dd HH:MM)")
    private Date costTime;

    @ApiModelProperty(value = "附件业务id")
    private String files;
}