package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_om_file")
@Setter
@Getter
public class WsOmFile extends WsBase{
    /**
     * OM文件id
     */
    @Column(name = "pk_omfile_id")
    @ApiModelProperty(value = "OM文件id")
    private String pkOmfileId;

    /**
     * 访问OM服务器URL
     */
    @ApiModelProperty(value = "访问OM服务器URL")
    private String url;

    @ApiModelProperty(value = "通话记录id")
    private String fkCustometLogId;

    /**
     * 是否上传文件服务器（0否1是）
     */
    @Column(name = "whether_to_upload")
    @ApiModelProperty(value = "是否上传文件服务器（0否1是）")
    private int whetherToUpload = 0;
}