package cn.trasen.worksheet.module.service.impl;


import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsHospitalDistrictSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsHospitalDistrictListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.entity.WsHospitalDistrict;
import cn.trasen.worksheet.module.mapper.WsHospitalDistrictMapper;
import cn.trasen.worksheet.module.service.WsHospitalDistrictService;
import tk.mybatis.mapper.entity.Example;

/**
 * 工单院区配置
 */
@Service
public class WsHospitalDistrictImpl implements WsHospitalDistrictService {


    @Autowired
    private WsHospitalDistrictMapper mapper;


    /**
     * 新增、修改工单院区配置
     *
     * @param hospitalDistrictSaveInputVo
     * @return
     */
    @Override
    public Integer hospitalDistrict(WsHospitalDistrictSaveInputVo hospitalDistrictSaveInputVo) {
        WsHospitalDistrict hospitalDistrict = BeanUtil.copyProperties(hospitalDistrictSaveInputVo, WsHospitalDistrict.class);
        return Optional.ofNullable(hospitalDistrict.getPkHospitalDistrictId())
                .map(temp -> modifyHospitalDistrict(hospitalDistrict))
                .orElseGet(() -> saveHospitalDistrict(hospitalDistrict));
    }

    /**
     * 查询工单院区配置信息
     *
     * @return
     */
    @Override
    public List<WsHospitalDistrictListOutVo> getHospitalDistrictList() {
        return BeanUtil.copyToList(mapper.selectAll(), WsHospitalDistrictListOutVo.class);
    }

    /**
     * 所有院区信息
     *
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    @Override
    public List<WsWorkFlowListOutVo> getHospitalDistrictEnableList(String hospitalDistrictStatus) {
        return getAllList(hospitalDistrictStatus).stream()
                .map(temp -> {
                    WsWorkFlowListOutVo workFlowOutVo = new WsWorkFlowListOutVo();
                    workFlowOutVo.setItemValue(temp.getPkHospitalDistrictId());
                    workFlowOutVo.setItemName(temp.getHospitalDistrictName());
                    return workFlowOutVo;
                }).collect(Collectors.toList());
    }


    /**
     * 启用禁用
     *
     * @param pkHospitalDistrictId   院区id
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    @Override
    public Integer enableOrDisable(String pkHospitalDistrictId, String hospitalDistrictStatus) {
        Example example = new Example(WsHospitalDistrict.class);
        example.createCriteria()
                .andEqualTo("pkHospitalDistrictId", pkHospitalDistrictId);
        return mapper.updateByExampleSelective(
                new WsHospitalDistrict(pkHospitalDistrictId, hospitalDistrictStatus),
                example
        );
    }

    /**
     * 所有院区信息
     *
     * @param hospitalDistrictStatus 0禁用，1启用
     * @return
     */
    @Override
    public List<WsHospitalDistrict> getAllList(String hospitalDistrictStatus) {
        return mapper.selectAll()
                .stream()
                .filter(hospitalDistric -> hospitalDistrictStatus.equals(hospitalDistric.getHospitalDistrictStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 单个院区信息
     *
     * @param pkHospitalDistrictId 院区id
     * @return
     */
    @Override
    public WsHospitalDistrict getOne(String pkHospitalDistrictId) {
        if (StringUtils.isEmpty(pkHospitalDistrictId)) {
            return new WsHospitalDistrict();
        }
        Example example = new Example(WsHospitalDistrict.class);
        example.createCriteria()
                .andEqualTo("pkHospitalDistrictId", pkHospitalDistrictId)
                .andEqualTo("hospitalDistrictStatus", IndexEnum.ONE.getValue());
        return Optional.ofNullable(mapper.selectOneByExample(example))
                .map(WsHospitalDistrict::get)
                .orElseGet(() -> new WsHospitalDistrict());
    }

    /**
     * 新增
     *
     * @param hospitalDistrict
     * @return
     */
    public Integer saveHospitalDistrict(WsHospitalDistrict hospitalDistrict) {
        hospitalDistrict.setPkHospitalDistrictId(IdUtils.getId());
        hospitalDistrict.setDeleteStatus(IndexEnum.ZERO.getValue());
        hospitalDistrict.setHospitalDistrictStatus(IndexEnum.ONE.getValue() + "");
        hospitalDistrict.setCreateBy(UserInfoHolder.getCurrentUserId());
        hospitalDistrict.setCreateTime(new Date());
        return mapper.insert(hospitalDistrict);
    }

    /**
     * 修改
     *
     * @param hospitalDistrict
     * @return
     */
    public Integer modifyHospitalDistrict(WsHospitalDistrict hospitalDistrict) {
        hospitalDistrict.setUpdateBy(UserInfoHolder.getCurrentUserId());
        hospitalDistrict.setUpdateTime(new Date());
        Example example = new Example(WsHospitalDistrict.class);
        example.createCriteria()
                .andEqualTo("pkHospitalDistrictId", hospitalDistrict.getPkHospitalDistrictId());
        return mapper.updateByExampleSelective(hospitalDistrict, example);
    }


}
