package cn.trasen.worksheet.module.dto.inputVo;

import cn.trasen.worksheet.module.entity.WsBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.function.Supplier;

@Table(name = "ws_external_personnel")
@Setter
@Getter
public class WsExternalPersonnelInputVo {

    @ApiModelProperty(value = "外部人员id")
    private String pkExternalPersonnelId;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "所属机构")
    private String institutionalAffiliations;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "人员姓名")
    private String userName;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "联系方式")
    private String phone;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "职责说明")
    private String jobDeion;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "状态（0停用1启用）")
    private int status;

    @NotNull(message = "故障类型ID不能为空")
    @Column(name = "belongs_dept_id")
    @ApiModelProperty(value = "所属管辖科室id")
    private String belongsDeptId;

    @NotNull(message = "故障类型ID不能为空")
    @ApiModelProperty(value = "所属管辖科室名称")
    private String belongsDeptName;

}