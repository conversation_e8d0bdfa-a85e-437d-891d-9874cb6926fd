package cn.trasen.worksheet.module.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsEvaluateInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsEvaluationTopOutVo;
import cn.trasen.worksheet.module.entity.WsWsEvaluation;

/**
 * <AUTHOR>
 * @date: 2021/6/21 16:18
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsEvaluationService {

    void insert(WsWsEvaluation wsEvaluation);

    PlatformResult workSheetToEvaluate(WsWsEvaluateInputVo wsEvaluateInputVo);

    void insertBatch(List<WsWsEvaluation> wsEvaluationList);

    /**
     * 工单综合评分
     *
     * @param fkUserId 用户id（不传为查询所有工单）
     * @return
     */
    Map<String, Object> getComprehensiveScoreOfWorkOrder(String fkDeptId, String fkUserId, String beginTime, String endTime);

    /**
     * 完成质量分析-总评分、处理速度、服务态度、技术水平
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    Map<String, Object> geTScoreAnalysis(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）的评价等级
     *
     * @return
     */
    Map<String, Object> getEvaluationLevel(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 工单各评价类型（总评分、处理速度、服务态度、技术水平）平均分
     *
     * @return
     */
    Map<String, Object> getEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 各科室工单评价各类型平均分及总评分平均分
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getDeptEvaluationAverageScore(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 查询用户处理工单评价分
     *
     * @param fkDeptId  科室id
     * @param beginTime
     * @param endTime
     * @param limit     返回条数
     * @return
     */
    List<WsEvaluationTopOutVo> getUserEvaluationAverageScore(String fkDeptId,
                                                       String beginTime,
                                                       String endTime,
                                                       Integer limit);

    /**
     * 查询各科室某段时间内，科室评分
     *
     * @param page
     * @return
     */
    List<Map<String, Object>> getEvaluationGroupByDeptAverageScore(Page page);

}
