package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;

import javax.persistence.*;
import lombok.*;

@Table(name = "ws_ws_hasten")
@Setter
@Getter
public class WsWsHasten extends WsBase{
    /**
     * 工单催办ID
     */
    @Column(name = "pk_ws_hasten_id")
    @ApiModelProperty(value = "工单催办ID")
    private String pkWsHastenId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    /**
     * 催办节点ID
     */
    @Column(name = "fk_ws_task_id")
    @ApiModelProperty(value = "催办节点ID")
    private String fkWsTaskId;

    /**
     * 催办节点名称
     */
    @Column(name = "task_name")
    @ApiModelProperty(value = "催办节点名称")
    private String taskName;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}