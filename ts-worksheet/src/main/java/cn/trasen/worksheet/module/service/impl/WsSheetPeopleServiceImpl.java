package cn.trasen.worksheet.module.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.EmployeeListReq;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.PageDataReq;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetPeoppleInputVo;
import cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultTypeTreeOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.entity.WsFaultType;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import cn.trasen.worksheet.module.service.WsFaultTypeService;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetPeopleService;
import cn.trasen.worksheet.module.service.WsSheetService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/6/26 16:58
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
@Slf4j
public class WsSheetPeopleServiceImpl implements WsSheetPeopleService {

    @Autowired
    private HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    private WsExternalPersonnelService wsExternalPersonnelService;
    @Autowired
    private WsOmMeauService wsOmMeauService;
    @Autowired
    private WsFaultTypeService wsFaultTypeService;
    @Autowired
    private WsSheetService wsSheetService;
    @Value("${externalPersonnel.ORG_ID}")
    private String defaultOrgId;

    @Override
    public WsSheetPeopleInfoOutVo loginPersonInfo() {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 工单业务管理员，查看用户所在科室所有业务
        Boolean isBusinessexception = false;
        // 服务台websocket权限
        Boolean isWebSocket = false;
        // 工单院领导权限
        Boolean isLead = false;
        // 工单院领导权限
        Boolean statisticsData = false;
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.WORK_SHEET_BUSINESSEXCEPTION_ROLE)) {
            isBusinessexception = true;
        }
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.TS_WORKSHEET_SERVICE_COUNTER)) {
            isWebSocket = true;
        }
        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.TS_WORKSHEET_LEAD)) {
            isLead = true;
        }

        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.STATISTICS_DATA)) {
            statisticsData = true;
        }

        return new WsSheetPeopleInfoOutVo(
                UserInfoHolder.getCurrentUserId(),
                UserInfoHolder.getCurrentUserName(),
                currentUserInfo.getMobileNo(),
                currentUserInfo.getDeptId(),
                currentUserInfo.getDeptname(),
                // 服务台websocket权限
                isWebSocket,
                // 工单业务管理员，查看用户所在科室所有业务
                isBusinessexception,
                // 工单院领导权限
                isLead,
                statisticsData,
                // 数据权限科室id
                currentUserInfo.getOrgRang().replace("')", "").replace("('", "").replace("'", "") + "," + currentUserInfo.getDeptId(),
                // 当前人所属科室是否为处理科室
                CollectionUtil.isEmpty(
                        wsOmMeauService.selectOmMeauAllList()
                                .stream()
                                .filter(temp -> temp.getDeptId().equals(currentUserInfo.getDeptId()))
                                .collect(Collectors.toList())
                ) ? false : true
        );
    }

    /**
     * 组合人员信息分页（OA人员、外部人员）
     *
     * @param page
     * @param wsWorkSheetPeoppleInputVo
     * @return
     */
    public List<WsSheetPeopleInfoOutVo> getOAAndExternalPageList(Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        // 外部人员分页信息
        Page pageExternal = new Page();
        pageExternal.setPageNo(page.getPageNo());
        pageExternal.setPageSize(page.getPageSize() / 2);
        List<ExternalPersonnelPageListOutVo> externalPersonnelPageListOutVos = wsExternalPersonnelService.selectPageList(
                pageExternal,
                StringUtils.isEmpty(wsWorkSheetPeoppleInputVo.getEmployeeName()) ? wsWorkSheetPeoppleInputVo.getDeptName() : wsWorkSheetPeoppleInputVo.getEmployeeName(),
                StringUtils.isEmpty(wsWorkSheetPeoppleInputVo.getDeptId()) ? null : wsWorkSheetPeoppleInputVo.getDeptId().replace(CuttingOperatorEnum.COMMA.getValue(), "")
        );
        // OA人员分页信息
        PageDataReq<EmployeeListReq> reqPageDataReq = new PageDataReq();
        reqPageDataReq.setPageNum(page.getPageNo());
        reqPageDataReq.setPageSize(page.getPageSize() - externalPersonnelPageListOutVos.size());

        EmployeeListReq employeeListReq = new EmployeeListReq();
        employeeListReq.setEmployeeName(wsWorkSheetPeoppleInputVo.getEmployeeName());
        employeeListReq.setOrgName(wsWorkSheetPeoppleInputVo.getDeptName());
        employeeListReq.setOrgId(wsWorkSheetPeoppleInputVo.getDeptId());
        reqPageDataReq.setData(employeeListReq);

        // OA人员信息
        DataSet<EmployeeResp> employeePageList = new DataSet<>();
        try {
            employeePageList = hrmsEmployeeFeignService.getEmployeePageList(reqPageDataReq);
        } catch (Exception e) {
            log.error("查询OA人员信息失败：" + e.getMessage());
            e.printStackTrace();
        }
        // 组装返回数据
        List<WsSheetPeopleInfoOutVo> returnVo = Lists.newArrayList();

        employeePageList.getRows().forEach(employeeResp -> {
            WsSheetPeopleInfoOutVo wsSheetPeopleInfoOutVo = new WsSheetPeopleInfoOutVo();
            wsSheetPeopleInfoOutVo.setUserId(employeeResp.getEmployeeId());
            wsSheetPeopleInfoOutVo.setName(employeeResp.getEmployeeName());
            wsSheetPeopleInfoOutVo.setDeptId(employeeResp.getOrgId());
            wsSheetPeopleInfoOutVo.setDeptName(employeeResp.getOrgName());
            wsSheetPeopleInfoOutVo.setSex(StringUtil.isEmpty(employeeResp.getGender()) ? null : employeeResp.getGender());
            wsSheetPeopleInfoOutVo.setSexText(StringUtil.isEmpty(employeeResp.getGenderText()) ? null : employeeResp.getGenderText());
            wsSheetPeopleInfoOutVo.setUrl(StringUtil.isEmpty(employeeResp.getAvatar()) ? null : employeeResp.getAvatar());
            returnVo.add(wsSheetPeopleInfoOutVo);
        });

        externalPersonnelPageListOutVos.forEach(externalPersonnel -> {
            WsSheetPeopleInfoOutVo wsSheetPeopleInfoOutVo = new WsSheetPeopleInfoOutVo();
            wsSheetPeopleInfoOutVo.setUserId(externalPersonnel.getPkExternalPersonnelId());
            wsSheetPeopleInfoOutVo.setName(externalPersonnel.getUserName());
            wsSheetPeopleInfoOutVo.setDeptId(externalPersonnel.getInstitutionalAffiliations());
            wsSheetPeopleInfoOutVo.setDeptName(externalPersonnel.getInstitutionalAffiliations());
            wsSheetPeopleInfoOutVo.setSex(null);
            wsSheetPeopleInfoOutVo.setSexText(null);
            wsSheetPeopleInfoOutVo.setUrl(null);
            returnVo.add(wsSheetPeopleInfoOutVo);
        });
        // 处理page
        int totalCount = pageExternal.getTotalCount() + employeePageList.getTotalCount();
        page.setTotalCount(totalCount);
        page.setTotalPages(
                IndexEnum.ZERO.getValue() == totalCount ?
                        IndexEnum.ZERO.getValue() :
                        totalCount / page.getPageSize() + totalCount % page.getPageSize()
        );
        return returnVo;
    }


    /**
     * 获取所有人员信息（包含外部人员信息）
     *
     * @return
     */
    @Override
    public List<WsSheetPeopleInfoOutVo> getOAPeopleList(Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        return getOAAndExternalPageList(page, wsWorkSheetPeoppleInputVo);

//        PageDataReq<EmployeeListReq> reqPageDataReq = new PageDataReq();
//        reqPageDataReq.setPageNum(page.getPageNo());
//        reqPageDataReq.setPageSize(page.getPageSize());
//        EmployeeListReq employeeListReq = new EmployeeListReq();
//        employeeListReq.setEmployeeName(wsWorkSheetPeoppleInputVo.getEmployeeName());
//        employeeListReq.setOrgName(wsWorkSheetPeoppleInputVo.getDeptName());
//        employeeListReq.setOrgId(wsWorkSheetPeoppleInputVo.getDeptId());
//        reqPageDataReq.setData(employeeListReq);
//        List<EmployeeResp> employeePageList;
//        try {
//            DataSet<EmployeeResp> employeeRespDataSet = hrmsEmployeeFeignService.getEmployeePageList(reqPageDataReq);
//            employeePageList = employeeRespDataSet.getRows();
//            page.setTotalCount(employeeRespDataSet.getTotalCount());
//            // 所有外部人员id
//            List<String> externalPersonnelIdsList = employeePageList
//                    .stream()
//                    .filter(employeeResp -> defaultOrgId.equals(employeeResp.getOrgId()))
//                    .map(EmployeeResp::getEmployeeId).collect(Collectors.toList());
//            // 修正外部人员科室信息
//            if (!CollectionUtil.isEmpty(externalPersonnelIdsList)) {
//                List<WsExternalPersonnel> wsExternalPersonnels = wsExternalPersonnelService.selectOneByIds(externalPersonnelIdsList);
//                wsExternalPersonnels
//                        .forEach(externalPersonnel -> {
//                            employeePageList
//                                    .stream()
//                                    .filter(employeeResp -> defaultOrgId.equals(employeeResp.getOrgId()))
//                                    .forEach(employeeResp -> {
//                                        if (externalPersonnel.getPkExternalPersonnelId().equals(employeeResp.getEmployeeId())) {
//                                            employeeResp.setOrgId(externalPersonnel.getInstitutionalAffiliations());
//                                            employeeResp.setOrgName(externalPersonnel.getInstitutionalAffiliations());
//                                        }
//                                    });
//                        });
//            }
//        } catch (Exception e) {
//            log.error("查询OA人员信息失败：" + e.getMessage());
//            return null;
//        }
//        // 组装返回数据
//        List<WsSheetPeopleInfoOutVo> wsSheetPeopleInfoOutVoList = Lists.newArrayList();
//        employeePageList.forEach(employeeResp -> {
//            WsSheetPeopleInfoOutVo wsSheetPeopleInfoOutVo = new WsSheetPeopleInfoOutVo();
//            wsSheetPeopleInfoOutVo.setUserId(employeeResp.getEmployeeId());
//            wsSheetPeopleInfoOutVo.setName(employeeResp.getEmployeeName());
//            wsSheetPeopleInfoOutVo.setDeptId(employeeResp.getOrgId());
//            wsSheetPeopleInfoOutVo.setDeptName(employeeResp.getOrgName());
//            wsSheetPeopleInfoOutVo.setSex(StringUtil.isEmpty(employeeResp.getGender()) ? null : employeeResp.getGender());
//            wsSheetPeopleInfoOutVo.setSexText(StringUtil.isEmpty(employeeResp.getGenderText()) ? null : employeeResp.getGenderText());
//            wsSheetPeopleInfoOutVo.setUrl(StringUtil.isEmpty(employeeResp.getAvatar()) ? null : employeeResp.getAvatar());
//            wsSheetPeopleInfoOutVoList.add(wsSheetPeopleInfoOutVo);
//        });
//        return CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoList) ? Lists.newArrayList() : wsSheetPeopleInfoOutVoList;
    }


    /**
     * 故障类型处理人信息
     *
     * @param wsWorkSheetPeoppleInputVo
     * @return
     */
    @Override
    public List<WsSheetPeopleInfoOutVo> getFaultTypePeopleInfoList(Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        // 故障类型信息
        WsFaultType wsFaultType = wsFaultTypeService.selectOneById(wsWorkSheetPeoppleInputVo.getFaultTypeId());
        // 故障类型预设处理人
        List<WsSheetPeopleInfoOutVo> wsSheetPeopleInfoOutVoList = wsExternalPersonnelService.fillPeopleInfo(wsFaultType);
        // 手动模糊查询
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getEmployeeName())) {
            wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> wsSheetPeopleInfoOutVo.getName().contains(wsWorkSheetPeoppleInputVo.getEmployeeName().trim()))
                    .collect(Collectors.toList());
        }
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getDeptName())) {
            wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> wsSheetPeopleInfoOutVo.getDeptName().contains(wsWorkSheetPeoppleInputVo.getDeptName().trim()))
                    .collect(Collectors.toList());
        }
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getDeptId())) {
            // 所有外部机构名称
            List<String> institutional_affiliations = wsExternalPersonnelService.selectAllInstitutionalAffiliations(null).stream()
                    .map(map -> map.get("institutional_affiliations") + "")
                    .collect(Collectors.toList());
            wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> institutional_affiliations.contains(wsWorkSheetPeoppleInputVo.getDeptId().trim()))
                    .collect(Collectors.toList());
        }
        // 故障类型默认所有处理人
        if (StringUtil.isEmpty(wsFaultType.getFkUserId()) && CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoList)) {
            // 当前故障类型不为一级故障类型
            if (!StringUtil.isEmpty(wsFaultType.getParentId())) {
                // 递归查找上级故障类型（一直找到存在故障类型处理人或者查找至一级故障类型）
                String flag = recursiveAccessUserId(
                        // 所有的故障类型信息集合
                        wsFaultTypeService.selectFaultTypeAllList("1", null, null),
                        wsFaultType.getParentId());
                if (!StringUtil.isEmpty(flag)) {
                    wsSheetPeopleInfoOutVoList = wsExternalPersonnelService.fillPeopleInfo(wsFaultTypeService.selectOneById(flag));
                    page.setPageSize(IndexEnum.ZERO.getValue() == wsSheetPeopleInfoOutVoList.size() ? IndexEnum.ONE.getValue() : wsSheetPeopleInfoOutVoList.size());
                    page.setTotalPages(IndexEnum.ONE.getValue());
                    page.setTotalCount(wsSheetPeopleInfoOutVoList.size());
                } else {
                    wsSheetPeopleInfoOutVoList = getOAPeopleList(page, wsWorkSheetPeoppleInputVo);
                }


//                wsSheetPeopleInfoOutVoList = Optional.ofNullable(
//                        // 递归查找上级故障类型（一直找到存在故障类型处理人或者查找至一级故障类型）
//                        recursiveAccessUserId(
//                                // 所有的故障类型信息集合
//                                wsFaultTypeService.selectFaultTypeAllList("1", null),
//                                wsFaultType.getParentId()
//                        )
//                )
//                        // 填充上级故障类型的故障处理人员信息
//                        .map(pkfaultTypeId -> wsExternalPersonnelService.fillPeopleInfo(wsFaultTypeService.selectOneById(pkfaultTypeId)))
//                        // OA所有人员数据
//                        .orElse(getOAPeopleList(page, wsWorkSheetPeoppleInputVo));
            } else {
                wsSheetPeopleInfoOutVoList = getOAPeopleList(page, wsWorkSheetPeoppleInputVo);
            }
        } else {
            page.setPageSize(IndexEnum.ZERO.getValue() == wsSheetPeopleInfoOutVoList.size() ? IndexEnum.ONE.getValue() : wsSheetPeopleInfoOutVoList.size());
            page.setTotalPages(IndexEnum.ONE.getValue());
            page.setTotalCount(wsSheetPeopleInfoOutVoList.size());
        }
        return CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoList) ? Lists.newArrayList() : wsSheetPeopleInfoOutVoList;
    }



    /**
     * 故障类型处理人信息
     *
     * @param wsWorkSheetPeoppleInputVo
     * @return
     */
    public List<WsSheetPeopleInfoOutVo> getFaultTypePeopleInfoList(WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        // 故障类型信息
        WsFaultType wsFaultType = wsFaultTypeService.selectOneById(wsWorkSheetPeoppleInputVo.getFaultTypeId());
        // 故障类型预设处理人
        List<WsSheetPeopleInfoOutVo> wsSheetPeopleInfoOutVoList = wsExternalPersonnelService.fillPeopleInfo(wsFaultType);
        // 手动模糊查询
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getEmployeeName())) {
            wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> wsSheetPeopleInfoOutVo.getName().contains(wsWorkSheetPeoppleInputVo.getEmployeeName().trim()))
                    .collect(Collectors.toList());
        }
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getDeptName())) {
            wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> wsSheetPeopleInfoOutVo.getDeptName().contains(wsWorkSheetPeoppleInputVo.getDeptName().trim()))
                    .collect(Collectors.toList());
        }
        if (!StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getDeptId())) {
            // 所有外部机构名称
            List<String> institutional_affiliations = wsExternalPersonnelService.selectAllInstitutionalAffiliations(null).stream()
                    .map(map -> map.get("institutional_affiliations") + "")
                    .collect(Collectors.toList());
            List<WsSheetPeopleInfoOutVo> wsSheetPeopleInfoOutVoListTemp = wsSheetPeopleInfoOutVoList
                    .stream()
                    .filter(wsSheetPeopleInfoOutVo -> institutional_affiliations.contains(wsWorkSheetPeoppleInputVo.getDeptId().trim()))
                    .collect(Collectors.toList());
            if(!CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoListTemp)){
                wsSheetPeopleInfoOutVoList = wsSheetPeopleInfoOutVoListTemp;
            }

        }
        // 故障类型默认所有处理人
        if (StringUtil.isEmpty(wsFaultType.getFkUserId()) && CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoList)) {
            // 当前故障类型不为一级故障类型
            if (!StringUtil.isEmpty(wsFaultType.getParentId())) {
                // 递归查找上级故障类型（一直找到存在故障类型处理人或者查找至一级故障类型）
                String flag = recursiveAccessUserId(
                        // 所有的故障类型信息集合
                        wsFaultTypeService.selectFaultTypeAllList("1", null, null),
                        wsFaultType.getParentId());
                if (!StringUtil.isEmpty(flag)) {
                    wsSheetPeopleInfoOutVoList = wsExternalPersonnelService.fillPeopleInfo(wsFaultTypeService.selectOneById(flag));
                } else {
                    wsSheetPeopleInfoOutVoList = getOAPeopleList(wsWorkSheetPeoppleInputVo);
                }
            } else {
                wsSheetPeopleInfoOutVoList = getOAPeopleList( wsWorkSheetPeoppleInputVo);
            }
        }
        return CollectionUtil.isEmpty(wsSheetPeopleInfoOutVoList) ? Lists.newArrayList() : wsSheetPeopleInfoOutVoList;
    }


    /**
     * 递归依次查找 当前故障类型存在故障处理人上级的故障类型id
     *
     * @param faultTypeTreeOutVoList 所有故障类型数据集合
     * @param pkFaultTypeId          当前故障类型id
     * @return
     */
    private String recursiveAccessUserId(List<FaultTypeTreeOutVo> faultTypeTreeOutVoList, String pkFaultTypeId) {
        // 当前故障类型上一级的故障类型
        List<FaultTypeTreeOutVo> collect = faultTypeTreeOutVoList
                .stream()
                .filter(temp -> temp.getId().equals(pkFaultTypeId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        } else {
            if (StringUtil.isEmpty(collect.get(0).getPeopleIds())) {
                return recursiveAccessUserId(faultTypeTreeOutVoList, collect.get(0).getPid());
            } else {
                return collect.get(0).getId();
            }
        }
    }

    /**
     * 处理人员信息
     *
     * @param page
     * @param wsWorkSheetPeoppleInputVo 查询参数
     * @return
     */
    @Override
    public List<WsSheetPeopleInfoOutVo> getPeopleListInfo(Page page, WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        List<WsSheetPeopleInfoOutVo> result;
        if (StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getFaultTypeId())) {
            result = getOAPeopleList(page, wsWorkSheetPeoppleInputVo);
        } else {
            result = getFaultTypePeopleInfoList(page, wsWorkSheetPeoppleInputVo);
        }
        return result;
    }

    @Override
    public List<WsSheetPeopleInfoOutVo> getPeopleListInfo(WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        List<WsSheetPeopleInfoOutVo> result;
        if (StringUtil.isEmpty(wsWorkSheetPeoppleInputVo.getFaultTypeId())) {
            result = getOAPeopleList(wsWorkSheetPeoppleInputVo);
        } else {
            result = getFaultTypePeopleInfoList(wsWorkSheetPeoppleInputVo);
        }
        return wsSheetService.fillPeopleProcessCount(result);
    }

    @Override
    public List<WsSheetPeopleInfoOutVo> getOAPeopleList(WsWorkSheetPeoppleInputVo wsWorkSheetPeoppleInputVo) {
        // 外部人员信息
        List<ExternalPersonnelPageListOutVo> externalPersonnelPageListOutVos = wsExternalPersonnelService.selectAllList(
                StringUtils.isEmpty(wsWorkSheetPeoppleInputVo.getEmployeeName()) ? wsWorkSheetPeoppleInputVo.getDeptName() : wsWorkSheetPeoppleInputVo.getEmployeeName(),
                StringUtils.isEmpty(wsWorkSheetPeoppleInputVo.getDeptId()) ? null : wsWorkSheetPeoppleInputVo.getDeptId().replace(CuttingOperatorEnum.COMMA.getValue(), "")
        );
        // OA人员分页信息
        PageDataReq<EmployeeListReq> reqPageDataReq = new PageDataReq();
        reqPageDataReq.setPageNum(1);
        reqPageDataReq.setPageSize(9999);
        EmployeeListReq employeeListReq = new EmployeeListReq();
        employeeListReq.setEmployeeName(wsWorkSheetPeoppleInputVo.getEmployeeName());
        employeeListReq.setOrgName(wsWorkSheetPeoppleInputVo.getDeptName());
        employeeListReq.setOrgId(wsWorkSheetPeoppleInputVo.getDeptId());
        reqPageDataReq.setData(employeeListReq);

        // OA人员信息
        DataSet<EmployeeResp> employeePageList = new DataSet<>();
        try {
            employeePageList = hrmsEmployeeFeignService.getEmployeePageList(reqPageDataReq);
        } catch (Exception e) {
            log.error("查询OA人员信息失败：" + e.getMessage());
            e.printStackTrace();
        }
        // 组装返回数据
        List<WsSheetPeopleInfoOutVo> returnVo = Lists.newArrayList();

        employeePageList.getRows().forEach(employeeResp -> {
            WsSheetPeopleInfoOutVo wsSheetPeopleInfoOutVo = new WsSheetPeopleInfoOutVo();
            wsSheetPeopleInfoOutVo.setUserId(employeeResp.getEmployeeId());
            wsSheetPeopleInfoOutVo.setName(employeeResp.getEmployeeName());
            wsSheetPeopleInfoOutVo.setDeptId(employeeResp.getOrgId());
            wsSheetPeopleInfoOutVo.setDeptName(employeeResp.getOrgName());
            wsSheetPeopleInfoOutVo.setSex(StringUtil.isEmpty(employeeResp.getGender()) ? null : employeeResp.getGender());
            wsSheetPeopleInfoOutVo.setSexText(StringUtil.isEmpty(employeeResp.getGenderText()) ? null : employeeResp.getGenderText());
            wsSheetPeopleInfoOutVo.setUrl(StringUtil.isEmpty(employeeResp.getAvatar()) ? null : employeeResp.getAvatar());
            wsSheetPeopleInfoOutVo.setUserCode(employeeResp.getEmployeeNo());
            returnVo.add(wsSheetPeopleInfoOutVo);
        });

        externalPersonnelPageListOutVos.forEach(externalPersonnel -> {
            WsSheetPeopleInfoOutVo wsSheetPeopleInfoOutVo = new WsSheetPeopleInfoOutVo();
            wsSheetPeopleInfoOutVo.setUserId(externalPersonnel.getPkExternalPersonnelId());
            wsSheetPeopleInfoOutVo.setName(externalPersonnel.getUserName());
            wsSheetPeopleInfoOutVo.setDeptId(externalPersonnel.getInstitutionalAffiliations());
            wsSheetPeopleInfoOutVo.setDeptName(externalPersonnel.getInstitutionalAffiliations());
            wsSheetPeopleInfoOutVo.setSex(null);
            wsSheetPeopleInfoOutVo.setSexText(null);
            wsSheetPeopleInfoOutVo.setUrl(null);
            returnVo.add(wsSheetPeopleInfoOutVo);
        });

        return returnVo;
    }

}
