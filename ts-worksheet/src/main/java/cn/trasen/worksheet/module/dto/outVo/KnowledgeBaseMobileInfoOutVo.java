package cn.trasen.worksheet.module.dto.outVo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class KnowledgeBaseMobileInfoOutVo {


    @ApiModelProperty(value = "知识点ID")
    private String pkKnowledgeBaseId;

    @ApiModelProperty(value = "知识点标题")
    private String knowledgeTitle;

    @ApiModelProperty(value = "知识点类型名称")
    private String fkKnowledgeTypeName;

    @ApiModelProperty(value = "贡献人科室名称")
    private String fkUserDeptName;

    @ApiModelProperty(value = "贡献人名称")
    private String fkUserName;

    @ApiModelProperty(value = "推荐工时")
    private int recommendedWorkHours;

    @ApiModelProperty(value = "有用次数（即赞）")
    private int usefulNumbers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "贡献时间")
    private Date contributionTime;

    @ApiModelProperty(value = "贡献人id")
    private String fkUserId;

    @ApiModelProperty(value = "贡献人科室id")
    private String fkUserDeptId;

    @ApiModelProperty(value = "知识点内容")
    private String knowledgeContent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

}