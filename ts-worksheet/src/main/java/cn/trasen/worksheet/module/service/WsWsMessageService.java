package cn.trasen.worksheet.module.service;

import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.outVo.WsWsMessageListOutVo;
import cn.trasen.worksheet.module.entity.WsWsMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2021/8/27 17:25
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsWsMessageService {

    int insertMessage(WsWsMessage WsWsMessage);

    /**
     * 置为已读
     * @param pkWsMessageId 消息id
     * @return
     */
    int updateMessage(String pkWsMessageId);

    int updateMessageAllByFkUserId();

    /**
     * 分页列表
     * @param page
     * @param isRead
     * @return
     */
    List<WsWsMessageListOutVo> selectMessagePageList(Page page,int isRead);


    /**
     * 分页列表
     * @param userId
     * @param isRead 0未读1已读
     * @return
     */
    List<WsWsMessageListOutVo> selectMessageAllList(String userId,int isRead);


    WsWsMessage selectOneById(String pkWsMessageId);

    /**
     * 查看详情
     * @param pkWsMessageId 消息id
     * @param isRead 是否已读标记 0否1是
     * @return
     */
    WsWsMessageListOutVo selectOneWsWsMessageListOutVoById(String pkWsMessageId, int isRead);

    /**
     * 查询待派单，待接单消息推送时间
     * @param workNumber 工单好
     * @param taskName 节点信息
     * @return
     */
    List<Map<String, Object>> selectOneDpdDjdByWorkNumber(List<String> workNumber, String taskName);

	List<EmployeeResp> getSchedule(String deptId);
}
