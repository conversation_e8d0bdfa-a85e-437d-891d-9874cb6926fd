package cn.trasen.worksheet.module.dto.outVo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class WsWorkSheetHomeListOutVo {

    @ApiModelProperty(value = "工单状态")
    private String workStatus;

    @ApiModelProperty(value = "工单状态CN")
    private String workStatusName;

    @ApiModelProperty(value = "工单编号")
    private String workNumber;

    @ApiModelProperty(value = "超期天数")
    private int cqDay;

    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "期待完成时间")
    private Date requiredCompletionTime;

    @ApiModelProperty(value = "故障描述")
    private String faultDeion;

    @ApiModelProperty(value = "报修人科室名称")
    private String repairManDeptName;

    @ApiModelProperty(value = "报修人")
    private String repairManName;

    @ApiModelProperty(value = "报修日期")
    private String createTime;

    @ApiModelProperty(value = "工单接单时间、最新更新时间")
    private Date taskCreateTime;

    @ApiModelProperty(value = "催办时间、评价时间")
    private Date operatingTime;

    @ApiModelProperty(value = "完成时间")
    private Date actualCompletionTime;

    @ApiModelProperty(value = "暂停原因、终止原因、打回原因")
    private String takeRemark;

    @ApiModelProperty(value = "处理人id")
    private String fkUserId;

    @ApiModelProperty(value = "处理人头像")
    private String fkUserUrl;

    @ApiModelProperty(value = "处理人名称")
    private String fkUserName;

    @ApiModelProperty(value = "处理人科室名称")
    private String fkUserDeptName;

    @ApiModelProperty(value = "催办次数、打回次数")
    private int counts;

    @ApiModelProperty(value = "综合评分")
    private float comprehensiveScore;

    @ApiModelProperty(value = "身为协助人，是否处理过，0为否1为是")
    private int theAssistFlag;
}
