package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_external_personnel")
@Setter
@Getter
public class WsExternalPersonnel extends WsBase implements Supplier {
    /**
     * 外部人员id
     */
    @Column(name = "pk_external_personnel_id")
    @ApiModelProperty(value = "外部人员id")
    private String pkExternalPersonnelId;

    /**
     * 所属机构
     */
    @Column(name = "institutional_affiliations")
    @ApiModelProperty(value = "所属机构")
    private String institutionalAffiliations;

    /**
     * 人员姓名
     */
    @Column(name = "user_name")
    @ApiModelProperty(value = "人员姓名")
    private String userName;


    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String phone;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;

    /**
     * 职责说明
     */
    @ApiModelProperty(value = "职责说明")
    private String jobDeion;

    /**
     * 状态（0停用1启用）
     */
    @ApiModelProperty(value = "状态（0停用1启用）")
    private int status;

    /**
     * 所属管辖科室id
     */
    @Column(name = "belongs_dept_id")
    @ApiModelProperty(value = "所属管辖科室id")
    private String belongsDeptId;

    /**
     * 所属管辖科室名称
     */
    @Column(name = "belongs_dept_name")
    @ApiModelProperty(value = "所属管辖科室名称")
    private String belongsDeptName;

    /**
     * 默认密码
     */
    @Column(name = "user_password")
    @ApiModelProperty(value = "默认密码")
    private String userPassword;

    /**
     * 默认外部机构id
     */
    @ApiModelProperty(hidden = true)
    private String defaultOrgId;

    /**
     * 默认外部角色编码
     */
    @ApiModelProperty(hidden = true)
    private String defaultRoleCode;



    @Override
    public WsExternalPersonnel get() {
        return this;
    }
}