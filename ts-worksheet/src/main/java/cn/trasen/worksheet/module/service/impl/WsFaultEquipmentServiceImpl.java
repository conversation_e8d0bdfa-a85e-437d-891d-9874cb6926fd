package cn.trasen.worksheet.module.service.impl;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.zxing.WriterException;

import cn.trasen.BootComm.utils.QrCode;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.QrCodeUtils;
import cn.trasen.worksheet.common.util.entity.QrcodeFont;
import cn.trasen.worksheet.module.dto.inputVo.FaultEquipmentInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentQRCodeInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoByFaultEquipmentOutVo;
import cn.trasen.worksheet.module.entity.WsFaultEquipment;
import cn.trasen.worksheet.module.mapper.WsFaultEquipmentMapper;
import cn.trasen.worksheet.module.service.WsFaultEquipmentService;
import cn.trasen.worksheet.module.service.WsSheetService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/7/2 11:15
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
@Slf4j
public class WsFaultEquipmentServiceImpl implements WsFaultEquipmentService {

    @Autowired
    private WsFaultEquipmentMapper wsFaultEquipmentMapper;

    @Autowired
    private WsSheetService wsSheetService;

    @Value("${oaUrl.faultEquipmentQrCodeUrl}")
    private String qrCode;

    /**
     * 保存信息
     *
     * @param faultEquipmentInputVo
     * @return
     */
    @Transactional
    @Override
    public String saveEquipment(FaultEquipmentInputVo faultEquipmentInputVo) {

        WsFaultEquipment wsFaultEquipment = new WsFaultEquipment();
        WsFaultEquipment faultEquipment = wsFaultEquipmentMapper.selectEquipmenByEquipmentNumber(faultEquipmentInputVo.getEquipmentNumber());
        Integer change;
        if (StringUtil.isEmpty(faultEquipmentInputVo.getPkFaultEquipmentId())) {
            if(null!=faultEquipment){
                throw new BusinessException("设备编码已存在");
            }
            wsFaultEquipment.setPkFaultEquipmentId(faultEquipmentInputVo.getEquipmentNumber());
            MyBeanUtils.copyBeanNotNull2Bean(faultEquipmentInputVo, wsFaultEquipment);
            change = wsFaultEquipmentMapper.insertEquipment(wsFaultEquipment);
        } else {
            if(null!=faultEquipment && !faultEquipmentInputVo.getPkFaultEquipmentId().equals(faultEquipment.getPkFaultEquipmentId())){
                throw new BusinessException("设备编码已存在");
            }
            wsFaultEquipment = wsFaultEquipmentMapper.selectEquipmenById(faultEquipmentInputVo.getPkFaultEquipmentId());
            MyBeanUtils.copyBeanNotNull2Bean(faultEquipmentInputVo, wsFaultEquipment);
            change = wsFaultEquipmentMapper.updateEquipment(wsFaultEquipment);
        }
        Optional.ofNullable(change)
                .orElseThrow(() -> new BusinessException(CommonlyConstants.OperationReturnValue.FAIL));
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }

    /**
     * 设备信息详情
     *
     * @param id
     * @return
     */
    @Override
    public WsFaultEquipment selectOne(String id) {
        return Optional.ofNullable(wsFaultEquipmentMapper.selectEquipmenById(id))
                .map(WsFaultEquipment::get)
                .orElseThrow(() -> new BusinessException("未查询到设备信息"));
    }


    @Override
    public FaultEquipmentOutVo selectOneFaultEquipmentOutVo(String id) {
        WsFaultEquipment wsFaultEquipment = selectOne(id);
        FaultEquipmentOutVo faultEquipmentOutVo = new FaultEquipmentOutVo();
        MyBeanUtils.copyBeanNotNull2Bean(wsFaultEquipment, faultEquipmentOutVo);
        return faultEquipmentOutVo;
    }

    @Transactional
    @Override
    public String deleteEquipmenById(String id) {
        if (CommonlyConstants.YesOrNo.NO == wsFaultEquipmentMapper.deleteEquipmenById(id)) {
            return CommonlyConstants.OperationReturnValue.FAIL;
        }
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }

    /**
     * 查询设备信息分页列表
     *
     * @param page
     * @param faultEquipmentInputVo
     * @return
     */
    @Override
    public List<FaultEquipmentOutVo> selectPageList(Page page, FaultEquipmentInputVo faultEquipmentInputVo) {
        List<FaultEquipmentOutVo> faultEquipmentOutVos = wsFaultEquipmentMapper.selectPageList(page, faultEquipmentInputVo);
        faultEquipmentOutVos.forEach(temp -> {
            try {
//                List<QrcodeFont> qrcodeFontList = Lists.newArrayList();
//                QrcodeFont font1 = new QrcodeFont();
//                font1.setX(125);
//                font1.setY(30);
//                font1.setContent(temp.getEquipmentName());
//                qrcodeFontList.add(font1);
//                QrcodeFont font2 = new QrcodeFont();
//                font2.setX(125);
//                font2.setY(100);
//                font2.setContent(DateUtils.dateToStringFormat("yyyy-MM-dd", temp.getCreateTime()));
//                qrcodeFontList.add(font2);
//                temp.setQrCode(
//                        QrCodeUtils.pressText(
//                                QrCode.createQrCode(qrCode + "?pkFaultEquipmentId=" + temp.getPkFaultEquipmentId(), 80, 0, "png"),
//                                240,
//                                140,
//                                qrcodeFontList
//                        )
//                );

                // 填充二维码
               temp.setQrCode(Base64.getEncoder().encodeToString(QrCode.createQrCode(qrCode + "?pkFaultEquipmentId=" + temp.getPkFaultEquipmentId(), 50, 5, "png")));
            } catch (IOException | WriterException e) {
                log.error("二维码生成失败" + e.getMessage());
                throw new BusinessException("二维码生成失败");
            }
        });
        return faultEquipmentOutVos;
    }

    /**
     * 查看二维码信息
     *
     * @param pkFaultEquipmentId
     */
    @Override
    public FaultEquipmentQRCodeInfoOutVo checkTheQRCodeInfo(String pkFaultEquipmentId) {
        FaultEquipmentOutVo faultEquipmentOutVo = selectOneFaultEquipmentOutVo(pkFaultEquipmentId);
        List<WsWsSheetInfoByFaultEquipmentOutVo> wsWsSheetInfoByFaultEquipmentOutVos = wsSheetService.selectListWsSheetByfaultEquipmentId(pkFaultEquipmentId);
        wsWsSheetInfoByFaultEquipmentOutVos.forEach(temp -> {
            temp.setWorkStatusValue(WorkSheetStatusEnum.getByValue(temp.workStatus).getName());
        });
        return new FaultEquipmentQRCodeInfoOutVo(faultEquipmentOutVo, wsWsSheetInfoByFaultEquipmentOutVos);
    }

    /**
     * 查询设备信息列表,不分页
     *
     * @param faultEquipmentInputVo
     * @return
     */
    @Override
    public List<FaultEquipmentOutVo> selectAllList(FaultEquipmentInputVo faultEquipmentInputVo) {
        return wsFaultEquipmentMapper.selectAllList(faultEquipmentInputVo);
    }

}
