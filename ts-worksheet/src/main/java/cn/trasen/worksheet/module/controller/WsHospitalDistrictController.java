package cn.trasen.worksheet.module.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsHospitalDistrictSaveInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsHospitalDistrictListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.service.WsHospitalDistrictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date: 2021/7/22 10:51
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Api(tags = "工单院区设置")
@RestController
public class WsHospitalDistrictController {

    @Autowired
    private WsHospitalDistrictService wsHospitalDistrictService;

    @ControllerLog(description = "工单院区配置信息")
    @ApiOperation(value = "工单院区配置信息", notes = "工单院区配置信息")
    @GetMapping("/hospitalDistrictList")
    public PlatformResult<List<WsHospitalDistrictListOutVo>> getHospitalDistrictList() {
        return PlatformResult.success(wsHospitalDistrictService.getHospitalDistrictList());
    }

    @ControllerLog(description = "保存、修改工单院区配置信息")
    @ApiOperation(value = "保存、修改工单院区配置信息", notes = "保存、修改工单院区配置信息")
    @PostMapping("/hospitalDistrict")
    public PlatformResult<Integer> hospitalDistrict(@RequestBody WsHospitalDistrictSaveInputVo hospitalDistrictSaveInputVo) {
        return PlatformResult.success(wsHospitalDistrictService.hospitalDistrict(hospitalDistrictSaveInputVo));
    }


    @ControllerLog(description = "启用禁用(0禁用，1启用)")
    @ApiOperation(value = "启用禁用(0禁用，1启用)", notes = "启用禁用(0禁用，1启用)")
    @PostMapping("/hospitalDistrict/{pkHospitalDistrictId}/{hospitalDistrictStatus}")
    public PlatformResult<Integer> hospitalDistrict(@PathVariable("pkHospitalDistrictId") String pkHospitalDistrictId,
                                                    @PathVariable("hospitalDistrictStatus") String hospitalDistrictStatus) {
        return PlatformResult.success(wsHospitalDistrictService.enableOrDisable(pkHospitalDistrictId, hospitalDistrictStatus));
    }

    @ControllerLog(description = "工单所有院区（仅启用信息）")
    @ApiOperation(value = "工单所有院区（仅启用信息）", notes = "工单所有院区（仅启用信息）")
    @GetMapping("/hospitalDistrictEnableList")
    public PlatformResult<List<WsWorkFlowListOutVo>> getHospitalDistrictEnableList() {
        return PlatformResult.success(wsHospitalDistrictService.getHospitalDistrictEnableList("1"));
    }
}
