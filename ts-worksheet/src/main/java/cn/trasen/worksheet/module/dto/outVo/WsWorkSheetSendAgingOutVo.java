package cn.trasen.worksheet.module.dto.outVo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class WsWorkSheetSendAgingOutVo {

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "姓名")
    private String employeeName;

    @ApiModelProperty(value = "派单数量")
    private Integer dispatchNumber;

    @ApiModelProperty(value = "派单最短时长")
    private Integer dispatchShortestTime;

    @ApiModelProperty(value = "派单最短时长(日时分)")
    private String dispatchShortestTimeValue;

    @ApiModelProperty(value = "派单最长时长")
    private Integer dispatchLongestTime;

    @ApiModelProperty(value = "派单最长时长(日时分)")
    private String dispatchLongestTimeValue;

    @ApiModelProperty(value = "派单平均时长")
    private Integer dispatchAvgtTime;

    @ApiModelProperty(value = "派单平均时长(日时分)")
    private String dispatchAvgTimeValue;
    
    @ApiModelProperty(value = "整体平均时长")
    private Integer dispatchTime;
    
    @ApiModelProperty(value = "整体平均时长(日时分)")
    private String dispatchTimeValue;

    @ApiModelProperty(value = "与整体平均时长差异")
    private Integer dispatchTimeDifference;

    @ApiModelProperty(value = "与整体平均时长差异(日时分)")
    private String dispatchTimeDifferenceValue;

    @ApiModelProperty(value = "与整体平均时长差异,false为快，true为慢")
    private Boolean flag;

}
