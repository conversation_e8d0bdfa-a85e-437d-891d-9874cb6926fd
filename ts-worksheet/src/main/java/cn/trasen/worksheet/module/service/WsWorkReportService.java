package cn.trasen.worksheet.module.service;

import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportOutVo;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsWrokReport;

import java.util.List;

/**
 * 工单日报
 * <AUTHOR>
 * @date: 2021/7/3 17:47
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsWorkReportService {

    /**
     * 保存
     * @param wsWorkReport
     * @return
     */
    int insert(WsWrokReport wsWorkReport);

    /**
     * 根据主键查询
     * @param pkWorkReportId 主键
     * @return
     */
    WsWrokReport selectOneById(String pkWorkReportId);

    /**
     * 工单日报详情
     * @param pkWorkReportId 主键
     * @return
     */
    WsWorkReportOutVo workReportInfoById(String pkWorkReportId);

}
