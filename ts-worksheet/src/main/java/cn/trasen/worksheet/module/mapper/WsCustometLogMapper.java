package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.WsCustometLogListInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogCallRecordsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportCallsListOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.entity.WsCustometService;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsCustometLogMapper extends Mapper<WsCustometLog> {

    int insertWsCustometLog(WsCustometLog wsCustometLog);

    int updateWsCustometLog(WsCustometLog wsCustometLog);

    int updateBatchWsCustometLog(List<WsCustometLog> wsCustometLog);

    /**
     * 查询通话记录未建单数量、未读数量
     * @param map
     * @return
     */
    Map<String,Object> workOrderNotProcessedCounts(Map<String,Object> map);

    WsCustometLog selectOneById(@Param(value = "pkCustometLogId") String pkCustometLogId);

    WsCustometLog selectOneByWorkNumber(@Param(value = "workNumber") String workNumber);

    List<WsCustometLog> selectAllById(@Param("list") List<String> pkCustometLogIds);


    /**
     * 服务台今日统计
     *
     * @return
     */
    Map<String, Object> serviceDeskStatisticsToday(@Param("list") List<String> fkDeptId,
                                                   @Param("beginTime") String beginTime,
                                                   @Param("endTime") String endTime);

    /**
     * 查询除当前通话中，该坐席人员所有未接来电
     *
     * @return
     */
    List<WsCustometLogOutVo> selectAllList(WsCustometLog wsCustometLog);

    List<WsCustometLogOutVo> selectListByCustometLog(WsCustometLog wsCustometLog);

    /**
     * 服务台通话记录
     *
     * @return
     */
    List<WsCustometLogCallRecordsOutVo> selectCallRecordsPageList(Page page, WsCustometLogListInputVo wsCustometLogListInputVo);


    /**
     * 当日报修次数
     * @param phone 报修人电话
     * @return
     */
    int numberOfDailyRepairReports(@Param("phone") String phone);

    /**
     * 将通话记录修改为已读
     * @param fkDeptId
     * @return
     */
    int updateIsReadByDeptId(@Param("list") List<String> fkDeptId);

    /**
     * 查看当天未接信息列表
     * @param fkDeptId
     * @return
     */
    List<WsWorkReportCallsListOutVo> selectDayByDeptId(@Param("fkDeptId") String fkDeptId);


    /**
     * 获取需填充工单号的录音文件信息
     * @return
     */
    List<Map<String,Object>> fillFileWorkNumberInfo();

	void updateCalltype();
}