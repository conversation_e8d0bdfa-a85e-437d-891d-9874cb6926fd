package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/1 17:38
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsWsSheetAssistInputVo {

    @NotNull(message = "工单编号不能为空")
    @ApiModelProperty(value = "工单编号" )
    private String workNumber;

    @NotNull(message = "协助人Id不能为空")
    @ApiModelProperty(value = "协助人Id（英文逗号拼接）" )
    private String fkUserIds;

    @NotNull(message = "协助备注不能为空")
    @ApiModelProperty(value = "协助备注（英文逗号拼接）" )
    private String remarks;


}
