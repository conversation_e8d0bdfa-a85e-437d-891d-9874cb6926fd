package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.constant.ConstantYml;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.PermissionsEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.ExportUtil;
import cn.trasen.worksheet.common.util.FileUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsCustometLogListInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogCallRecordsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogWorkSheetOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkReportCallsListOutVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.mapper.WsCustometLogMapper;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/7/26 17:53
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
@Service
public class WsCustometLogServiceImpl implements WsCustometLogService {

    @Autowired
    private WsCustometLogMapper wsCustometLogMapper;


    @Override
    public void insertWsCustometLog(WsCustometLog wsCustometLog) {
        if (IndexEnum.ZERO.getValue() == wsCustometLogMapper.insertWsCustometLog(wsCustometLog)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
    }

    @Override
    public int updateWsCustometLog(WsCustometLog wsCustometLog) {
        if (IndexEnum.ZERO.getValue() == wsCustometLogMapper.updateWsCustometLog(wsCustometLog)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Override
    public int updateBatchWsCustometLog(List<WsCustometLog> wsCustometLog) {
        return wsCustometLogMapper.updateBatchWsCustometLog(wsCustometLog);
    }

    /**
     * 查询通话记录未建单数量、未读数量
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> workOrderNotProcessedCounts(Map<String, Object> map) {
        return wsCustometLogMapper.workOrderNotProcessedCounts(map);
    }

    /**
     * 服务台今日统计
     *
     * @return
     */
    @Override
    public Map<String, Object> serviceDeskStatisticsToday() {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 人员数据权限过滤
        String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "");
        List<String> fkDeptId = Lists.newArrayList();
        // 跨科室
        if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
            fkDeptId = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
            // 本科室
        } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
            fkDeptId = Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue()));
        } else {
            //本人
            // 服务台数据特殊处理（服务台数据权限最小单位为科室）
            fkDeptId = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
        }
        return wsCustometLogMapper.serviceDeskStatisticsToday(fkDeptId, DateUtils.getDayStart(), DateUtils.getDayEnd());
    }

    /**
     * 指定服务台今日统计
     *
     * @param fkDeptId
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public Map<String, Object> serviceDeskStatisticsToday(String fkDeptId, String beginTime, String endTime) {

        return wsCustometLogMapper.serviceDeskStatisticsToday(Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue())), DateUtils.getDayStart(), DateUtils.getDayEnd());
    }

    @Override
    public WsCustometLogOutVo selectOneWsCustometLogOutVoById(String pkCustometLogId) {
        WsCustometLogOutVo wsCustometLogOutVo = new WsCustometLogOutVo();
        WsCustometLog wsCustometLog = wsCustometLogMapper.selectOneById(pkCustometLogId);
        MyBeanUtils.copyBeanNotNull2Bean(wsCustometLog, wsCustometLogOutVo);

        return wsCustometLogOutVo;
    }

    @Override
    public WsCustometLog selectOneById(String pkCustometLogId) {
        return wsCustometLogMapper.selectOneById(pkCustometLogId);
    }

    @Override
    public WsCustometLog selectOneByWorkNumber(String workNumber) {
        return Optional.ofNullable(wsCustometLogMapper.selectOneByWorkNumber(workNumber))
                .map(WsCustometLog::get)
                .orElseThrow(() -> new BusinessException("未查询到通话记录"));
    }

    @Override
    public List<WsCustometLog> selectAllById(List<String> pkCustometLogIds) {
        return wsCustometLogMapper.selectAllById(pkCustometLogIds);
    }


    /**
     * 查询除当前通话中，该坐席人员所有未接来电
     *
     * @return
     */
    @Override
    public List<WsCustometLogOutVo> selectAllList(WsCustometLog wsCustometLog) {
        return wsCustometLogMapper.selectAllList(wsCustometLog);
    }

    @Override
    public List<WsCustometLogOutVo> selectListByCustometLog(WsCustometLog wsCustometLog) {
        return wsCustometLogMapper.selectListByCustometLog(wsCustometLog);
    }

    @Override
    public List<WsCustometLogCallRecordsOutVo> selectCallRecordsPageList(Page page, WsCustometLogListInputVo wsCustometLogListInputVo) {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 人员数据权限过滤
        String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "");
        String fkDeptId = "";
        // 跨科室
        if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
            fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
            // 本科室
        } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
            fkDeptId = currentUserInfo.getDeptId();
        } else {
            //本人
            // 服务台数据特殊处理（服务台数据权限最小单位为科室）
            fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
        }
        wsCustometLogListInputVo.setFkDeptId(fkDeptId);
        wsCustometLogListInputVo.setList(StringUtils.isEmpty(fkDeptId) ? null : Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue())));
        List<WsCustometLogCallRecordsOutVo> wsCustometLogCallRecordsOutVos = wsCustometLogMapper.selectCallRecordsPageList(page, wsCustometLogListInputVo);
        // 填充中文展示
        wsCustometLogCallRecordsOutVos.forEach(temp -> {
            temp.setFileCount(StringUtils.isEmpty(temp.getFileUrl()) ? 0 : temp.getFileUrl().split(CuttingOperatorEnum.COMMA.getValue()).length);
            WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getRemark());
            if (null != byValue) {
                temp.setRemark(byValue.getName() + "");
            }
            if (!StringUtils.isEmpty(temp.getFileUrl())) {
                temp.setDuration(FileUtils.mp3ToDurationString(ConstantYml.getInstance().getFileServerIpHost(), temp.getFileUrl(), UserInfoHolder.getToken()));
            }
        });
        // 通话记录修改为已读
        if (StringUtils.isEmpty(wsCustometLogListInputVo.getType())) {
            updateIsReadByDeptId(fkDeptId);
        }
        return wsCustometLogCallRecordsOutVos;
    }

    /**
     * 查询个人通话记录
     *
     * @param pkCustometLogId 当前通话中坐席通话记录id
     * @return
     */
    @Override
    public WsCustometLogWorkSheetOutVo selectCreateWorkSheetCustometService(String pkCustometLogId) {
        WsCustometLogWorkSheetOutVo wsCustometLogWorkSheetOutVo = new WsCustometLogWorkSheetOutVo();
        // 查询当前通话记录
        if (!StringUtils.isEmpty(pkCustometLogId)) {
            wsCustometLogWorkSheetOutVo.setWsCustometLogOutVo(selectOneWsCustometLogOutVoById(pkCustometLogId));
        }
        wsCustometLogWorkSheetOutVo.setWsCustometLogOutVoList(wsCustometLogMapper.selectAllList
                (new WsCustometLog(pkCustometLogId, IndexEnum.ONE.getValue(), IndexEnum.ONE.getValue(), UserInfoHolder.getCurrentUserId()))
        );
        return wsCustometLogWorkSheetOutVo;
    }

    /**
     * 修改通话记录状态
     *
     * @param pkCustometLogId
     * @param callType
     * @param callWorkStatus
     * @return
     */
    @Transactional
    @Override
    public String modifyingCallRecords(String pkCustometLogId, int callType, int callWorkStatus) {
        updateWsCustometLog(new WsCustometLog(pkCustometLogId, callType, callWorkStatus, new Date(), UserInfoHolder.getCurrentUserId()));
        return CommonlyConstants.OperationReturnValue.SUCCESS;
    }

    /**
     * 当日报修次数
     *
     * @param phone 报修人电话
     * @return
     */
    @Override
    public int numberOfDailyRepairReports(String phone) {
        return wsCustometLogMapper.numberOfDailyRepairReports(phone);
    }

    /**
     * 将通话记录修改为已读
     *
     * @param fkDeptId
     * @return
     */
    @Override
    public int updateIsReadByDeptId(String fkDeptId) {
        return wsCustometLogMapper.updateIsReadByDeptId(Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue())));
    }


    /**
     * 查看当天未接信息列表
     *
     * @param fkDeptId
     * @return
     */
    @Override
    public List<WsWorkReportCallsListOutVo> selectDayByDeptId(String fkDeptId) {
        return wsCustometLogMapper.selectDayByDeptId(fkDeptId);
    }

    /**
     * 获取需填充工单号的录音文件信息
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> fillFileWorkNumberInfo() {
        return wsCustometLogMapper.fillFileWorkNumberInfo();
    }

    /**
     * 导出excel
     *
     * @param page
     * @param wsCustometLogListInputVo
     */
    @Override
    public void exportExcel(Page page, WsCustometLogListInputVo wsCustometLogListInputVo, HttpServletResponse response, HttpServletRequest request) {
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 人员数据权限过滤
        String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "");
        String fkDeptId = "";
        // 跨科室
        if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
            fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
            // 本科室
        } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
            fkDeptId = currentUserInfo.getDeptId();
        } else {
            //本人
            // 服务台数据特殊处理（服务台数据权限最小单位为科室）
            fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
        }
        wsCustometLogListInputVo.setFkDeptId(fkDeptId);
        wsCustometLogListInputVo.setList(StringUtils.isEmpty(fkDeptId) ? null : Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue())));
        List<WsCustometLogCallRecordsOutVo> wsCustometLogCallRecordsOutVos = wsCustometLogMapper.selectCallRecordsPageList(page, wsCustometLogListInputVo);
        // 文件名、表头
        String excelName = "";
        // 列头
        List<String> headList = com.google.common.collect.Lists.newArrayList();
        List<Map<String, Object>> dataList;
        if (StringUtils.isEmpty(wsCustometLogListInputVo.getType())) {
            excelName = "通话记录";
            headList.add("来电时间");
            headList.add("报修科室");
            headList.add("报修人");
            headList.add("报修电话");
            headList.add("类型");
            headList.add("故障描述");
            headList.add("备注说明");
            headList.add("工单号");
            dataList = wsCustometLogCallRecordsOutVos.stream().map(custometLogCallRecordsOutVo -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("来电时间", custometLogCallRecordsOutVo.getVisitTime());
                map.put("报修科室", custometLogCallRecordsOutVo.getVisitUserDeptName());
                map.put("报修人", custometLogCallRecordsOutVo.getVisitUserName());
                map.put("报修电话", custometLogCallRecordsOutVo.getVisitPhone());
                map.put("类型", custometLogCallRecordsOutVo.getCallTypeName());
                map.put("故障描述", custometLogCallRecordsOutVo.getFaultDeion());
                WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(custometLogCallRecordsOutVo.getRemark());
                if (null != byValue) {
                    custometLogCallRecordsOutVo.setRemark(byValue.getName() + "");
                }
                map.put("备注说明", custometLogCallRecordsOutVo.getRemark());
                map.put("工单号", custometLogCallRecordsOutVo.getWorkNumber());
                return map;
            }).collect(Collectors.toList());
        } else {
            excelName = "未建单";
            headList.add("来电时间");
            headList.add("报修科室");
            headList.add("报修人");
            headList.add("报修电话");
            headList.add("时长");
            headList.add("备注说明");
            dataList = wsCustometLogCallRecordsOutVos.stream().map(custometLogCallRecordsOutVo -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("来电时间", custometLogCallRecordsOutVo.getVisitTime());
                map.put("报修科室", custometLogCallRecordsOutVo.getVisitUserDeptName());
                map.put("报修人", custometLogCallRecordsOutVo.getVisitUserName());
                map.put("报修电话", custometLogCallRecordsOutVo.getVisitPhone());
                if(!StringUtils.isEmpty(custometLogCallRecordsOutVo.getFileUrl())){
                    map.put("时长", FileUtils.mp3ToDuration(ConstantYml.getInstance().getFileServerIpHost(),custometLogCallRecordsOutVo.getFileUrl(), UserInfoHolder.getToken()));
                }
                WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(custometLogCallRecordsOutVo.getRemark());
                if (null != byValue) {
                    custometLogCallRecordsOutVo.setRemark(byValue.getName() + "");
                }
                map.put("备注说明", custometLogCallRecordsOutVo.getRemark());
                return map;
            }).collect(Collectors.toList());
        }
        try {
            log.info("----------------------------------------------生成Excel开始：" + new Date());
            ExportUtil.createExcel(excelName, headList, headList, dataList, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------------------------------------------生成Excel失败：" + new Date() + "。" + e.getMessage());
        }
    }

	@Override
	@Transactional
	public void updateCalltype() {
		wsCustometLogMapper.updateCalltype();
	}
    
    

}
