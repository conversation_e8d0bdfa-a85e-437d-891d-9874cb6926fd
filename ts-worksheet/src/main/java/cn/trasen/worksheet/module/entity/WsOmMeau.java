package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_om_meau")
@Setter
@Getter
public class WsOmMeau extends WsBase implements Supplier {
    /**
     * 语音菜单配置id
     */
    @Column(name = "pk_om_meau_id")
    @ApiModelProperty(value = "语音菜单配置id")
    private String pkOmMeauId;

    /**
     * 科室id
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "科室id")
    private String deptId;

    /**
     * 科室名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 语音菜单按键内容（仅可0-9之间的数字）
     */
    @Column(name = "input_content")
    @ApiModelProperty(value = "语音菜单按键内容（仅可0-9之间的数字）")
    private String inputContent;

    @ApiModelProperty(value = "排序字段")
    private Integer px;

    @ApiModelProperty(value = "OM设备mac地址")
    private String mac;

    @ApiModelProperty(value = "附件是否必填（0否1是）")
    private Integer fileRequired;


    public WsOmMeau(String deptId, String inputContent) {
        this.deptId = deptId;
        this.inputContent = inputContent;
    }


    public WsOmMeau(String pkOmMeauId) {
        this.pkOmMeauId = pkOmMeauId;
    }



    public WsOmMeau() {
        super();
    }

    @Override
    public WsOmMeau get() {
        return this;
    }
}