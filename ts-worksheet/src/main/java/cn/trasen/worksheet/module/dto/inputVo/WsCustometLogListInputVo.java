package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/26 18:06
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Setter
@Getter
public class WsCustometLogListInputVo {

    @ApiModelProperty(value = "类型（不传为通话记录页签所有数据，1为仅查询未建单全部页签数据（2为未建单数据、3未未接听数据））")
    private String type;

    @ApiModelProperty(value = "进展（1：待派单，2：待接单，3：处理中，4：待验收，5：待评价，6：已完成，7：已暂停，8：已终止）")
    private String workStatus;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "通话时间开始（yyyy-MM-dd）")
    private Date beginCallTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "通话时间结束（yyyy-MM-dd）")
    private Date endCallTime;

    @ApiModelProperty(value = "是否接听（0否1是）")
    private String callType;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报修开始时间(yyyy-MM-dd)")
    private Date beginTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报修结束时间(yyyy-MM-dd)")
    private Date endTime;

    @ApiModelProperty(value = "报修科室id")
    private String repairManDeptId;

    @ApiModelProperty(value = "查询报修人id")
    private String repairManId;

    /**
     * 数据权限检索字段
     */
    @ApiModelProperty(hidden = true)
    private String fkDeptId;

    @ApiModelProperty(hidden = true)
    private List<String> list;

}
