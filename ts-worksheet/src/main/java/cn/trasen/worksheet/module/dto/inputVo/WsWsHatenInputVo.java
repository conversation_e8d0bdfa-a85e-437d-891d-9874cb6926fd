package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2021/7/1 11:49
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsWsHatenInputVo {

    /**
     * 工单编号
     */
    @NotNull(message = "工单编号不能为空")
    @ApiModelProperty(value = "工单编号")
    private String workNumber;
    /**
     * 催办节点ID
     */
    @ApiModelProperty(value = "催办节点ID")
    private String fkWsTaskId;

    /**
     * 催办节点名称
     */
    @ApiModelProperty(value = "催办节点名称")
    private String taskName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
