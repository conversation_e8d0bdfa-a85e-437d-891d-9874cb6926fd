package cn.trasen.worksheet.module.dto.outVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date: 2021/7/29 9:33
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Getter
@Setter
public class WsCustometServiceOutVo {

    /**
     * 坐席ID
     */
    @ApiModelProperty(value = "坐席ID")
    private String pkCustometServiceId;

    /**
     * 来电是否弹屏
     */
    @ApiModelProperty(value = "来电是否弹屏")
    private int playScreen;
    
    @ApiModelProperty(value = "是否语音播报")
    private int playVoice;

    /**
     * 坐席联系方式
     */
    @ApiModelProperty(value = "坐席联系方式")
    private String phone;

    /**
     * 坐席状态
     */
    @ApiModelProperty(value = "坐席状态")
    private int custometServiceStatus;

    /**
     * 坐席设备号
     */
    @ApiModelProperty(value = "坐席设备号")
    private String equipment;

    /**
     * 坐席人员ID
     */
    @ApiModelProperty(value = "坐席人员ID")
    private String fkUserId;

    /**
     * 坐席人员ID
     */
    @ApiModelProperty(value = "坐席科室id")
    private String fkUserDeptId;
    
}
