package cn.trasen.worksheet.module.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsExternalPersonnelStatusInputVo;
import cn.trasen.worksheet.module.dto.outVo.ExternalPersonnelPageListOutVo;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "院外人员管理")
@RestController
public class ExternalPersonnelServiceController {

    @Autowired
    private WsExternalPersonnelService wsExternalPersonnelService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="保存或修改院外人员")
    @ApiOperation(value = "保存或修改院外人员", notes = "保存或修改院外人员")
    @PostMapping("/externalPersonnel/save")
    public PlatformResult save(@RequestBody @Validated WsExternalPersonnelInputVo wsExternalPersonnelInputVo){
        return PlatformResult.success(wsExternalPersonnelService.saveOrUpdate(wsExternalPersonnelInputVo));
    }

    @ControllerLog(description="院外人员分页列表")
    @ApiOperation(value = "院外人员分页列表", notes = "院外人员分页列表")
    @GetMapping("/externalPersonnel/externalPersonnelPageList")
    public DataSet<ExternalPersonnelPageListOutVo> externalPersonnelPageList(Page page, @ApiParam(value = "单位或姓名") String fuzzy){
        List<ExternalPersonnelPageListOutVo> externalPersonnelPageListOutVoList = wsExternalPersonnelService.selectPageList(page, fuzzy,null);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),externalPersonnelPageListOutVoList);
    }

    @ControllerLog(description="禁用前查询人员状态")
    @ApiOperation(value = "禁用前查询人员状态", notes = "禁用前查询人员状态")
    @GetMapping("/externalPersonnel/QueryingPersonnelStatus/{pkExternalPersonnelId}")
    public PlatformResult QueryingPersonnelStatus(@PathVariable  @ApiParam(value = "院外人员id") String pkExternalPersonnelId){
        return wsExternalPersonnelService.QueryingPersonnelStatus(pkExternalPersonnelId);
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="启用、禁用账号")
    @ApiOperation(value = "启用、禁用账号", notes = "启用、禁用账号")
    @PostMapping("/externalPersonnel/enableOrDisable")
    public PlatformResult enableOrDisable(@RequestBody @Validated WsExternalPersonnelStatusInputVo wsExternalPersonnelStatusInputVo){
        return PlatformResult.success(wsExternalPersonnelService.enableOrDisable(wsExternalPersonnelStatusInputVo));
    }

    @ControllerLog(description="查询所有机构名称")
    @ApiOperation(value = "查询所有机构名称", notes = "查询所有机构名称")
    @PostMapping("/externalPersonnel/selectAllInstitutionalAffiliations")
    public PlatformResult selectAllInstitutionalAffiliations(@ApiParam(value = "机构名称") String institutionalAffiliations){
        return PlatformResult.success(wsExternalPersonnelService.selectAllInstitutionalAffiliations(institutionalAffiliations));
    }

    @ControllerLog(description="查询所有职位名称")
    @ApiOperation(value = "查询所有职位名称", notes = "查询所有职位名称")
    @PostMapping("/externalPersonnel/selectAllPosition")
    public PlatformResult selectAllPosition(@ApiParam(value = "职位名称") String position){
        return PlatformResult.success(wsExternalPersonnelService.selectAllPosition(position));
    }

}
