package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;

import javax.persistence.*;
import lombok.*;

@Table(name = "ws_fault_common")
@Setter
@Getter
public class WsFaultCommon extends WsBase{
    /**
     * 常见故障ID
     */
    @Column(name = "pk_fault_common_id")
    @ApiModelProperty(value = "常见故障ID")
    private String pkFaultCommonId;

    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String falutDeion;

    /**
     * 所属人ID
     */
    @Column(name = "fk_user_id")
    @ApiModelProperty(value = "所属人ID")
    private String fkUserId;
}