package cn.trasen.worksheet.module.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsWsHatenInputVo;
import cn.trasen.worksheet.module.service.WsHastenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/7/1 11:46
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@RestController
@Api(tags ="工单催办管理")
public class WorkSheetHastenController {

    @Autowired
    private WsHastenService wsHastenService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="工单催办")
    @ApiOperation(value = "工单催办", notes = "工单催办")
    @PostMapping("/workSheetHasten/save")
    public PlatformResult save(@RequestBody @Validated WsWsHatenInputVo wsHatenInputVo){
        return PlatformResult.success(wsHastenService.save(wsHatenInputVo));
    }

    @ControllerLog(description="获取催办信息")
    @ApiOperation(value = "获取催办信息", notes = "获取催办信息")
    @GetMapping("/workSheetHasten/getHastenInfo/{workNumber}")
    public PlatformResult getHastenInfo(@PathVariable @ApiParam(value = "工单编号") String workNumber){
        return PlatformResult.success(wsHastenService.getHastenInfo(workNumber));
    }

    @ControllerLog(description="催办信息列表")
    @ApiOperation(value = "催办信息列表", notes = "催办信息列表")
    @GetMapping("/workSheetHasten/getHastenInfoList/{workNumber}")
    public PlatformResult getHastenInfoList(@PathVariable @ApiParam(value = "工单编号") String workNumber){
        return PlatformResult.success(wsHastenService.getHastenInfoList(workNumber));
    }
}
