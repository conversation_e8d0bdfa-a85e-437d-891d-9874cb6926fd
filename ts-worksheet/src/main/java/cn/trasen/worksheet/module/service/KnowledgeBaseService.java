package cn.trasen.worksheet.module.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseMobileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseOperationInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseQueryInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseMobileInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBasePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeBase;

/**
 * <AUTHOR>
 * @date: 2021/8/2 14:25
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface KnowledgeBaseService {

    int saveOrUpdateKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo);

    int saveKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo);

    int updateKnowledgeBase(KnowledgeBaseInputVo knowledgeBaseInputVo);

    WsKnowledgeBase selectOneById(String pkKnowledgeBaseId);

    KnowledgeBaseInfoOutVo selectOneInfo(String pkKnowledgeBaseId);

    int giveALike(String pkKnowledgeBaseId);

    int cancelThePraise(String pkKnowledgeBaseId);

    List<KnowledgeBasePageOutVo> selectPageList(Page page, KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo);

    List<KnowledgeBasePageOutVo> selectPageCreateWorkList(Page page, KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo);

    int remove(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int notApproved(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int approve(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int withdraw(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int move(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int submit(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int delete(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    int operationKnowledgeBase(KnowledgeBaseOperationInputVo knowledgeBaseOperationInputVo, WsKnowledgeBase wsKnowledgeBase);

    /**
     * 申请人首页-我的知识点
     *
     * @param fkUserId
     * @return
     */
    Map<String, Object> getMyKnowledgeBaseCount(String fkUserId);

    /**
     * 知识点提交趋势
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    Map<String, Object> getKnowledgeBaseCountByDate(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 一级知识点占比
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getLevelOneKnowledgeBaseTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 知识点提交top榜单
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getKnowledgeBaseSubmitTopDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 科室接单统计
     *
     * @return
     */
    List<Map<String, Object>> getKnowledgeLikeCountTop(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);


    List<KnowledgeTypeTreeOutVo> selectKnowledgeTreeAllList(String categoryName);

    /**
     * 查询知识库菜单，各页签数据
     *
     * @param knowledgeBaseQueryInputVo
     * @return
     */
    Map<String, Object> selectCountsGroupStatus(KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo);

    /**
     * 知识点点赞排行（点赞数排序，相同时取贡献时间晚的）
     * @param knowledgeBaseMobileInputVo
     * @return
     */
    List<KnowledgeBaseMobileInfoOutVo> knowledgeLikeRank(KnowledgeBaseMobileInputVo knowledgeBaseMobileInputVo);


}
