package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsOmMeauInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsOmMeauListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkFlowListOutVo;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import cn.trasen.worksheet.module.mapper.WsOmMeauMapper;
import cn.trasen.worksheet.module.service.WsOmMeauService;

/**
 * 语音菜单配置
 */
@Service
public class WsOmMeauServiceImpl implements WsOmMeauService {

    @Resource
    private WsOmMeauMapper wsOmMeauMapper;

    /**
     * 保存修改
     *
     * @param wsOmMeauInputVo
     * @return
     */
    @Transactional
    @Override
    public int saveOrUpdate(WsOmMeauInputVo wsOmMeauInputVo) {
        WsOmMeau omMeau = new WsOmMeau();
        MyBeanUtils.copyBeanNotNull2Bean(wsOmMeauInputVo, omMeau);
        if (StringUtil.isEmpty(wsOmMeauInputVo.getPkOmMeauId())) {
            WsOmMeau meauBydept = seleteOneOmMeau(new WsOmMeau(wsOmMeauInputVo.getDeptId(), null));
            WsOmMeau meauByInputContent = seleteOneOmMeau(new WsOmMeau(null, wsOmMeauInputVo.getInputContent()));
            if (null != meauBydept || null != meauByInputContent) {
                throw new BusinessException("已存在科室或按键内容");
            }
            omMeau.setPkOmMeauId(IdUtils.getId());
            insertOmMeau(omMeau);
        } else {
            WsOmMeau meauBydept = seleteOneOmMeau(new WsOmMeau(wsOmMeauInputVo.getDeptId(), null));
            WsOmMeau meauByInputContent = seleteOneOmMeau(new WsOmMeau(null, wsOmMeauInputVo.getInputContent()));
            if ((null != meauBydept && !wsOmMeauInputVo.getPkOmMeauId().equals(meauBydept.getPkOmMeauId()))
                    || (null != meauByInputContent) && !wsOmMeauInputVo.getPkOmMeauId().equals(meauByInputContent.getPkOmMeauId())) {
                throw new BusinessException("已存在科室或按键内容");
            }
            updateOmMeau(omMeau);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 根据id查询单条数据
     *
     * @return
     */
    @Override
    public WsOmMeau seleteOneOmMeau(WsOmMeau wsOmMeau) {
        return wsOmMeauMapper.seleteOneOmMeau(wsOmMeau);

    }

    @Override
    public List<WsOmMeauListOutVo> selectOmMeauList(Page page) {
        return wsOmMeauMapper.selectOmMeauList(page);
    }

    /**
     * 语音菜单（即科室处理科室列表）
     *
     * @return
     */
    @Override
    public List<WsWorkFlowListOutVo> selectOmItemMeauAllList() {
        return wsOmMeauMapper.selectOmMeauAllList()
                .stream()
                .map(temp -> {
                    WsWorkFlowListOutVo workFlowOutVo = new WsWorkFlowListOutVo();
                    workFlowOutVo.setItemValue(temp.getDeptId());
                    workFlowOutVo.setItemName(temp.getDeptName());
                    return workFlowOutVo;
                }).collect(Collectors.toList());
    }

    /**
     * 语音菜单（即科室处理科室列表）
     *
     * @return
     */
    @Override
    public List<WsOmMeauListOutVo> selectOmMeauAllList() {
        return wsOmMeauMapper.selectOmMeauAllList();
    }

    /**
     * 人员数据权限过滤语音菜单（即科室处理科室列表）
     *
     * @return
     */
    @Override
    public List<WsOmMeauListOutVo> meauPermissionsList() {
        String orgRang = UserInfoHolder.getCurrentUserInfo().getOrgRang() + "," + UserInfoHolder.getCurrentUserInfo().getDeptId();
        // 人员数据权限过滤
        return wsOmMeauMapper.selectOmMeauAllList()
                .stream()
                .filter(meau -> orgRang.contains(meau.getDeptId()))
                .collect(Collectors.toList());
    }

    /**
     * 拖动排序修改
     * @param omMeauInputVos
     * @return
     */
    @Override
    public int updateSort(List<WsOmMeauInputVo> omMeauInputVos) {
        omMeauInputVos.forEach(omMeauInputVo ->{
            WsOmMeau wsOmMeau = wsOmMeauMapper.seleteOneOmMeau(new WsOmMeau(omMeauInputVo.getPkOmMeauId()));
            wsOmMeau.setUpdateTime(new Date());
            wsOmMeau.setUpdateBy(UserInfoHolder.getCurrentUserId());
            wsOmMeau.setPx(omMeauInputVo.getPx());
            wsOmMeauMapper.updateOmMeau(wsOmMeau);
        });
        return 0;
    }

    @Transactional
    @Override
    public int insertOmMeau(WsOmMeau wsOmMeau) {
        if (IndexEnum.ZERO.getValue() == wsOmMeauMapper.insertOmMeau(wsOmMeau)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    @Transactional
    @Override
    public int updateOmMeau(WsOmMeau wsOmMeau) {
        if (IndexEnum.ZERO.getValue() == wsOmMeauMapper.updateOmMeau(wsOmMeau)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        return IndexEnum.ONE.getValue();
    }

    /**
     * 批量逻辑删除
     *
     * @param ids 多个已因为逗号拼接
     * @return
     */
    @Transactional
    @Override
    public int deleteOmMeau(String ids) {
        return Optional.ofNullable(ids)
                .map(temp -> wsOmMeauMapper.deleteOmMeau(Arrays.asList(ids.split(CuttingOperatorEnum.COMMA.getValue()))))
                .orElseThrow(() -> new BusinessException("请求参数不能为空"));
    }

    /**
     * 个人是否属于处理科室
     *
     * @return
     */
    @Override
    public boolean personalBelongToBusinessDept() {
        WsOmMeau omMeau = new WsOmMeau();
        omMeau.setDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        return Optional.ofNullable(wsOmMeauMapper.seleteOneOmMeau(omMeau))
                .map(temp -> true)
                .orElseGet(() -> false);
    }

    /**
     * 根据workNumber查询单条数据
     *
     * @param workNumber 工单好
     * @return
     */
    @Override
    public WsOmMeau seleteOneOmMeauByWorkNumber(String workNumber) {
        if(StringUtil.isEmpty(workNumber)){
            return new WsOmMeau();
        }
        return wsOmMeauMapper.seleteOneOmMeauByWorkNumber(workNumber);
    }

}
