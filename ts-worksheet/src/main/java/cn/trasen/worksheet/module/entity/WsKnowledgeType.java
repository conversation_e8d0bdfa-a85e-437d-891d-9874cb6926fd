package cn.trasen.worksheet.module.entity;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.function.Supplier;
import javax.persistence.*;
import lombok.*;

@Table(name = "ws_knowledgepoints_type")
@Setter
@Getter
public class WsKnowledgeType extends WsBase implements Supplier {
    /**
     * 知识点类型ID
     */
    @ApiModelProperty(value = "知识点类型ID")
    private String pkKnowledgeTypeId;

    /**
     * 全路径
     */
    @ApiModelProperty(value = "全路径")
    private String fullPath;

    /**
     * 上级ID（即父ID）
     */
    @ApiModelProperty(value = "上级ID（即父ID）")
    private String parentId;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 知识点类型状态
     */
    @ApiModelProperty(value = "知识点类型状态")
    private int knowledgeStatus;

    @Override
    public WsKnowledgeType get() {
        return this;
    }
}