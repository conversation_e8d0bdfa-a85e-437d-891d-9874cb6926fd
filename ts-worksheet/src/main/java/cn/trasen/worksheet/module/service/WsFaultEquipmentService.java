package cn.trasen.worksheet.module.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.FaultEquipmentInputVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentOutVo;
import cn.trasen.worksheet.module.dto.outVo.FaultEquipmentQRCodeInfoOutVo;
import cn.trasen.worksheet.module.entity.WsFaultEquipment;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2021/7/2 11:13
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
public interface WsFaultEquipmentService {

    String saveEquipment(FaultEquipmentInputVo faultEquipmentInputVo) ;

    WsFaultEquipment selectOne(String id);

    FaultEquipmentOutVo selectOneFaultEquipmentOutVo(String id);

    String deleteEquipmenById(String id);

    List<FaultEquipmentOutVo> selectPageList(Page page, FaultEquipmentInputVo faultEquipmentInputVo);

    /**
     * 查询设备二维码信息
     * @param pkFaultEquipmentId
     * @return
     */
    FaultEquipmentQRCodeInfoOutVo checkTheQRCodeInfo(String pkFaultEquipmentId);

    List<FaultEquipmentOutVo> selectAllList(FaultEquipmentInputVo faultEquipmentInputVo);

}
