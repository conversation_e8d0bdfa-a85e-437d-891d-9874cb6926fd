package cn.trasen.worksheet.module.dto.inputVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Setter
@Getter
public class WsWorkSheetMobileStatisticalInputVo {

    @NotEmpty(message = "工单类型不能为空")
    @ApiModelProperty(value = "工单状态为work_status,报修方式为repair_type,故障紧急程度为fault_emergency,故障影响范围为fault_affect_scope")
    private String statusType;

    @ApiModelProperty(value = "查询开始时间（yyyy-MM-dd）")
    private String beginTime;

    @ApiModelProperty(value = "查询结束时间（yyyy-MM-dd）")
    private String endTime;

}
