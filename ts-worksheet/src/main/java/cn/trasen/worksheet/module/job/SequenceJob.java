package cn.trasen.worksheet.module.job;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.homs.core.mapper.ExceptionLogMapper;
import cn.trasen.worksheet.module.service.SequenceService;
import lombok.extern.slf4j.Slf4j;

/**
 * 序列定时器
 * <AUTHOR>
 * @date: 2021/7/8 15:40
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
@Component
@EnableScheduling
@ConditionalOnProperty(prefix = "scheduling",name ="sequenceJob.enabled",havingValue ="true" )
public class SequenceJob {

    @Autowired
    private SequenceService sequenceService;
    
	@Autowired
	private ExceptionLogMapper exceptionLogMapper;

    /**
     * 定时重置序列
     */
    @Scheduled(cron = "${scheduling.sequenceJob.cron}")
    public void resetSequenceJob() {
        try {
            sequenceService.deleteSequence();
            log.info("###########工单编号序列删除定时任执行完毕#############"+ new Date());
            sequenceService.createSequence();
            log.info("###########工单编号序列创建定时任执行完毕#############"+ new Date());
        } catch (Exception e) {
            log.error("工单编号序列定时任务执行失败："+e.getMessage(),e);
        }
    }
    
	/**
	 * 清理comm_error_logs日志，每天凌晨0点开始
	 *  0 0 * * *
	 */
	@Scheduled(cron = "0 0 * * * ?")
	public void CleanHistoryErrorLogs() {
		exceptionLogMapper.cleanHistoryErrorLogs();
	}
}
