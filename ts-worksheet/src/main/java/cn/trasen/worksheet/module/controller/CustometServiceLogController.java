package cn.trasen.worksheet.module.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsCustometLogListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsCustometLogModifyingCallRecordsInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsCustometLogCallRecordsOutVo;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/7/26 18:12
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */

@Api(tags = "坐席通话记录管理")
@RestController
public class CustometServiceLogController {

    @Autowired
    private WsCustometLogService wsCustometLogService;

    @ControllerLog(description="查询坐席状态")
    @ApiOperation(value = "查询个人坐席通话记录列表（创建工单左边列表）", notes = "查询个人坐席通话记录列表（创建工单左边列表）")
    @GetMapping("/CustometServiceLog/selectCreateWorkSheetCustometService")
    public PlatformResult selectCreateWorkSheetCustometService(@ApiParam(value = "坐席当前通话记录id")String pkCustometLogId){

        return PlatformResult.success(wsCustometLogService.selectCreateWorkSheetCustometService(pkCustometLogId));
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="修改坐席通话记录")
    @ApiOperation(value = "修改坐席通话记录", notes = "修改坐席通话记录")
    @PostMapping("/CustometServiceLog/modifyingCallRecords")
    public PlatformResult modifyingCallRecords(@RequestBody @Validated
                                              @ApiParam(value = "坐席通话信息") WsCustometLogModifyingCallRecordsInputVo modifyingCallRecords){
        return PlatformResult.success(wsCustometLogService.modifyingCallRecords(
                modifyingCallRecords.getPkCustometLogId()
                ,modifyingCallRecords.getCallType()
                ,modifyingCallRecords.getCallWorkStatus()));
    }

    @ControllerLog(description="查询通话记录")
    @ApiOperation(value = "查询通话记录", notes = "查询通话记录")
    @PostMapping("/CustometServiceLog/selectCallRecordsPageList")
    public DataSet<WsCustometLogCallRecordsOutVo> selectCallRecordsPageList(Page page, WsCustometLogListInputVo wsCustometLogListInputVo){
        List<WsCustometLogCallRecordsOutVo> custometLogCallRecordsOutVoList = wsCustometLogService.selectCallRecordsPageList(page,wsCustometLogListInputVo);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),custometLogCallRecordsOutVoList);
    }

    @ControllerLog(description="导出Excel")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @GetMapping("/CustometServiceLog/exportExcel")
    public void exportExcel(Page page, WsCustometLogListInputVo wsCustometLogListInputVo, HttpServletResponse response, HttpServletRequest request){
        wsCustometLogService.exportExcel(page, wsCustometLogListInputVo, response, request);
    }

    @ControllerLog(description="服务台今日统计")
    @ApiOperation(value = "服务台今日统计", notes = "服务台今日统计")
    @GetMapping("/CustometServiceLog/serviceDeskStatisticsToday")
    public PlatformResult serviceDeskStatisticsToday(){
        return PlatformResult.success(wsCustometLogService.serviceDeskStatisticsToday());
    }

}


