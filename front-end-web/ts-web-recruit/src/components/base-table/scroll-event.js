export default {
  data() {
    return {
      resizeTimer: null,
      left: 0,
      top: 0,

      route: '',
    };
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scroll.$refs.wrap;
    },
    hasFixedColumn() {
      return this.columns && this.columns.filter(item => item.fixed).length;
    },
  },
  watch: {
    $route: function (newVal) {
      if (newVal && newVal.path == this.route) {
        window.dispatchEvent(new Event('resize'));
      }
    },
  },
  async mounted() {
    this.route = this.$route.path;

    window.addEventListener('resize', this.computedTableWidth);
    this.scrollWrapper.addEventListener('scroll', this.handleScroll);
    await this.$nextTick();
    this.computedTableWidth();
    this.computedTableStyle();
  },
  beforeDestroy() {
    this.scrollWrapper.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.computedTableWidth);
  },
  methods: {
    computedTableStyle() {
      let container = this.$refs.tableContainer,
        tableHead = container.querySelector('.el-table__header-wrapper'),
        tableBody = container.querySelector('.el-table__body-wrapper'),
        hasFixedColumn =
          this.hasFixedColumn ||
          (
            container.querySelector('.el-table__fixed') ||
            container.querySelector('.el-table__fixed-right') ||
            {}
          ).clientWidth > 0;

      tableBody.style.marginTop =
        (this.showHeader
          ? tableHead.offsetHeight || 31
          : tableHead.offsetHeight) + 'px';
      setTimeout(() => {
        if (hasFixedColumn) {
          let fixedTable = container.querySelector('.el-table__fixed-right');
          fixedTable.offsetWidth
            ? (fixedTable.style.width = fixedTable.offsetWidth + 1 + 'px')
            : null;
        }
        this.computedTableScrollingPlace();
      }, 500);
    },
    computedTableWidth() {
      this.resizeTimer && clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(async () => {
        let container = this.$refs.tableContainer,
          headDom = container.querySelector('.el-table__header'),
          viewDom = container.querySelector('.el-scrollbar__view');

        if (headDom.offsetWidth > container.offsetWidth) {
          viewDom.style.width = headDom.offsetWidth + 2 + 'px';
        } else {
          viewDom.style.width = '100%';
        }
        this.computedTableScrollingPlace();
        await this.$nextTick();
        this.$refs.scroll.update();
      }, 200);
    },
    handleScroll(e) {
      requestAnimationFrame(() => {
        const horizonScroll = this.left != e.target.scrollLeft,
          verticalScroll = this.top != e.target.scrollTop;
        this.top = e.target.scrollTop;
        this.left = e.target.scrollLeft;

        let container = this.$refs.tableContainer,
          headDom = container.querySelector('.el-table__header-wrapper'),
          hasFixedColumn =
            this.hasFixedColumn ||
            (
              container.querySelector('.el-table__fixed') ||
              container.querySelector('.el-table__fixed-right') ||
              {}
            ).clientWidth > 0;

        headDom.style.left = -e.target.scrollLeft + 'px';
        if (
          e.target.scrollWidth > container.offsetWidth &&
          hasFixedColumn &&
          horizonScroll
        ) {
          this.computedTableScrollingPlace();
        }

        if (horizonScroll && this.showSummary) {
          let summaryDom = container.querySelector('.el-table__footer-wrapper');
          summaryDom.style.left = -e.target.scrollLeft + 'px';
        }

        if (verticalScroll && e.target.scrollHeight > container.offsetHeight) {
          let fixedBody = container.querySelectorAll(
            '.el-table__fixed-body-wrapper'
          );
          fixedBody &&
            fixedBody.forEach(
              item => (item.style.marginTop = -e.target.scrollTop + 'px')
            );
        }
      });
    },
    computedTableScrollingPlace() {
      let container = this.$refs.tableContainer,
        tableBody = container.querySelector('.el-table__body-wrapper'),
        hasFixedColumn =
          this.hasFixedColumn ||
          (
            container.querySelector('.el-table__fixed') ||
            container.querySelector('.el-table__fixed-right') ||
            {}
          ).clientWidth > 0;

      const scrollLeft = this.$refs.scroll.$refs.wrap.scrollLeft,
        scrollWidth = this.$refs.scroll.$refs.wrap.scrollWidth;

      if (!hasFixedColumn || container.offsetWidth >= scrollWidth) {
        tableBody.className = 'el-table__body-wrapper is-scrolling-none';
      } else {
        if (scrollLeft == 0) {
          tableBody.className = 'el-table__body-wrapper is-scrolling-left';
        } else if (scrollWidth - container.offsetWidth > scrollLeft) {
          tableBody.className = 'el-table__body-wrapper is-scrolling-middle';
        } else {
          tableBody.className = 'el-table__body-wrapper is-scrolling-right';
        }
      }
    },
  },
};
