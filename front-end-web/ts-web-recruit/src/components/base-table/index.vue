<script>
import scrollJS from './scroll-event';

export default {
  name: 'BaseTable',
  mixins: [scrollJS],
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    border: {
      type: Boolean,
      default: () => true,
    },
    stripe: {
      type: Boolean,
      default: () => false,
    },
    showSummary: Boolean,
    pageSizes: {
      type: Array,
      default: () => [20, 40, 60, 80, 100, 200, 500, 1000, 2000],
    },
    layout: {
      type: String,
      default: () => 'total, sizes, prev, pager, next, jumper',
    },
    pageProp: {
      type: Object,
      default: () => {},
    },
    pageEvent: {
      type: Object,
      default: () => {},
    },
    hasPage: {
      type: Boolean,
      default: () => true,
    },
    defaultSort: Object,
  },
  data() {
    return {
      rows: [],
      pageSize: 100,
      pageNo: 1,
      total: 0,
      sort: 'desc',
      sidx: 'a.create_time',
    };
  },
  watch: {
    defaultSort: {
      handler(val = {}) {
        let { prop, order } = val;
        this.sort =
          {
            ascending: 'asc',
            descending: 'desc',
          }[order] || 'desc';
        this.sidx = prop || 'a.create_time';
      },
      immediate: true,
      deep: true,
    },
  },
  render(h) {
    let tableProp = { ...this.$attrs, ...this.$props };
    delete tableProp.pageProp;
    delete tableProp.pageEvent;
    delete tableProp.layout;
    delete tableProp.pageSizes;
    delete tableProp.hasPage;

    let childNodeList = [
      h(
        'div',
        {
          ref: 'tableContainer',
          class:
            'table-content' + (this.showSummary ? ' summary-fiexd-table' : ''),
        },
        [
          h(
            'ElScrollbar',
            {
              ref: 'scroll',
              style: 'width: 100%; height: 100%',
              attrs: {
                viewClass: 'scroll-table-container',
                wrapClass: 'scroll-wrap-class',
              },
            },
            [
              h(
                'TsTable',
                {
                  ref: 'table',
                  class: 'table-head',
                  attrs: { ...tableProp, data: this.rows },
                  on: {
                    'sort-change': this.defaultSortChange,
                    ...this.$listeners,
                    'header-dragend': this.handleDragend,
                  },
                  scopedSlots: this.$scopedSlots,
                },
                this.$slots.default
              ),
            ]
          ),
        ]
      ),
    ];
    if (this.hasPage) {
      childNodeList.push(
        h('TsPagination', {
          class: 'base-table-page',
          attrs: {
            'current-page': this.pageNo,
            'page-size': this.pageSize,
            'page-sizes': this.pageSizes,
            layout: this.layout,
            total: this.total,
            ...this.pageProp,
          },
          on: {
            'current-change': this.handleCurrentChange,
            'size-change': this.handlePageSizeChange,
            'update:page-size': value => (this.pageSize = value),
            ...this.pageEvent,
          },
        })
      );
    }

    return h(
      'div',
      {
        ref: 'baseTableContent',
        class: 'base-table-content',
      },
      childNodeList
    );
  },
  methods: {
    handleDragend() {
      if (this.$listeners.headerDragend) {
        this.$listeners.headerDragend(...arguments);
      }
      if (this.$listeners['header-dragend']) {
        this.$listeners['header-dragend'](...arguments);
      }
      this.computedTableWidth();
    },
    /**@desc 主动获取表格数据 */
    getTableData() {
      return this.defaultSort && Object.keys(this.defaultSort).length
        ? {
            pageNo: this.pageNo,
            pageSize: this.pageSize,
            sort: this.sort,
            sidx: this.sidx,
          }
        : {
            pageNo: this.pageNo,
            pageSize: this.pageSize,
          };
    },
    /**@desc 数据刷新 */
    refresh() {
      let rows,
        totalCount,
        argList = Array(...arguments),
        obj = argList.find(
          arg => Object.prototype.toString.call(arg) == '[object Object]'
        ),
        list = argList.find(arg => arg instanceof Array),
        total =
          argList.find(
            arg => typeof arg === 'number' || typeof arg === 'string'
          ) || 0;

      if (obj) {
        rows = obj.rows;
        totalCount = obj.totalCount;
      } else {
        rows = list;
        totalCount = Number(total);
      }

      this.rows = rows || [];
      this.total = totalCount || 0;
    },
    /**@desc 当前页码改变 */
    handleCurrentChange(value) {
      this.pageNo = value;
      this.triggerRefresh();
    },
    /**@desc 当前 分页大小 改变 */
    handlePageSizeChange(value) {
      this.pageSize = value;
      this.triggerRefresh();
    },
    /**@desc 主动触发刷新 */
    triggerRefresh() {
      this.$emit('refresh', this.getTableData());
    },
    /**@desc 默认排序改变 */
    defaultSortChange({ column, order, prop }) {
      this.sort =
        {
          ascending: 'asc',
          descending: 'desc',
        }[order] || 'desc';
      this.sidx = prop;
      this.triggerRefresh();
    },
  },
};
</script>

<style lang="scss" scoped>
.table-content {
  overflow: hidden;
  transform: scale(1);
  flex: 1;
}
.table-head {
  border: none;
}
.table-head ::v-deep .el-table__header-wrapper {
  position: fixed;
  top: 0;
  z-index: 2;
  max-width: unset;
  width: inherit;
  overflow: visible;
}
::v-deep {
  .el-scrollbar__bar {
    z-index: 8;
  }
  .el-table__fixed-header-wrapper {
    z-index: 4;
    .el-table__header th {
      border-bottom: none;
    }
  }
  .el-table__fixed-right {
    z-index: 2;
    height: 100% !important;
  }
  .scroll-wrap-class {
    height: calc(100% + 8px);
  }
}
::v-deep .scroll-table-container .el-table {
  position: static;
  .el-table__fixed-body-wrapper .el-table__cell {
    padding: 5px 0;
  }
  thead {
    color: $primary-black;
    .cell {
      font-weight: 600 !important;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
::v-deep .el-table__header th * {
  text-align: center;
}
.base-table-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.base-table-page {
  margin-top: $primary-spacing;
  text-align: right;
}
::v-deep .summary-fiexd-table {
  .ts-table {
    padding-bottom: 32px;
  }
  .el-table__footer-wrapper {
    position: absolute;
    bottom: 0;
    overflow: visible;
  }
}
</style>
