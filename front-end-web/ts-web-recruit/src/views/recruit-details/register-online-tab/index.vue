<template>
  <div id="RegisterTabsBox">
    <Authentication
      :planId="planId"
      @getIsSignUpHandle="getIsSignUpHandle"
      v-if="progress === 'check'"
      type="searchRegistrationInfo"
    />
    <div class="psot-table-box" v-if="progress === 'select'">
      <p class="plan-person-tips">
        请注意，本次招聘，每人限报{{ planNoticeInfo.limitPostNum }}个岗位！
      </p>
      <base-table
        class="form-table"
        ref="table"
        border
        stripe
        :columns="columns"
        :hasPage="false"
      >
        <template #action="{ row }">
          <el-button
            v-if="row.signUpStatus === 1 || row.signUpStatus === 3"
            @click="signUpHandle(row)"
            type="text"
            size="small"
            >去报名
          </el-button>
          <span v-if="row.signUpStatus === 2" style="color: #70b603"
            >已报名
          </span>
          <el-button
            v-if="row.signUpStatus === 2"
            @click="cancelSignUpHandle(row)"
            type="text"
            size="small"
            >取消报名
          </el-button>
        </template>
      </base-table>
    </div>
    <div v-if="progress === 'write'">
      <component
        :is="templateName"
        :postId="postId"
        :planId="planId"
        :basicsInfo="basicsInfo"
        @signUpSuccess="signUpSuccess"
        @backPostLIST="backPostLIST"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import {
  cancelSignUp,
  fastSignUpJk,
  userGetPostList,
} from '@/api/recruitDetails';
import Authentication from '@/components/Authentication';
import BaseTable from '@/components/base-table';

import lyszyy from '@/views/components/post-from/lyszyy';
import cscjk from '@/views/components/post-from/cscjk';

import data from './data';

export default {
  components: {
    Authentication,
    BaseTable,
    lyszyy,
    cscjk,
  },
  mixins: [data],
  props: {
    planId: {
      type: String,
      default: () => '',
    },
    planNoticeInfo: {
      type: Object,
      default: () => {},
    },
  },
  data: () => ({
    progress: 'check',
    isSignUp: false,
    hospitalFromName: '',
    postId: '',
    basicsInfo: {},
  }),
  computed: {
    ...mapState([
      'templateName',
      'registrationName',
      'registrationIdentityCard',
      'registrationSex',
      'registrationBirthday',
    ]),
  },
  methods: {
    // 获取登陆状态
    getIsSignUpHandle(arr) {
      // 是否已报名过一个岗位
      this.isSignUp = arr.some(item => item.signUpStatus === 2);

      this.progress = 'select';

      this.$nextTick(() => {
        this.$refs.table.refresh({
          rows: arr,
        });
      });
    },
    // 去报名
    async signUpHandle(row) {
      this.postId = row.id;
      // 已有一个岗位报名 则能进行快速报名
      if (this.isSignUp) {
        let data = {
          identityCard: this.registrationIdentityCard,
          name: this.registrationName,
          planId: this.planId,
          postId: row.id,
        };

        const result = await fastSignUpJk(data);
        if (!result.success) {
          this.$message({
            type: 'warning',
            center: true,
            message: result.message,
          });
          return false;
        }

        this.$message({
          type: 'success',
          center: true,
          message: '恭喜您报名成功，可去”进度查询“中跟踪进度哦!',
        });
        this.getUserSignUpPostHandle();
      } else {
        this.basicsInfo = {
          name: this.registrationName,
          identityCard: this.registrationIdentityCard,
          gender: this.registrationSex,
          dateBirth: this.registrationBirthday,
        };
        this.progress = 'write';
      }
    },
    // 取消报名
    async cancelSignUpHandle(item) {
      try {
        await this.$confirm('是否确定要取消报名?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        let data = {
          identityCard: this.registrationIdentityCard,
          signupId: item.signupId,
          name: this.registrationName,
        };
        const result = await cancelSignUp(data);
        if (result.statusCode === 200 && result.success) {
          this.$message({
            type: 'success',
            center: true,
            message: '操作成功!',
          });
          this.getUserSignUpPostHandle();
        } else {
          this.$message({
            type: 'warning',
            center: true,
            message: result.message,
          });
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 更新该考生的表格状态 重新查询
    async getUserSignUpPostHandle() {
      let data = {
        identityCard: this.registrationIdentityCard,
        name: this.registrationName,
        planId: this.planId,
      };
      const res = await userGetPostList(data);
      if (res.success && res.statusCode === 200)
        this.getIsSignUpHandle(res.object);
    },
    signUpSuccess() {
      this.$emit('signUpSuccess');
    },
    backPostLIST() {
      this.progress = 'select';
      this.getUserSignUpPostHandle();
    },
  },
};
</script>

<style lang="scss" scoped>
#RegisterTabsBox {
  .plan-person-tips {
    color: #606266;
    margin: 0 0 8px 0px;
  }
}
</style>
