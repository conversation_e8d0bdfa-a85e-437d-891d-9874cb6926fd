<template>
  <ul class="file-ul reviewProgress">
    <li v-for="item in list" :key="item.id">
      <p :title="item.originalName">{{ item.originalName }}</p>
      <span class="previewA" @click="prevoewHadnle(item)">预览</span>
    </li>

    <el-image
      ref="preview"
      style="display: none"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </ul>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
    },
  },
  data() {
    return {
      previewFile: '',
      previewFileList: [],
    };
  },
  methods: {
    prevoewHadnle(file) {
      this.previewFile = location.origin + file.realPath;
      this.previewFileList = [this.previewFile];
      this.$nextTick(() => {
        this.$refs.preview.clickHandler();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.file-ul {
  &.reviewProgress {
    margin-left: 0;
  }

  li {
    font-size: 14px;
    color: #333333;
    line-height: 19px;
    margin: 5px 0;
    padding-left: 8px;
    display: flex;
    align-items: center;

    p {
      width: 100px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin: 0px !important;
    }

    .previewA {
      cursor: pointer;
      font-size: 12px;
      color: #5260ff;
      margin: 0 8px;
      line-height: 16px;
    }
  }
}
</style>
