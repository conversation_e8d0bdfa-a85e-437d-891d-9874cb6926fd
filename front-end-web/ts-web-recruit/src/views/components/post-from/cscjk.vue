<template>
  <div id="FormBox">
    <h1 v-show="!readonly">
      {{ new Date().getFullYear() }}年长沙市经开医院公开招聘报名表
    </h1>

    <table
      v-show="readonly"
      ref="tableDataBox"
      id="PrintFormTable"
      class="table-data"
      style="table-layout: fixed"
    >
      <tr>
        <td class="title" colspan="6">
          <h1>{{ new Date().getFullYear() }}年长沙经开医院公开招聘报名表</h1>
        </td>
      </tr>
      <tr>
        <td class="title" colspan="6">基本信息</td>
      </tr>
      <tr>
        <td>姓名</td>
        <td>{{ form.name }}</td>
        <td>性别</td>
        <td>{{ form.gender }}</td>
        <td>身份证号码</td>
        <td>{{ form.identityCard }}</td>
      </tr>
      <tr>
        <td>政治面貌</td>
        <td>{{ form.politicalStatus }}</td>
        <td>民族</td>
        <td>{{ form.nationalityName }}</td>
        <td>出生日期</td>
        <td>{{ form.dateBirth }}</td>
      </tr>
      <tr>
        <td>学历</td>
        <td>{{ form.educationName }}</td>
        <td>职称</td>
        <td>{{ form.jobtitleName }}</td>
        <td>是否应届生</td>
        <td>{{ form.freshGraduate }}</td>
      </tr>
      <tr>
        <td>是否全日制</td>
        <td>{{ form.fullTime }}</td>
        <td>身高</td>
        <td>{{ form.height && form.height.length > 0 ? form.height + 'cm' : '-' }}</td>
        <td>体重</td>
        <td>{{ form.weight && form.weight.length > 0 ? form.weight + 'kg' : '-' }}</td>
      </tr>
      <tr>
        <td>阴历生日</td>
        <td>{{ form.birthLunar }}</td>
        <td>是否有驾照</td>
        <td>{{ form.isDrivingLicense }}</td>
        <td>目前在职情况</td>
        <td>{{ form.incumbency }}</td>
      </tr>
      <tr>
        <td>期望月薪</td>
        <td>
          {{ form.monthlyPay && form.monthlyPay.length > 0 ? form.monthlyPay + '元' : '-' }}
        </td>
        <td>可接受最低月薪</td>
        <td>
          {{
            form.minimumMonthlyPay && form.minimumMonthlyPay.length > 0
              ? form.minimumMonthlyPay + '元'
              : '-'
          }}
        </td>
        <td>最快入职日期</td>
        <td>{{ form.fastestEntryDate }}</td>
      </tr>
      <tr>
        <td>籍贯</td>
        <td>{{ form.nativePlace }}</td>
        <td>婚育状况</td>
        <td>{{ form.maritalStatus }}</td>
        <td>子女</td>
        <td>{{ form.children && form.children.length > 0 ? form.children + '个' : '-' }}</td>
      </tr>
      <tr>
        <td>外语水平</td>
        <td>{{ form.foreignLanguageProficiency }}</td>
      </tr>
      <tr>
        <td>户口所在地</td>
        <td colspan="5">
          {{ (form.region && form.region.join('')) || ''
          }}{{ form.positionDetails }}
        </td>
      </tr>
      <tr>
        <td>通讯地址</td>
        <td colspan="5">
          {{ (form.address && form.address.join('')) || '' }}
          {{ form.addressDetails }}
        </td>
      </tr>
      <tr>
        <td>联系方式</td>
        <td>{{ form.iphone }}</td>
        <td>Email</td>
        <td>{{ form.email }}</td>
        <td>紧急联络人及电话</td>
        <td>{{ form.contactNameAndIphone }}</td>
      </tr>
      <tr>
        <td>个人专长/业余爱好</td>
        <td colspan="5">{{ form.hobby }}</td>
      </tr>
      <tr>
        <td class="title" colspan="6">学习经历</td>
      </tr>
      <tr class="title-tr">
        <td>阶段</td>
        <td>起止时间</td>
        <td>毕业院校</td>
        <td>专业</td>
        <td>学位/全日制</td>
        <td>附件</td>
      </tr>
      <tr
        class="value-tr"
        v-for="(item, index) in form.studyCareer"
        :key="`study${index}`"
      >
        <td>{{ item.stage }}</td>
        <td>{{ (item.time && item.time.join(' ~ ')) || '' }}</td>
        <td>{{ item.school }}</td>
        <td>{{ item.major }}</td>
        <td>{{ item.degree }}/{{ item.fullTime }}</td>
        <td>
          <file-item :list="item.file" />
        </td>
      </tr>
      <tr>
        <td>在校期间获奖情况</td>
        <td colspan="5">{{ form.awardsSchool }}</td>
      </tr>
      <tr>
        <td class="title" colspan="6">工作经历</td>
      </tr>
      <tr class="title-tr">
        <td>起止时间</td>
        <td>单位名称</td>
        <td>科室</td>
        <td>职务/薪资</td>
        <td>证人/联系</td>
        <td>离职原因</td>
      </tr>
      <tr
        class="value-tr"
        v-for="(item, index) in form.workCareer"
        :key="`work${index}`"
      >
        <td>{{ (item.time && item.time.join(' ~ ')) || '' }}</td>
        <td>{{ item.companyName }}</td>
        <td>{{ item.dept }}</td>
        <td>{{ item.job }}/{{ item.pay }}</td>
        <td>{{ item.person }}</td>
        <td>{{ item.reasonForLeaving }}</td>
      </tr>
      <tr>
        <td>临床技能·专业特长</td>
        <td colspan="5">{{ form.speciality }}</td>
      </tr>
      <tr>
        <td class="title" colspan="6">既往学习、工作的自我评价及获奖情况</td>
      </tr>
      <tr>
        <td colspan="6">{{ form.evaluate }}</td>
      </tr>
      <tr>
        <td class="title" colspan="6">进修培训经历</td>
      </tr>
      <tr class="title-tr">
        <td>起止时间</td>
        <td>培训项目</td>
        <td>培训机构</td>
        <td>授课老师</td>
        <td>取得执业资格</td>
        <td>认证</td>
      </tr>
      <tr
        class="value-tr"
        v-for="(item, index) in form.trainCareer"
        :key="`train${index}`"
      >
        <td>{{ (item.time && item.time.join(' ~ ')) || '' }}</td>
        <td>{{ item.program }}</td>
        <td>{{ item.institution }}</td>
        <td>{{ item.teacher }}</td>
        <td>{{ item.qualifying }}</td>
        <td>
          <file-item :list="item.file" />
        </td>
      </tr>

      <tr>
        <td class="title" colspan="6">职称晋升</td>
      </tr>
      <tr class="title-tr">
        <td>职称</td>
        <td colspan="2">职称的专业</td>
        <td>取得时间</td>
        <td>职称取得地点</td>
        <td>附件</td>
      </tr>
      <tr
        class="value-tr"
        v-for="(item, index) in form.titleCareer"
        :key="`title${index}`"
      >
        <td>{{ item.title }}</td>
        <td colspan="2">{{ item.titleProfessional }}</td>
        <td>{{ item.acquisitionTime }}</td>
        <td>{{ item.locale }}</td>
        <td>
          <file-item :list="item.file" />
        </td>
      </tr>

      <tr>
        <td class="title" colspan="6">家庭主要成员</td>
      </tr>
      <tr class="title-tr">
        <td>关系</td>
        <td>姓名</td>
        <td>出生年月</td>
        <td colspan="2">工作单位</td>
        <td>联系方式</td>
      </tr>
      <tr
        class="value-tr"
        v-for="(item, index) in form.familyCareer"
        :key="`family${index}`"
      >
        <td>{{ item.relation }}</td>
        <td>{{ item.name }}</td>
        <td>{{ item.birthDay }}</td>
        <td colspan="2">{{ item.workUnit }}</td>
        <td>{{ item.iphone }}</td>
      </tr>
      <tr>
        <td class="title" colspan="6">其他情况</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">你是否有亲友在本院工作？</td>
        <td>{{ form.isFriendWork }}</td>
        <td colspan="2">{{ form.isFriendWorkLabel }}</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">你是否有重大疾病/手术记录？</td>
        <td>{{ form.isOperation }}</td>
        <td colspan="2">{{ form.isOperationLabel }}</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">
          你是否有可能影响你完成所申请工作的健康缺陷或其他缺陷？
        </td>
        <td>{{ form.isHealthy }}</td>
        <td colspan="2">{{ form.isHealthyLabel }}</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">你是否受到其它单位记过、察看、开除或其他严重处分？</td>
        <td>{{ form.isPunish }}</td>
        <td colspan="2">{{ form.isisPunishLabel }}</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">你是否曾因触犯法律受到刑事处罚或治安处罚？</td>
        <td>{{ form.isLaw }}</td>
        <td colspan="2">{{ form.isLawLabel }}</td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">你从何处得知本招聘信息？</td>
        <td colspan="1">{{ form.isGetInfo }}</td>
        <td colspan="2"></td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">
          是否接受公司以考察你个人素质为目的相关测试（结果保密）？
        </td>
        <td colspan="1">{{ form.isTest }}</td>
        <td colspan="2"></td>
      </tr>
      <tr class="value-tr">
        <td colspan="3">是否接受工作调配？</td>
        <td colspan="1">{{ form.isChange }}</td>
        <td colspan="2"></td>
      </tr>
    </table>
    <ts-form
      v-show="!readonly"
      id="SetFormTableForm"
      ref="ruleForm"
      :model="form"
      labelWidth="130px"
    >
      <span class="group-title"> 1、基本信息 </span>
      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="姓名" prop="name" :rules="rules.required">
            <ts-input v-model="form.name" disabled placeholder="请输入" />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="性别" prop="gender" :rules="rules.required">
            <ts-select
              style="width: 100%"
              v-model="form.gender"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of sexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item
            label="身份证号码"
            prop="identityCard"
            :rules="rules.required"
          >
            <ts-input
              v-model="form.identityCard"
              disabled
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="政治面貌">
            <ts-select
              style="width: 100%"
              v-model="form.politicalStatus"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of politicalStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="民族">
            <ts-input v-model="form.nationalityName" placeholder="请输入" />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="出生日期：">
            <ts-date-picker
              style="width: 100%"
              v-model="form.dateBirth"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="学历" prop="educationId" :rules="rules.required">
            <ts-select
              style="width: 100%"
              v-model="form.educationId"
              @change="handleEducationChange"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of educationList"
                :key="item.itemNameValue"
                :label="item.itemName"
                :value="item.itemNameValue"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="职称"
            prop="jobtitleName"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="form.jobtitleName"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of jobTitleList"
                :key="item.jobtitleId"
                :label="item.jobtitleName"
                :value="item.jobtitleName"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="8">
          <ts-form-item
            label="是否应届生"
            prop="freshGraduate"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="form.freshGraduate"
              clearable
              placeholder="请选择"
            >
              <ts-option key="是" label="是" value="是"></ts-option>
              <ts-option key="否" label="否" value="否"></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="是否全日制"
            prop="fullTime"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="form.fullTime"
              clearable
              placeholder="请选择"
            >
              <ts-option key="是" label="是" value="是"></ts-option>
              <ts-option key="否" label="否" value="否"></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="身高">
            <ts-input
              v-model="form.height"
              placeholder="请输入(cm)"
              oninput="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
              onafterpaste="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="体重">
            <ts-input
              v-model="form.weight"
              placeholder="请输入(kg)"
              oninput="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
              onafterpaste="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="阴历生日：">
            <ts-date-picker
              style="width: 100%"
              v-model="form.birthLunar"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="是否有驾照">
            <ts-radio-group v-model="form.isDrivingLicense">
              <ts-radio label="是">是</ts-radio>
              <ts-radio label="否">否</ts-radio>
            </ts-radio-group>
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="目前在职情况">
            <ts-radio-group v-model="form.incumbency">
              <ts-radio label="在职">在职</ts-radio>
              <ts-radio label="离职">离职</ts-radio>
              <ts-radio label="退休">退休</ts-radio>
            </ts-radio-group>
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="期望月薪">
            <ts-input
              v-model="form.monthlyPay"
              placeholder="请输入(元)"
              oninput="this.value=this.value?.replace(/\D/g,'')"
              onafterpaste="this.value=this.value?.replace(/\D/g,'')"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="可接受最低月薪">
            <ts-input
              v-model="form.minimumMonthlyPay"
              placeholder="请输入(元)"
              oninput="this.value=this.value?.replace(/\D/g,'')"
              onafterpaste="this.value=this.value?.replace(/\D/g,'')"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="最快入职日期：">
            <ts-date-picker
              style="width: 100%"
              v-model="form.fastestEntryDate"
              :disabledDate="disabledDate"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item
            label="籍贯："
            prop="nativePlace"
            :rules="rules.required"
          >
            <ts-input v-model="form.nativePlace" placeholder="请输入" />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="婚育状况">
            <ts-radio-group v-model="form.maritalStatus">
              <ts-radio label="未婚">未婚</ts-radio>
              <ts-radio label="离异">离异</ts-radio>
              <ts-radio label="已婚">已婚</ts-radio>
            </ts-radio-group>
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="子女">
            <ts-input
              v-model="form.children"
              placeholder="请输入(个)"
              oninput="this.value=this.value?.replace(/\D/g,'')"
              onafterpaste="this.value=this.value?.replace(/\D/g,'')"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="外语水平">
            <ts-input
              v-model="form.foreignLanguageProficiency"
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <el-col :span="8">
          <ts-form-item label="户口所在地">
            <ts-cascader
              style="width: 100%"
              v-model="form.region"
              placeholder="请选择"
              :options="regionOption"
              :props="{
                value: 'label',
              }"
            ></ts-cascader>
          </ts-form-item>
        </el-col>
        <el-col :span="13">
          <ts-form-item labelWidth="0" label="" style="width: 100%">
            <ts-input
              style="width: 365px"
              v-model="form.positionDetails"
              placeholder="请输入"
            />
          </ts-form-item>
        </el-col>
      </ts-row>

      <ts-row>
        <el-col :span="8">
          <ts-form-item label="通讯地址">
            <ts-cascader
              style="width: 100%"
              v-model="form.address"
              placeholder="请选择"
              :options="regionOption"
              :props="{
                value: 'label',
              }"
            ></ts-cascader>
          </ts-form-item>
        </el-col>
        <el-col :span="13">
          <ts-form-item labelWidth="0" label="" style="width: 100%">
            <ts-input
              style="width: 365px"
              v-model="form.addressDetails"
              placeholder="请输入"
            />
          </ts-form-item>
        </el-col>
      </ts-row>

      <ts-row>
        <ts-col :span="8">
          <ts-form-item label="联系方式" prop="iphone" :rules="rules.iphone">
            <ts-input
              v-model="form.iphone"
              placeholder="请输入"
              oninput="this.value=this.value?.replace(/\D/g,'')"
              onafterpaste="this.value=this.value?.replace(/\D/g,'')"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="Email" prop="email" :rules="rules.email">
            <ts-input v-model="form.email" placeholder="请输入" />
          </ts-form-item>
        </ts-col>

        <ts-col :span="8">
          <ts-form-item label="紧急联络人及电话">
            <ts-input
              v-model="form.contactNameAndIphone"
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-form-item label="个人专长/业余爱好">
          <ts-input
            v-model="form.hobby"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
          />
        </ts-form-item>
      </ts-row>

      <span class="group-title"> 2、学习经历 </span>

      <ts-row>
        <form-table
          :formData="form"
          operateDataKey="studyCareer"
          :columns="studyCareerColumns"
        >
          <template #stage="{ row, column }">
            <ts-select
              class="width-select-90"
              v-model="row[column.property]"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of stageList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </template>
          <template #time="{ row, column }">
            <el-date-picker
              style="width: 100%"
              value-format="yyyy-MM-dd"
              v-model="row[column.property]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </template>
          <template #school="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`studyCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                class="width-input-100"
                v-model="row[column.property]"
              ></ts-input>
            </ts-form-item>
          </template>
          <template #major="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`studyCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                class="width-input-100"
                v-model="row[column.property]"
              ></ts-input>
            </ts-form-item>
          </template>
          <template #degree="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`studyCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                class="width-input-100"
                v-model="row[column.property]"
              ></ts-input>
            </ts-form-item>
          </template>
          <template #fullTime="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`studyCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-select
                class="width-select-90"
                v-model="row[column.property]"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of fullTimeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </template>
          <template #file="{ row, column }">
            <form-upload
              class="form-upload"
              accept=".jpg,.jpeg,.png"
              v-model="row[column.property]"
            />
          </template>
        </form-table>
      </ts-row>

      <ts-row>
        <ts-form-item label="在校期间获奖情况">
          <ts-input
            v-model="form.awardsSchool"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
          />
        </ts-form-item>
      </ts-row>

      <span class="group-title"> 3、工作经历 </span>

      <ts-row>
        <form-table
          :formData="form"
          operateDataKey="workCareer"
          :columns="workCareerColumns"
        >
          <template #time="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`workCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <el-date-picker
                style="width: 100%"
                v-model="row[column.property]"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </ts-form-item>
          </template>

          <template #companyName="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`workCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                class="width-input-100"
                v-model="row[column.property]"
              ></ts-input>
            </ts-form-item>
          </template>

          <template #dept="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #job="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #pay="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #person="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #reasonForLeaving="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>
        </form-table>
      </ts-row>

      <ts-row>
        <ts-form-item label="临床技能·专业特长">
          <ts-input
            v-model="form.speciality"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
          />
        </ts-form-item>
      </ts-row>

      <span class="group-title"> 4、既往学习、工作的自我评价及获奖情况 </span>

      <ts-row>
        <ts-form-item label="自我评价·获奖情况">
          <ts-input
            v-model="form.evaluate"
            type="textarea"
            class="textarea"
            maxlength="200"
            show-word-limit
          />
        </ts-form-item>
      </ts-row>

      <span class="group-title"> 5、进修培训经历 </span>

      <ts-row>
        <form-table
          :formData="form"
          operateDataKey="trainCareer"
          :columns="trainCareerColumns"
        >
          <template #time="{ row, column }">
            <el-date-picker
              style="width: 100%"
              value-format="yyyy-MM-dd"
              v-model="row[column.property]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </template>

          <template #program="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #institution="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #teacher="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #qualifying="{ row, column }">
            <ts-input
              class="width-input-100"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #file="{ row, column }">
            <form-upload
              class="form-upload"
              accept=".jpg,.jpeg,.png"
              v-model="row[column.property]"
            />
          </template>
        </form-table>
      </ts-row>

      <span class="group-title"> 6、职称晋升 </span>

      <ts-row>
        <form-table
          :formData="form"
          operateDataKey="titleCareer"
          :columns="titleCareerColumns"
        >
          <template #title="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`titleCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-select
                style="width: 100%"
                v-model="row[column.property]"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of titleList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </template>

          <template #titleProfessional="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`titleCareer.${index}.${column.property}`"
              :rules="rules.required"
            >
              <span class="required-icon">*</span>
              <ts-input
                style="width: 100%"
                v-model="row[column.property]"
              ></ts-input>
            </ts-form-item>
          </template>

          <template #acquisitionTime="{ row, column }">
            <el-date-picker
              style="width: 180px !important"
              value-format="yyyy-MM-dd"
              v-model="row[column.property]"
              type="date"
              placeholder="选择日期"
            />
          </template>

          <template #locale="{ row, column }">
            <ts-input
              style="width: 100%"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #file="{ row, column }">
            <form-upload
              class="form-upload"
              accept=".jpg,.jpeg,.png"
              v-model="row[column.property]"
            />
          </template>
        </form-table>
      </ts-row>

      <span class="group-title"> 7、家庭关系 </span>

      <ts-row>
        <form-table
          :formData="form"
          operateDataKey="familyCareer"
          :columns="familyCareerColumns"
        >
          <template #relation="{ row, column }">
            <ts-select
              style="width: 100%"
              v-model="row[column.property]"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of relationList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </template>

          <template #name="{ row, column }">
            <ts-input
              style="width: 100%"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #birthDay="{ row, column }">
            <el-date-picker
              style="width: 180px !important"
              value-format="yyyy-MM-dd"
              v-model="row[column.property]"
              type="date"
              placeholder="选择日期"
            />
          </template>

          <template #workUnit="{ row, column }">
            <ts-input
              style="width: 100%"
              v-model="row[column.property]"
            ></ts-input>
          </template>

          <template #iphone="{ row, column }">
            <ts-input
              style="width: 100%"
              v-model="row[column.property]"
            ></ts-input>
          </template>
        </form-table>
      </ts-row>

      <span class="group-title"> 8、其他情况 </span>

      <ul class="select-problem">
        <li>
          <div class="tips">你是否有亲友在本院工作？</div>

          <ts-radio-group v-model="form.isFriendWork">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>

          <ts-input
            class="details-input"
            v-model="form.isFriendWorkLabel"
            placeholder="请输入关系和姓名"
          />
        </li>

        <li>
          <div class="tips">你是否有重大疾病/手术记录？</div>

          <ts-radio-group v-model="form.isOperation">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>

          <ts-input
            class="details-input"
            v-model="form.isOperationLabel"
            placeholder="请说明"
          />
        </li>

        <li>
          <div class="tips">
            你是否有可能影响你完成所申请工作的健康缺陷或其他缺陷？
          </div>

          <ts-radio-group v-model="form.isHealthy">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>

          <ts-input
            class="details-input"
            v-model="form.isHealthyLabel"
            placeholder="请说明"
          />
        </li>

        <li>
          <div class="tips">
            你是否受到其它单位记过、察看、开除或其他严重处分？
          </div>

          <ts-radio-group v-model="form.isPunish">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>

          <ts-input
            class="details-input"
            v-model="form.isisPunishLabel"
            placeholder="请说明"
          />
        </li>

        <li>
          <div class="tips">你是否曾因触犯法律受到刑事处罚或治安处罚？</div>

          <ts-radio-group v-model="form.isLaw">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>

          <ts-input
            class="details-input"
            v-model="form.isLawLabel"
            placeholder="请说明"
          />
        </li>

        <li>
          <div class="tips">你从何处得知本招聘信息？</div>

          <ts-radio-group v-model="form.isGetInfo">
            <ts-radio label="报纸">报纸</ts-radio>
            <ts-radio label="网络">网络</ts-radio>
            <ts-radio label="亲友介绍">亲友介绍</ts-radio>
            <ts-radio label="招聘会">招聘会</ts-radio>
            <ts-radio label="其他">其他</ts-radio>
          </ts-radio-group>
        </li>

        <li>
          <div class="tips">
            是否接受公司以考察你个人素质为目的相关测试（结果保密）？
          </div>

          <ts-radio-group v-model="form.isTest">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>
        </li>

        <li>
          <div class="tips">是否接受工作调配？</div>

          <ts-radio-group v-model="form.isChange">
            <ts-radio label="是">是</ts-radio>
            <ts-radio label="否">否</ts-radio>
          </ts-radio-group>
        </li>
      </ul>
    </ts-form>

    <div class="bottom-btn-box" v-if="isSignUp">
      <ts-radio v-model="formRadio" label="1"
        >本人承诺所填信息真实合法
      </ts-radio>
      <ts-button type="primary" @click="formSubmitHandle">提交</ts-button>
      <ts-button @click="cancelSignUpHandle('ruleForm')">取消</ts-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import moment from 'moment';
import { userJKSignUpApi, editSignUpJk } from '@/api/recruitDetails';

import { city } from '@/config/area.js';
import { deepClone } from '@/utils/utils';

import cscjkDictionary from './cscjkDictionary';

import FormTable from '@/components/form-table.vue';
import FormUpload from '@/components/form-upload/index.vue';
import FileItem from './file-item';

import { getEducationList, getjobtitleList } from '@/api/index.js';

export default {
  mixins: [cscjkDictionary],
  components: {
    FormTable,
    FormUpload,
    FileItem,
  },
  props: {
    type: {
      type: String,
      default: () => 'signUp',
    },
    postId: {
      type: String,
      default: () => '',
    },
    planId: {
      type: String,
      default: () => '',
    },
    basicsInfo: {
      type: Object,
      default: () => {},
    },
    signUpData: {
      type: Object,
      default: () => {},
    },
    readonly: {
      type: Boolean,
      default: () => false,
    },
  },
  data: () => ({
    formRadio: false,
    educationList: [],
    jobTitleList: [],

    form: {
      name: '',
      gender: '',
      identityCard: '',

      educationName: '',
      educationId: '',
      jobtitleName: '',
      freshGraduate: '',
      politicalStatus: '',
      nationalityName: '',
      height: '',
      weight: '',
      birthLunar: '',
      isDrivingLicense: '',
      incumbency: '',
      monthlyPay: '',
      minimumMonthlyPay: '',
      fastestEntryDate: '',
      nativePlace: '',
      maritalStatus: '',
      children: '',
      foreignLanguageProficiency: '',
      region: [],
      positionDetails: '',
      address: [],
      addressDetails: '',
      iphone: '',
      email: '',
      contactNameAndIphone: '',
      hobby: '',

      studyCareer: [],
      workCareer: [],
      trainCareer: [],
      titleCareer: [],
      familyCareer: [],
    },

    rules: {
      required: { required: true, message: '必填' },
      iphone: [
        { required: true, message: '必填' },
        {
          trigger: ['blur', 'change'],
          message: '联系方式格式有误',
          validator: (prop, value, cb) => {
            let reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
            if (value && !reg.test(value)) {
              cb('false');
              return;
            }
            cb();
          },
        },
      ],
      email: {
        trigger: ['blur', 'change'],
        message: '请输入正确格式的邮箱',
        validator: (prop, value, cb) => {
          let reg =
            /^([a-zA-Z\d][\w-]{2,})@(\w{2,})\.([a-z]{2,})(\.[a-z]{2,})?$/;
          if (value && !reg.test(value)) {
            cb('false');
            return;
          }
          cb();
        },
      },
    },

    regionOption: deepClone(city),

    disabledDate: current => {
      return current && current <= moment().subtract(1, 'days');
    },

    studyCareerColumns: [
      {
        prop: 'stage',
        label: '阶段',
      },
      {
        prop: 'time',
        width: 250,
        label: '起止时间',
      },
      {
        prop: 'school',
        label: '毕业院校',
      },
      {
        prop: 'major',
        label: '专业',
      },
      {
        prop: 'degree',
        label: '学位',
      },
      {
        prop: 'fullTime',
        label: '全日制',
      },
      {
        prop: 'file',
        width: 180,
        defaultValue: [],
        label: '附件',
      },
    ],

    workCareerColumns: [
      {
        prop: 'time',
        width: 250,
        label: '起止时间',
      },
      {
        prop: 'companyName',
        label: '单位名称',
      },
      {
        prop: 'dept',
        label: '科室',
      },
      {
        prop: 'job',
        label: '职务',
      },
      {
        prop: 'pay',
        label: '薪资',
      },
      {
        prop: 'person',
        label: '证人/联系',
      },
      {
        prop: 'reasonForLeaving',
        label: '离职原因',
      },
    ],

    trainCareerColumns: [
      {
        prop: 'time',
        width: 250,
        label: '起止时间',
      },
      {
        prop: 'program',
        label: '培训项目',
      },
      {
        prop: 'institution',
        label: '培训机构',
      },
      {
        prop: 'teacher',
        label: '授课老师',
      },
      {
        prop: 'qualifying',
        label: '取得执业资格',
      },
      {
        prop: 'file',
        width: 180,
        defaultValue: [],

        label: '认证',
      },
    ],

    titleCareerColumns: [
      {
        prop: 'title',
        label: '职称',
      },
      {
        prop: 'titleProfessional',
        label: '职称的专业',
      },
      {
        prop: 'acquisitionTime',
        label: '取得时间',
      },
      {
        prop: 'locale',
        label: '职称取得地点',
      },
      {
        prop: 'file',
        width: 180,
        defaultValue: [],

        label: '附件',
      },
    ],

    familyCareerColumns: [
      {
        prop: 'relation',
        label: '关系',
      },
      {
        prop: 'name',
        label: '姓名',
      },
      {
        prop: 'birthDay',
        label: '出生日期',
      },
      {
        prop: 'workUnit',
        label: '工作单位',
      },
      {
        prop: 'iphone',
        label: '联系方式',
      },
    ],
  }),
  computed: {
    isSignUp() {
      return this.type === 'signUp';
    },
    isProgress() {
      return this.type === 'progress';
    },
    ...mapState(['registrationName', 'registrationIdentityCard']),
  },
  async created() {
    if (this.isSignUp) {
      this.form = {
        ...this.form,
        ...this.basicsInfo,
      };
    }

    if (this.isProgress) {
      this.form = this.signUpData;
    }

    const educationRes = await getEducationList();
    if (!educationRes.success) {
      this.$message.error(educationRes.message || '获取学历字典失败');
      return;
    }
    this.educationList = educationRes.object;

    const jobTitleRes = await getjobtitleList();
    if (!jobTitleRes.success) {
      this.$message.error(jobTitleRes.message || '获取职称字典失败');
      return;
    }
    this.jobTitleList = jobTitleRes.object;
  },
  methods: {
    handleEducationChange(e) {
      this.form.educationName = this.educationList.find(
        item => item.itemNameValue == e
      ).itemName;
    },
    async formSubmitHandle() {
      let _this = this;

      try {
        await _this.$refs.ruleForm.validate();

        if (_this.isSignUp && !_this.formRadio) {
          _this.$message({
            message: '请承诺所填信息真实合法!',
            center: true,
            type: 'warning',
          });
          return false;
        }
        const data = deepClone(this.form);

        data.postId = _this.postId;
        data.planId = _this.planId;

        let functionType = null;
        if (_this.isSignUp) {
          functionType = userJKSignUpApi;
        } else {
          functionType = editSignUpJk;
        }

        const result = await functionType(data);

        if (result.success && result.statusCode === 200) {
          // 成功提示信息
          let message = '恭喜您报名成功，可在”进度查询“中跟踪进度哦!';
          if (_this.isProgress) {
            message = '报名信息修改成功!';
          }
          _this.$message({
            type: 'success',
            center: true,
            message,
          });

          // 入口状态为报名 则切换至查看进度页面
          if (_this.isSignUp) {
            setTimeout(() => {
              _this.$emit('signUpSuccess');
            }, 1000);
          }

          return true;
        } else {
          _this.$message({
            type: 'warning',
            center: true,
            message: result.message,
          });
          return false;
        }
      } catch (error) {
        console.log(error);
        return false;
      }
    },

    cancelSignUpHandle(formName) {
      this.$emit('backPostLIST');
      this.$refs[formName].resetFields();
      this.$refs[formName].clearValidate();
    },

    print() {
      if (!this.readonly) {
        return false;
      }
      const PrintFormTable = document.getElementById('PrintFormTable');
      const SetFormTableForm = document.getElementById('SetFormTableForm');

      PrintFormTable.style.display = 'block';
      SetFormTableForm.style.display = 'none';
      this.$emitter.emit('startLoading');
      setTimeout(() => {
        this.$print(document.getElementById('FormBox'));
        PrintFormTable.style.display = 'none';
        SetFormTableForm.style.display = 'block';
        this.$emitter.emit('endLoading');
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
#FormBox {
  position: absolute;
  top: 0;
  bottom: 0;
  overflow-y: auto;
  width: 100%;
  padding-right: 8px;

  .table-data {
    width: 100%;
    height: 100%;
    border-spacing: 0;
    border-collapse: collapse;

    h1 {
      font-size: 1.6em;
      text-align: center;
      margin: 8px 0;
    }

    tr {
      width: 100%;

      > td {
        height: 40px;
        border: 1px solid #cecece;

        &:nth-child(2n-1) {
          width: 105px;
          color: #000;
          padding-right: 12px;
          text-align: center;
          line-height: 1;
        }

        &:nth-child(2n) {
          padding: 8px;
        }
      }

      &.title-tr {
        > td {
          color: #000;
          text-align: center;
        }
      }

      &.value-tr {
        > td {
          color: rgb(96, 98, 102);
          text-align: center;
        }
      }

      .title {
        font-weight: 700;
        letter-spacing: 3px;
        font-size: 16px;
      }
    }
  }

  .table-data tr > td {
    line-height: 16px !important;
  }

  h1 {
    text-align: center;
    font-size: 32px;
  }

  .ts-form {
    position: relative;
  }

  .group-title {
    display: block;
    font-size: 14px;
    color: 333;
    font-weight: 600;
    margin: 10px 0;
  }

  .width-select-90 {
    width: 90px;

    ::v-deep {
      .el-input {
        min-width: 90px;
      }
    }
  }

  .width-select-100 {
    width: 100px;

    ::v-deep {
      .el-input {
        min-width: 100px;
      }
    }
  }

  .width-input-100 {
    width: 100px;

    ::v-deep {
      &.el-input {
        min-width: 100px;
      }
    }
  }

  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }

  .flex-item {
    ::v-deep {
      .el-form-item__content {
        display: flex;
        align-items: center;
      }
    }
  }

  .required-icon {
    color: #f56c6c;
    margin-right: 4px;
  }

  .form-upload {
    margin-top: 10px;
  }

  .el-date-editor {
    width: 100% !important;

    ::v-deep {
      .el-range-separator {
        width: 12% !important;
      }
    }
  }

  .select-problem {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eee;

    li {
      border: 1px solid #eee;
      border-bottom: 1px solid transparent;
      height: 30px;
      display: flex;
      align-items: center;

      .tips {
        width: 450px;
        height: 30px;
        line-height: 30px;
        margin-bottom: 0px;
        border-right: 1px solid #eee;
        padding-left: 8px;
        font-size: 14px;
        color: #333333;
      }

      .el-radio-group {
        width: 380px;
        height: 100%;
        display: flex;
        align-items: center;
        border-right: 1px solid #eee;
        padding: 0 8px;

        .el-radio {
          margin-right: 15px;
        }
      }

      .details-input {
        width: 250px;

        ::v-deep {
          &.el-input {
            min-width: 250px;
          }

          .el-input__inner {
            border: 1px solid transparent;
            border-top: 1px solid #eee;
          }
        }
      }
    }
  }

  .bottom-btn-box {
    text-align: center;
    padding: 24px 0 0;
  }
}
</style>
