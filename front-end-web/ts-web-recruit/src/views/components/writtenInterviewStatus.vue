<template>
  <div>
    <div v-for="item in list" :key="item.postId">
      <div
        name="writtenInterviewStatus"
        class="written_item"
        v-if="item.status !== null && !item.exemptionStatus"
      >
        <div class="status_content">
          <div class="content_top">
            <h3>{{ item.postName }}</h3>
            <div class="right_top" v-if="item.time">
              <img src="@/assets/icon_prinit.png" alt="" />
              <span
                class="print-ticket"
                @click="printTicketHandle(item.postId)"
              >
                打印准考证
              </span>
            </div>
          </div>
          <div :id="'printTicketTable' + item.postId" class="printTable">
            <h1 class="printTableTitle">准考证</h1>
            <table class="status_info_table" cellpadding="0" cellspacing="0">
              <tr>
                <td style="width: 110px" class="table_label">姓名</td>
                <td style="width: 190px">{{ basicInfo.name }}</td>
                <td style="width: 110px" class="table_label">性别</td>
                <td style="width: 190px">{{ basicInfo.gender }}</td>
              </tr>
              <tr>
                <td class="table_label">身份证号码</td>
                <td>{{ basicInfo.identityCard }}</td>
                <td class="table_label">报考岗位</td>
                <td>{{ item.postName }}</td>
              </tr>
              <tr>
                <td class="table_label">考试地点</td>
                <td>{{ item.addr }}</td>
                <td class="table_label">
                  {{ type === '笔试' ? '座位号' : '顺序' }}
                </td>
                <td>{{ item.seatNo }}</td>
              </tr>
              <tr>
                <td class="table_label">考试时间</td>
                <td colspan="3">{{ item.time }}</td>
              </tr>
              <tr>
                <td class="table_label">考试内容</td>
                <td colspan="3">{{ item.content }}</td>
              </tr>
            </table>
          </div>
        </div>
        <div class="status-view">
          <img :src="item.imgUrl" alt="" />
          <p class="status-tips" :class="item.className">
            {{ item.tips }}
          </p>
        </div>
      </div>
      <div
        class="written_item written_interview"
        v-else-if="item.exemptionStatus === 2"
      >
        <h3>{{ item.postName }}</h3>
        <p>您已获得该岗位{{ type }}免试资格</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    basicInfo: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: () => '',
    },
  },
  created() {},
  methods: {
    printTicketHandle(id) {
      const table = document.getElementById(`printTicketTable${id}`);
      table.style.margin = '10px auto 0';
      table.childNodes[0].style.display = 'block';

      this.$emitter.emit('startLoading');
      setTimeout(() => {
        this.$print(table);
        table.style.margin = '0';
        table.childNodes[0].style.display = 'none';
        this.$emitter.emit('endLoading');
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.printTable {
  width: 600px;

  .status_info_table {
    width: 100%;
    border-right: 1px solid #333;
    border-bottom: 1px solid #333;

    .table_label {
      background: #f4f4f4;
    }

    tr {
      height: 40px;
      text-align: center;
    }

    td {
      border-left: 1px solid #333;
      border-top: 1px solid #333;
      color: #333;
    }
  }

  .printTableTitle {
    text-align: center;
    display: none;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    line-height: 21px;
    margin-bottom: 16px;
  }
}

.written_item {
  width: 940px;
  display: flex;
  padding: 8px 12px 16px;
  margin-bottom: 12px;
  background: #fafafa;
  border: 1px solid #e7ebf0;
  border-radius: 4px;

  .status_content {
    width: 600px;

    .content_top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right_top {
        display: flex;
        align-items: center;

        .print-ticket {
          font-size: 12px;
          font-weight: 400;
          color: #5260ff;
          line-height: 17px;
          cursor: pointer;
        }

        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }

      h3 {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        line-height: 21px;
        margin-bottom: 16px;
      }
    }
  }

  &.written_interview {
    flex-direction: column;

    > h3 {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    > p {
      color: rgb(103, 194, 58);
    }
  }

  .status-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 140px;
      height: 140px;
    }

    p {
      text-align: center;
      font-size: 12px;
      color: #666666;
      line-height: 16px;
    }

    .status-tips {
      margin-top: 8px;

      &.pass {
        font-size: 16px;
        color: #d13333;
        line-height: 21px;
      }
    }

    .color_pass {
      border-color: #e24242 !important;
      color: #e24242 !important;
    }

    .color_fail {
      border-color: #999999 !important;
      color: #999999 !important;
    }
  }
}
</style>
