<template>
  <ts-dialog
    class="dialog-contract-management-details"
    fullscreen
    title="基本信息"
    :visible.sync="visible"
    @close="close"
  >
    <div class="content">
      <ts-tabs v-model="tabs">
        <ts-tab-pane label="基本信息" name="1"></ts-tab-pane>
        <ts-tab-pane label="查看合同文件" name="2"></ts-tab-pane>
        <ts-tab-pane label="变更记录" name="3"></ts-tab-pane>
        <ts-tab-pane label="收票记录" name="4"></ts-tab-pane>
        <ts-tab-pane label="付款记录" name="5"></ts-tab-pane>
        <ts-tab-pane label="风险信息" name="6"></ts-tab-pane>
        <ts-tab-pane label="借阅记录" name="7"></ts-tab-pane>
      </ts-tabs>
      <div class="tabs-content">
        {{ tabs }}
        <table-ticket-receipt v-if="tabs === '4'" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import TableTicketReceipt from '../components/table-ticket-receipt.vue';
export default {
  components: {
    TableTicketReceipt
  },
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean,
      default: () => false
    }
  },
  data: function() {
    return {
      visible: false,
      tabs: '1'
    };
  },
  watch: {
    show: {
      async handler(val) {
        this.visible = val;
      }
    }
  },
  computed: {},
  methods: {
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-contract-management-details {
  ::v-deep {
    .el-dialog__body {
      width: 1280px;
      height: 100%;
      .content {
        height: 100%;
        .tabs-content {
          width: 100%;
          height: calc(100% - 44px);
        }
      }
    }
  }
}
</style>
