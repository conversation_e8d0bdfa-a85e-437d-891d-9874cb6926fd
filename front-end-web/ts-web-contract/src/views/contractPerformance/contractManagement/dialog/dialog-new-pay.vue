<template>
  <ts-dialog
    class="dialog-new-pay"
    fullscreen
    title="新建付款"
    :visible.sync="visible"
    @close="close"
  >
    <div class="content">
      <ts-tabs v-model="tabs">
        <ts-tab-pane label="流程表单" name="1"></ts-tab-pane>
        <ts-tab-pane label="流程信息" name="2"></ts-tab-pane>
        <ts-tab-pane label="签字意见" name="3"></ts-tab-pane>
      </ts-tabs>
      <div class="tabs-content">
        <FormNewPay
          ref="FormNewPay"
          v-if="tabs === '1'"
          :visible="visible"
          :newPayInfo="newPayInfo"
        />
        <process-info :searchData="searchData" v-if="tabs === '2'" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="back">退 回</ts-button>
      <ts-button @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <!-- 流程发起或者同意 -->
    <dialog-associated-process
      v-model="payDialogAssociatedProcess"
      :processNeedIdObject="processNeedIdObject"
      :submitCallBack="payProcessSubmitCallBack"
    />
  </ts-dialog>
</template>

<script>
import api from '@/api/ajax/api-contract-performance.js';
import FormNewPay from '../components/form-new-pay.vue';
import DialogAssociatedProcess from '@/components/process-components/dialog-associated-process.vue';
import ProcessInfo from '@/components/process-components/process-info.vue';

export default {
  components: {
    FormNewPay,
    ProcessInfo,
    DialogAssociatedProcess
  },
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean,
      default: () => false
    },
    newPayInfo: {
      type: Object,
      default: () => {}
    }
  },
  data: function() {
    return {
      visible: false,
      tabs: '1',
      payFormData: null,

      payDialogAssociatedProcess: false,
      processNeedIdObject: {},
      searchData: {}
    };
  },
  watch: {
    show: {
      async handler(val) {
        this.visible = val;
      }
    },
    tabs: {
      async handler(val) {
        if (val === '2') {
          const wfDefinitionId = await this.handleGetPayProcessId();
          this.searchData.definitionId = wfDefinitionId;
        }
      }
    }
  },
  computed: {},
  methods: {
    async handleGetPayProcessId() {
      const res = await api.selectWfDefinition();
      if (res.success && res.statusCode === 200) {
        return res.object.WF_DEFINITION_ID;
      } else {
        this.$message.error(res.message || '获取付款流程信息失败!');
        return false;
      }
    },
    back() {},
    async submit() {
      const formSubmit = await this.$refs.FormNewPay.submit();
      if (formSubmit === 'validate fail') {
        return false;
      }

      this.payFormData = formSubmit;

      const wfDefinitionId = await this.handleGetPayProcessId();
      if (wfDefinitionId) {
        this.processNeedIdObject.wfDefinitionId = wfDefinitionId;
        this.payDialogAssociatedProcess = true;
      }
    },
    async payProcessSubmitCallBack(processForm, endStepName) {
      let data = {
        ...processForm,
        ...this.payFormData,
        status: '1'
      };

      if (endStepName) data.status = '2';

      const submitRes = await api.contractRecordPaymentSave(data);
      if (submitRes.success && submitRes.statusCode === 200) {
        this.$message.success('操作成功!');
        this.payDialogAssociatedProcess = false;
        this.close();
      } else {
        this.$message.error(submitRes.message || '操作失败!');
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-new-pay {
  ::v-deep {
    .el-dialog__body {
      width: 1280px;
      height: 100%;
      .content {
        height: 100%;
        .tabs-content {
          width: 100%;
          height: calc(100% - 44px);
        }
      }
    }
  }
}
</style>
