<template>
  <ts-dialog
    class="dialog-open-version"
    width="700px"
    title="版本信息"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <base-table
        ref="table"
        :columns="columns"
        class="table-content"
        :hasPage="false"
      >
        <template #action="{row}">
          <span class="operation-span" @click="handlePreview(row)">预览</span>
          <span class="operation-span" @click="handleDownload(row)">下载</span>
        </template>
      </base-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import api from '@/api/ajax/api-creation.js';
import { isDoc, viewerDocBase } from '@/util/file.js';
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: <PERSON><PERSON><PERSON>
    },
    recordId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '流程节点',
          prop: 'stepName',
          align: 'center'
        },
        {
          label: '操作人',
          prop: 'createUserName',
          align: 'center'
        },
        {
          label: '操作时间',
          prop: 'createDate',
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 140,
          columnSlots: 'action'
        }
      ]
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.handleRefreshTable();
        }
        this.visible = val;
      }
    }
  },
  methods: {
    handleRefreshTable() {
      api
        .selectContractRecordFileList({ recordId: this.recordId })
        .then(res => {
          if (res.statusCode === 200 && res.success) {
            let rows = res.object;
            this.$refs.table.refresh({ rows });
          }
        });
    },
    // 预览
    handlePreview(file) {
      if (isDoc(file.fileName)) {
        viewerDocBase(
          `http://127.0.0.1:9019/ts-oa/api/contractRecordFile/downloadFile/${file.id}`,
          file.fileName
        );
      }
    },
    // 下载
    handleDownload(file) {
      const { id } = file;
      let a = document.createElement('a');
      a.href = `/ts-oa/api/contractRecordFile/downloadFile/${id}`;
      a.click();
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-open-version {
  .operation-span {
    color: $primary-blue;
    margin-right: 8px;
    cursor: pointer;
  }
}
</style>
