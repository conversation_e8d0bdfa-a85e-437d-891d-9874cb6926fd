<template>
  <div class="sign-opinion-box">
    <base-table
      ref="table"
      :columns="columns"
      class="table-content"
      :hasPage="false"
    />
  </div>
</template>

<script>
import api from '@/api/ajax/api-creation.js';
export default {
  props: {
    typeContractData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '流程节点',
          prop: 'wfStepName',
          align: 'center'
        },
        {
          label: '操作人',
          prop: 'createUserName',
          align: 'center'
        },
        {
          label: '签字意见',
          prop: 'remark',
          align: 'center'
        },
        {
          label: '审批时间',
          prop: 'createDate',
          align: 'center'
        }
      ]
    };
  },
  created() {
    this.handleRefreshTable();
  },
  methods: {
    handleRefreshTable() {
      let params = {
        wfInstId: this.typeContractData.wfInstanceId,
        sidx: 'finished_date',
        sord: 'desc',
        pageNo: 1,
        pageSize: 100
      };
      api.workflowTaskHisList(params).then(res => {
        let rows = res.rows.map(item => {
          if (!item.remark && item.wfStepName !== '【开始】') {
            item.createDate = '';
          }
          return item;
        });
        this.$refs.table.refresh({ rows });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.sign-opinion-box {
  height: 500px;
  overflow: auto;
  padding: 0 8px;
  .table-content {
    overflow: hidden;
  }
}
</style>
