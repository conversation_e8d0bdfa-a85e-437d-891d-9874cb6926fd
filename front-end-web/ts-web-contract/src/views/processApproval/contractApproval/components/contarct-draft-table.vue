<template>
  <div class="table-box">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :actions="[]"
      @search="getTableData"
    >
    </ts-search-bar>
    <base-table
      ref="table"
      v-loading="tableLoading"
      :columns="columns"
      class="table-content"
      @refresh="getTableData"
    />
  </div>
</template>

<script>
import api from '@/api/ajax/api-process-approval.js';
export default {
  props: {
    processTabsStatus: {
      type: String
    }
  },
  data() {
    return {
      tableLoading: false,
      searchForm: {},
      searchList: [
        {
          label: '合同名称',
          value: 'contractName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入合同名称'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '合同名称',
          prop: 'contract_name',
          align: 'center',
          minWidth: 160,
          formatter: (row, prop, cell) => {
            let className = row.enabled === '1' ? 'primary-span' : 'error-span';
            return this.$createElement(
              'span',
              {
                class: 'jump-span',
                on: {
                  click: () => this.handleShowDetails(row)
                }
              },
              cell
            );
          }
        },
        {
          label: '合同状态',
          prop: 'instanceStatusName',
          minWidth: 120,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    async getTableData(pageData = { pageNo: 1, pageSize: 100 }) {
      this.tableLoading = true;
      const res = await api.contractRecordList({
        ...pageData,
        status: this.processTabsStatus
      });
      this.tableLoading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.table-box {
  .jump-span {
    cursor: pointer;
    color: $primary-blue;
  }
}
</style>
