<template>
  <div class="trasen-container flex-column">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :actions="actions"
      @search="refresh"
    >
    </ts-search-bar>
    <base-table
      ref="table"
      :columns="columns"
      class="table-content"
      @refresh="handleRefreshTable"
    >
      <template slot="action">
        <i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>
      </template>
    </base-table>

    <ts-dialog :visible.sync="showEdit" :title="title" :fullscreen="true">
      <div
        style="height: calc(100vh - 140px); overflow: hidden;"
        v-if="editTab != 'add'"
      >
        <ts-tabs :type="null" v-model="editTab" class="edit-tab flex-column">
          <ts-tab-pane label="基本信息" name="basic">
            <div style="height: 100%; overflow: hidden;">
              <BaseInformBoard ref="baseBoard" :data="editData" />
            </div>
          </ts-tab-pane>
          <ts-tab-pane label="历史合同信息" name="history">
            <HistoryContractBoard ref="historyBoard" />
          </ts-tab-pane>
        </ts-tabs>
      </div>
      <div v-else style="height: calc(100vh - 140px); overflow: hidden;">
        <BaseInformBoard ref="baseBoard" :data="editData" />
      </div>
      <div slot="footer">
        <ts-button @click="handleCloseEditModal">取消</ts-button>
        <ts-button type="primary" @click="handleSaveEdit">提交</ts-button>
      </div>
    </ts-dialog>
  </div>
</template>

<script>
import js from './index';
import BaseInformBoard from './components/base-inform-board.vue';
import HistoryContractBoard from './components/historical-contract.vue';

export default {
  mixins: [js],
  components: {
    BaseInformBoard,
    HistoryContractBoard
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '相对方名称',
          value: 'partnersName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入相对方名称'
          }
        },
        {
          label: '负责人名称',
          value: 'partnersPrincipal',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入负责人名称'
          }
        },
        {
          label: '相对方行业',
          value: 'partnersIndustry',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入相对方行业'
          }
        }
      ],
      actions: [
        {
          label: '新增',
          prop: { type: 'primary' },
          click: this.handleOpenEditModal
        },
        {
          label: '导出',
          click: this.handleExport
        }
      ],

      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60
        },
        {
          label: '相对方名称',
          align: 'center',
          prop: 'partnersName'
        },
        {
          label: '相对方负责人',
          align: 'center',
          prop: 'partnersPrincipal'
        },
        {
          label: '相对方类型',
          align: 'center',
          prop: 'partnersType'
        },
        {
          label: '相对方行业',
          align: 'center',
          prop: 'partnersIndustry'
        },
        {
          label: '相对方级别',
          align: 'center',
          prop: 'partnersLevel'
        },
        {
          label: '所在地区',
          align: 'center',
          prop: 'partnersAddress'
        },
        {
          headerSlots: 'action',
          minWidth: 40,
          resizable: false,
          align: 'center',
          formatter: (row, opt, cell) => {
            let btnList = [
              {
                label: '编辑',
                event: 'edit',
                icon: 'fa fa-pencil-square-o'
              },
              {
                label: '删除',
                event: 'delete',
                icon: 'fa fa-trash-o'
              }
            ];
            return (
              <BaseActionCell
                actions={btnList}
                on={{ 'action-select': value => this.action(value, row) }}
              />
            );
          }
        }
      ],

      editTab: 'basic'
    };
  }
};
</script>

<style lang="scss" scoped>
.table-content {
  flex: 1;
  overflow: hidden;
  margin-bottom: $primary-spacing;
}
/deep/ .el-tabs__item.is-active {
  border: none;
}
/deep/ .el-tabs__item {
  border: none;
}
.edit-tab {
  height: 100%;
}
/deep/ .el-tabs__content {
  flex: 1;
}
/deep/ .el-tab-pane {
  height: 100%;
  overflow: hidden;
}
</style>
