<template>
  <div style="height: 100%; overflow: hidden;">
    <el-scrollbar style="height: 100%" wrapStyle="overflow-x:hidden;">
      <ts-form ref="form1" :model="formData" :rules="rules">
        <div class="inform-title">基本信息</div>
        <ts-row>
          <ts-col :span="8">
            <ts-form-item label="相对方名称" prop="partnersName">
              <ts-input
                v-model="formData.partnersName"
                placeholder="请输入相对方名称"
                maxlength="20"
                clearable
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="8">
            <ts-form-item label="相对方编号">
              <ts-input
                v-model="formData.partnersNumber"
                maxlength="20"
                clearable
                placeholder="请输入相对方编号"
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="8">
            <ts-form-item label="相对方简称">
              <ts-input
                v-model="formData.partnersAbbreviation"
                maxlength="20"
                clearable
                placeholder="请输入相对方简称"
              ></ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="8">
            <ts-form-item label="相对方级别">
              <ts-select
                v-model="formData.partnersLevel"
                placeholder="请选择相对方级别"
                style="width: 100%"
              >
                <ts-option
                  v-for="item in levelList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="8">
            <ts-form-item label="相对方类型">
              <ts-select
                v-model="formData.partnersType"
                placeholder="请选择相对方级别"
                style="width: 100%"
              >
                <ts-option
                  v-for="item in typeList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <div class="inform-title">详细信息</div>
        <ts-row>
          <ts-col :span="8">
            <ts-form-item label="相对方行业" prop="name">
              <ts-select
                v-model="formData.partnersIndustry"
                placeholder="请选择相对方级别"
                style="width: 100%"
              >
                <ts-option
                  v-for="item in industryList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="8"><div style="height: 5px;"></div></ts-col>
          <ts-col :span="8">
            <ts-form-item label="网址">
              <ts-input
                v-model="formData.partnersWebsite"
                maxlength="20"
                clearable
                placeholder="请输入网址"
              ></ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="所在地区">
          <ts-input
            v-model="formData.partnersAddress"
            maxlength="50"
            clearable
            placeholder="请输入相对方编号"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="相对方简介">
          <ts-input
            v-model="formData.partnersProfile"
            placeholder="请输入相对方简介"
            type="textarea"
            maxlength="500"
            :rows="6"
            show-word-limit
            resize="none"
          ></ts-input>
        </ts-form-item>
      </ts-form>

      <ts-form ref="form2" :model="formData" :inline="true" :rules="rules">
        <ts-form-item label="法定代表">
          <ts-input
            v-model="formData.partnersLegal"
            maxlength="10"
            clearable
            placeholder="请输入法定代表"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="注册信息">
          <ts-input
            v-model="formData.partnersRegister"
            maxlength="15"
            clearable
            placeholder="请输入注册信息"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="组织机构编号">
          <ts-input
            v-model="formData.partnersOrgNumber"
            maxlength="18"
            clearable
            placeholder="请输入组织机构编号"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="单位电话" prop="partnersPhone">
          <ts-input
            v-model="formData.partnersPhone"
            maxlength="11"
            @input="
              (value) => (formData.phone = (value.match(/\d+/g) || [''])[0])
            "
            clearable
            placeholder="请输入单位电话"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="单位邮箱" prop="partnersEmail">
          <ts-input
            v-model="formData.partnersEmail"
            maxlength="10"
            clearable
            placeholder="请输入单位邮箱"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="负责人" prop="partnersPrincipal">
          <ts-input
            v-model="formData.partnersPrincipal"
            maxlength="10"
            clearable
            placeholder="请输入负责人"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="手机号码" prop="partnersPrincipalPhone">
          <ts-input
            v-model="formData.partnersPrincipalPhone"
            maxlength="11"
            clearable
            placeholder="请输入负责人手机号码"
          ></ts-input>
        </ts-form-item>

        <div class="flex">
          <ts-form-item label="企业资质附件"></ts-form-item>
          <div class="flex-grow">
            <ts-upload
              ref="tsUpload"
              action="/ts-basics-bottom/fileAttachment/upload?moduleName=contract"
              :fileList.sync="fileList"
              :show-file-list="false"
              :http-request="handleUploadFile"
            >
              <ts-button>文件上传</ts-button>
              <div slot="tip" class="el-upload__tip">
                只能上传jpg/png文件，且不超过500kb
              </div>
            </ts-upload>
            <ts-upload-file-list
              :fileList.sync="fileList"
              :on-remove="onRemove"
              :on-abort="onAbort"
              :on-upload="onUpload"
              :onPreview="onPreview"
              type="mixture"
            ></ts-upload-file-list>
          </div>
        </div>

        <div class="inform-title">财务信息</div>

        <ts-form-item label="结算方式">
          <ts-select
            v-model="formData.partnersPayment"
            placeholder="请选择结算方式"
            style="width: 100%"
          >
            <ts-option
              v-for="item in settlementList"
              :key="item"
              :label="item"
              :value="item"
            >
            </ts-option>
          </ts-select>
        </ts-form-item>

        <ts-form-item label="开户行" prop="partnersBank">
          <ts-input
            v-model="formData.partnersBank"
            maxlength="11"
            clearable
            placeholder="请输入开户行"
          ></ts-input>
        </ts-form-item>

        <ts-form-item label="账号" prop="partnersAccount">
          <ts-input
            v-model="formData.partnersAccount"
            maxlength="15"
            clearable
            @input="
              (value) =>
                (formData.partnersAccount = (value.match(/\d+/g) || [''])[0])
            "
            placeholder="请输入账号"
          ></ts-input>
        </ts-form-item>
      </ts-form>
      <div class="inform-title">联系人信息</div>
      <div class="table-data-action-line">
        <div class="table-action add" @click="partnersLinkmanList.push({})">
          <i class="el-icon-plus"></i>
        </div>
        <div class="table-action delete" @click="handleDeleteConcatTableData">
          <i class="el-icon-minus"></i>
        </div>
      </div>
      <ts-table
        ref="contactTable"
        border
        stripe
        :columns="columns"
        :data="partnersLinkmanList"
        highlight-current-row
      >
      </ts-table>
    </el-scrollbar>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="[previewFile]"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import { guid, isDoc, viewerDocBase } from '@/util/file.js';
import cPartApi from '@/api/ajax/contract-counterpart.js';
import fileApi from '@/api/ajax/file.js';

export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      businessId: '',

      formData: {},
      fileList: [],
      previewFile: '',
      rules: {
        partnersName: [
          { required: true, message: '请输入相对方名称', trigger: 'blur' },
        ],
        partnersPhone: [
          {
            trigger: 'blur',
            message: '请输入正确格式的手机号',
            validator: (prop, value, cb) => {
              let reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
              if (value && !reg.test(value)) {
                cb('false');
                return;
              }
              cb();
            },
          },
        ],
        partnersPrincipal: [
          { required: true, message: '请输入负责人', trigger: 'blur' },
        ],
        partnersPrincipalPhone: [
          { required: true, message: '请输入负责人手机号码', trigger: 'blur' },
          {
            trigger: 'blur',
            message: '请输入正确格式的手机号',
            validator: (prop, value, cb) => {
              let reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
              if (value && !reg.test(value)) {
                cb('false');
                return;
              }
              cb();
            },
          },
        ],
        partnersBank: [
          { required: true, message: '请输入开户行', trigger: 'blur' },
        ],
        partnersAccount: [
          { required: true, message: '请输入开户行账号', trigger: 'blur' },
        ],
        partnersEmail: [
          {
            trigger: 'blur',
            message: '请输入正确格式的邮箱',
            validator: (prop, value, cb) => {
              let reg = /1+@[a-zA-Z0-9_-]+(.[a-zA-Z0-9_-]+)+$/;
              if (value && !reg.test(value)) {
                cb('false');
                return;
              }
              cb();
            },
          },
        ],
      },

      levelList: ['A级', 'B级', 'C级'],
      typeList: ['供应商', '客户', '合作伙伴', '政府单位'],
      industryList: [
        '采购类',
        '工程类',
        '合作类',
        '服务外包类',
        '租赁类',
        '代理类',
      ],
      settlementList: ['银行转账', '电汇', '现金结算'],

      partnersLinkmanList: [],
      columns: [
        {
          type: 'selection',
          width: 36,
        },
        {
          type: 'index',
          label: '序号',
          width: 60,
          align: 'center',
        },
        {
          label: '姓名',
          formatter: (row) => {
            return (
              <ElInput
                vModel={row.name}
                placeholder="请输入姓名"
                maxlength="10"
                class="table-inline-input"
              ></ElInput>
            );
          },
        },
        {
          label: '岗位',
          formatter: (row) => {
            return (
              <ElInput
                vModel={row.jobs}
                placeholder="请输入岗位名称"
                class="table-inline-input"
                maxlength="10"
              ></ElInput>
            );
          },
        },
        {
          label: '部门',
          formatter: (row) => {
            return (
              <ElInput
                vModel={row.deptName}
                placeholder="请输入部门名称"
                class="table-inline-input"
                maxlength="10"
              ></ElInput>
            );
          },
        },
        {
          label: '联系方式',
          formatter: (row) => {
            return (
              <ElInput
                vModel={row.phone}
                placeholder="请输入联系方式"
                class="table-inline-input"
                onInput={(value) =>
                  (row.phone = (value.match(/\d+/g) || [''])[0])
                }
                maxlength="11"
              ></ElInput>
            );
          },
        },
        {
          label: '邮箱',
          formatter: (row) => {
            return (
              <ElInput
                vModel={row.email}
                class="table-inline-input"
                placeholder="请输入邮箱名称"
              ></ElInput>
            );
          },
        },
      ],
    };
  },
  watch: {
    data: {
      handler(val) {
        this.getContractDetail(val);
      },
      immediate: true,
    },
  },
  methods: {
    /**@desc 获取 相对方详情 */
    async getContractDetail(val) {
      let detail = val || {};
      if (val.id) {
        let detailRes = await cPartApi.getContractCounterpartDetail(val.id);
        detail = detailRes.success ? detailRes.object || {} : val;
      }

      if (val.partnersFile) {
        this.businessId = val.partnersFile;
        this.getFileList();
      } else {
        this.businessId = guid();
      }

      this.formData = Object.assign(
        {
          partnersLevel: this.levelList[0],
          partnersType: this.typeList[0],
          partnersIndustry: this.industryList[0],
          partnersPayment: this.settlementList[0],
        },
        detail
      );

      this.partnersLinkmanList = detail.partnersLinkmanList || [];
    },
    /**@desc 数据校验 */
    async validate() {
      let formResList = await Promise.all([
        this.$refs.form1.validate().catch((res) => res),
        this.$refs.form2.validate().catch((res) => res),
      ]);
      if (!(formResList[0] && formResList[1])) {
        return false;
      }

      let failRow = this.partnersLinkmanList.find((item) => {
        if (!item.name || !item.phone) {
          this.$message.error(
            item.name ? '联系方式不能为空' : '联系人姓名不能为空'
          );
          return true;
        }

        let phoneTest = /1[^1-2]\d{9}/g;
        if (!phoneTest.test(item.phone)) {
          this.$message.error('请输入正确的手机格式');
          return true;
        }

        let emailTest = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
        if (item.email && !emailTest.test(item.email)) {
          this.$message.error('请输入正确的邮箱');
          return true;
        }
        return false;
      });

      if (failRow) {
        return false;
      }

      return {
        ...this.formData,
        partnersFile: this.businessId,
        partnersLinkmanList: this.partnersLinkmanList,
      };
    },
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      fileApi
        .getFileAttachmentByBusinessId({
          businessId: this.businessId,
        })
        .then((res) => {
          this.fileList = res.object.map((item) => ({
            ...item,
            fileName: `${item.id}.${item.fileExtension}`,
            fileRealName: item.originalName,
            fileId: item.id,
            uid: item.id,
            url: item.realPath,
            name: item.originalName,
            status: 'success',
          }));
        });
    },
    /**@desc 替换原生上传 */
    handleUploadFile(params) {
      let data = new FormData();
      data.append('file', params.file);
      data.append('businessId', this.businessId);
      fileApi.uploadFile(data).then((res) => {
        if (res.success) {
          this.fileList.push({
            ...res.object[0],
            realPath: res.object[0].filePath,
            url: res.object[0].filePath,
            uid: res.object[0].fileId,
            name: res.object[0].fileRealName,
          });
        } else {
          this.$message.error(res.message || '上传失败');
        }
      });
    },
    onRemove(file) {
      let idx = this.fileList.findIndex((e) => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      fileApi.deleteFile(deleteFile.id).then((res) => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex((e) => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = file.url;
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (isDoc(file.fileName)) {
        viewerDocBase(file.realPath, file.fileName);
      } else {
        this.previewFile = location.origin + file.url;
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    },
    /**@desc 删除表格数据 */
    handleDeleteConcatTableData() {
      let deleteRows = this.$refs.contactTable.$children[0].selection;
      if (deleteRows.length == this.partnersLinkmanList.length) {
        this.partnersLinkmanList = [];
        return;
      }
      deleteRows.map((item) => {
        let deleteRowIndex = this.partnersLinkmanList.findIndex(
          (row) => row == item
        );
        if (deleteRowIndex >= 0) {
          this.partnersLinkmanList.splice(deleteRowIndex, 1);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.inform-title {
  display: flex;
  align-items: center;
  font-size: $seconde-title-text-size;
  &::before {
    content: '';
    height: 18px;
    width: 4px;
    border-radius: 6px;
    background-color: $primary-blue;
    margin-right: $primary-spacing;
  }
}
/deep/ textarea.el-textarea__inner:focus {
  border-color: $primary-blue;
}
.table-data-action-line {
  text-align: right;
  margin: $primary-spacing 0;
  .table-action {
    display: inline-block;
    border: 1px solid $primary-blue;
    border-radius: 2px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    margin-right: $primary-spacing;
    line-height: 24px;
    text-align: center;
    * {
      color: $primary-blue;
      font-size: 16px;
    }
    &:hover {
      opacity: 0.8;
    }
    &:active {
      border-color: mix($primary-blue, #000, 80%);
      opacity: 1;
      * {
        color: mix($primary-blue, #000, 80%);
      }
    }
    &.delete {
      border-color: $split-line-color;
      * {
        color: #333;
      }

      &:active {
        border-color: mix($split-line-color, #000, 80%);
        opacity: 1;
        * {
          color: #000;
        }
      }
    }
  }
}
.table-inline-input {
  margin: $primary-spacing 0;
}
</style>
