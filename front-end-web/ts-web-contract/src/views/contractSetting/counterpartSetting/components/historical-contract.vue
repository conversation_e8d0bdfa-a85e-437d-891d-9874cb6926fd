<template>
  <div style="height: 100%; overflow: hidden;" class="flex-column">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
    >
    </ts-search-bar>
    <base-table
      ref="table"
      :columns="columns"
      class="table-content"
      @refresh="handleRefreshTable"
    >
    </base-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '合同编号',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入合同编号'
          }
        },
        {
          label: '合同名称',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入合同名称'
          }
        }
      ],

      columns: [
        {
          label: '序号',
          prop: 'pageRowIndex'
        },
        {
          label: '合同编号',
          prop: '',
          align: 'center'
        },
        {
          label: '合同名称',
          prop: ''
        },
        {
          label: '签订日期',
          prop: ''
        },
        {
          label: '负责人',
          prop: ''
        },
        {
          label: '签约主体',
          prop: ''
        },
        {
          label: '合同金额',
          prop: '',
          align: 'right'
        },
        {
          label: '合同状态',
          prop: ''
        }
      ]
    };
  },
  methods: {
    refresh() {
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(pageData = { pageNo: 1, pageSize: 100 }) {}
  }
};
</script>

<style lang="scss" scoped>
/deep/.el-table__body-wrapper.is-scrolling-none {
  margin-top: 31px !important;
}
</style>
