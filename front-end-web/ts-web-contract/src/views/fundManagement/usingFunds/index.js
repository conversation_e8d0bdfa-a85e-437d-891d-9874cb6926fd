export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          value: 'condition',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '项目名称/项目负责人'
          }
        },
        {
          label: '经费类型',
          value: 'fundType'
        }
      ],
      fundTypeTreeData: [],

      activeFundsType: '1',
      waitingHandleColumns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60
        },
        {
          label: '项目编号',
          prop: 'itemCode'
        },
        {
          label: '项目名称',
          prop: 'itemName',
          minWidth: 440,
          formatter: (row, prop, cell) => {
            return (
              <span
                class="action-cell"
                onclick={this.handleShowPreview.bind(this, row)}>
                {cell}
              </span>
            );
          }
        },
        {
          label: '经费类型',
          prop: 'fundTypeName',
          minWidth: 120
        },
        {
          label: '项目负责人',
          prop: 'leaderUserName',
          minWidth: 120
        },
        {
          label: '科室',
          prop: 'deptName',
          minWidth: 180
        },
        {
          label: '状态',
          prop: 'status',
          minWidth: 140,
          formatter: (row, prop, cell) => {
            return [
              '待提交',
              '预算待审批',
              '预算审批完成待报销',
              '预算变更待审批',
              '预算变更完成',
              '报销待审批',
              '报销已完成',
              '预算驳回',
              '变更驳回',
              '报销驳回'
            ][cell];
          }
        },
        {
          label: '核定项目经费(元)',
          prop: 'approvedItemFund',
          align: 'right',
          minWidth: 150
        },
        {
          label: '医院配套经费(元)',
          prop: 'supportingFund',
          align: 'right',
          minWidth: 150
        },
        {
          label: '进度',
          minWidth: 180,
          formatter: row => {
            let {
                approvedItemFund = 0,
                supportingFund = 0,
                itemActualAmount = 0
              } = row,
              percent = itemActualAmount / (approvedItemFund + supportingFund);
            percent = percent * 100;
            percent = parseFloat(percent?.toFixed(2));
            return (
              <TsProgress
                class={percent > 100 ? 'full-percent' : ''}
                percentage={percent}></TsProgress>
            );
          }
        },
        {
          label: '预算金额(元)',
          prop: 'budgetAmount',
          align: 'right',
          minWidth: 150
        },
        {
          label: '实际执行金额(元)',
          prop: 'actualAmount',
          align: 'right',
          minWidth: 150
        },
        {
          label: '预算执行率',
          minWidth: 120,
          align: 'right',
          formatter: row => {
            let { actualAmount = 0, budgetAmount } = row,
              percent = (
                ((actualAmount * 100) / (budgetAmount * 100)) *
                100
              ).toFixed(2);

            return (
              <span class={percent > 100 ? 'error-span' : ''}>{percent}%</span>
            );
          }
        },
        {
          label: '申请时间',
          prop: 'createDate',
          minWidth: 160
        }
      ],
      tableVisible: true,

      actionList: [
        {
          label: '提交',
          event: row => this.handleSubmitUsingFundByTableData(row)
        },
        {
          label: '编辑',
          event: row => this.handleOpenEnterFundModal(row, 'edit')
        },
        {
          label: '删除',
          event: this.handleDeleteUsingFundData
        },
        {
          label: '审批',
          event: row => this.handleOpenEnterFundModal(row, 'approval')
        },
        {
          label: '报销经费',
          event: row => this.handleOpenEnterFundModal(row, 'remibur')
        },
        {
          label: '预算变更',
          event: row => this.handleOpenEnterFundModal(row, 'change')
        },
        {
          label: '重新提交',
          event: row => this.handleOpenEnterFundModal(row, 'reSubmit')
        }
      ]
    };
  },
  computed: {
    columns() {
      this.reRenderTable();
      let actionCol = [
          {
            label: '操作',
            align: 'center',
            fixed: 'right',
            formatter: (row, prop, cell) => {
              let actions =
                  {
                    0: [0, 1, 2],
                    1: [3],
                    2: [4, 5],
                    3: [3],
                    4: [4],
                    5: [3]
                  }[row.status] || [],
                actionList = [];
              actions.map(index => {
                actionList.push(this.actionList[index]);
              });
              return (
                <BaseActionCell
                  actions={actionList}
                  on={{
                    'action-select': e => e(row)
                  }}></BaseActionCell>
              );
            }
          },
          null,
          {
            label: '操作',
            fixed: 'right',
            align: 'center',
            formatter: (row, prop, cell) => {
              let actions =
                  {
                    7: [6, 2],
                    9: [6, 2]
                  }[row.status] || [],
                actionList = [];
              actions.map(index => {
                actionList.push(this.actionList[index]);
              });
              return (
                <BaseActionCell
                  actions={actionList}
                  on={{
                    'action-select': e => e(row)
                  }}></BaseActionCell>
              );
            }
          }
        ],
        columns = [...this.waitingHandleColumns];
      if (this.activeFundsType != 2) {
        columns.push(actionCol[this.activeFundsType - 1]);
      } else {
        columns.splice(7, 0, {
          label: '当前审批人',
          prop: 'assigneeName',
          minWidth: 100
        });
      }
      return columns;
    }
  },
  watch: {
    activeFundsType() {
      this.refresh();
    }
  },
  methods: {
    refreshTable(page) {
      let { pageNo = 1, pageSize = 15 } = page || {},
        data = {
          pageNo,
          pageSize,
          ...this.searchForm,
          index: this.activeFundsType,
          sidx: 'b.create_date',
          sord: 'desc'
        };
      this.ajax.getUsingFundsDataList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        res.rows.forEach((item, index) => {
          item.pageIndex = index + 1 + (pageNo - 1) * pageSize;
        });
        this.$refs.table.refresh(res);
      });
    },
    getFundTypes() {
      this.ajax
        .getFundTypesTreeData()
        .then(res => {
          this.fundTypeTreeData = res;
        })
        .catch();
    },
    reRenderTable() {
      this.tableVisible = false;
      this.$nextTick(() => {
        this.tableVisible = true;
      });
    }
  }
};
