<template>
  <div class="trasen-container">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="refresh"
    >
      <template slot="fundType">
        <ts-ztree-select
          :data="fundTypeTreeData"
          defaultExpandAll
          :inpText.sync="searchForm.fundTypeName"
          :inpVal.sync="searchForm.fundTypeId"
          textName="typeName"
          valName="id"
        >
        </ts-ztree-select>
      </template>

      <template slot="right">
        <div class="action-content flex-col-center">
          <ts-tabs v-model="activeFundsType" class="fund-type">
            <ts-tab-pane label="待办" name="1"></ts-tab-pane>
            <ts-tab-pane label="处理中" name="2"></ts-tab-pane>
            <ts-tab-pane label="已完成" name="3"></ts-tab-pane>
          </ts-tabs>
          <div class="flex">
            <ts-button type="text" @click="handleShowUsingStep">
              使用流程
            </ts-button>
            <ts-button type="primary" @click="handleOpenEnterFundModal()">
              经费使用
            </ts-button>
          </div>
        </div>
      </template>
    </ts-search-bar>

    <base-table
      ref="table"
      v-if="tableVisible"
      border
      stripe
      :columns="columns"
      @refresh="refreshTable"
    >
    </base-table>

    <ts-dialog :visible.sync="visible" :title="title" type="fullScreen">
      <div class="dialog-content">
        <using-fund-board
          ref="form"
          v-model="editData"
          :editType="editType"
        ></using-fund-board>

        <template v-if="editType == 'approval'">
          <div
            v-if="editData.status == 5"
            class="flex"
            style="margin-top: 8px;"
          >
            <div
              style="width: 124px; text-align: right; line-height: 36px; padding-right: 8px;"
            >
              报销附件
            </div>
            <div class="flex-grow">
              <base-upload
                v-model="editData.reimbursementFileids"
                :onlyRead="true"
                :actions="['preview', 'downLoad']"
              />
            </div>
          </div>

          <p class="section-title">审批</p>
          <ts-form ref="approvalForm" :model="editData">
            <template v-if="editData.wfStepName == '财务科主任'">
              <ts-row>
                <ts-col :span="12">
                  <ts-form-item
                    label="分管领导审批人"
                    prop="leaderApproverName"
                    :rules="rules.requiredRow"
                    label-width="150px"
                  >
                    <ts-input
                      v-model="editData.leaderApproverName"
                      placeholder="请选择审核人"
                      @focus="handleOpenSelectUser"
                      clearable
                    ></ts-input>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </template>
            <ts-form-item
              label="审批意见"
              prop="type"
              :rules="rules.requiredRow"
            >
              <ts-radio-group v-model="editData.type">
                <ts-radio label="1">同意</ts-radio>
                <ts-radio label="0">不同意</ts-radio>
              </ts-radio-group>
            </ts-form-item>

            <ts-form-item label="备注">
              <ts-input
                v-model="editData.approvalRemark"
                type="textarea"
                rows="4"
                resize="none"
                maxlength="200"
              ></ts-input>
            </ts-form-item>
          </ts-form>
        </template>

        <template
          v-if="
            editType == 'remibur' ||
              (editType == 'reSubmit' && editData.status == 9)
          "
        >
          <p class="section-title">报销信息</p>
          <ts-form ref="remiburForm" :model="editData">
            <ts-form-item
              label="附件（发票）"
              prop="reimbursementFileids"
              :rules="rules.requiredRow"
            >
              <base-upload v-model="editData.reimbursementFileids" />
            </ts-form-item>

            <ts-form-item label="备注">
              <ts-input
                v-model="editData.reimbursementRemark"
                type="textarea"
                rows="4"
                resize="none"
                maxlength="200"
              ></ts-input>
            </ts-form-item>
          </ts-form>
        </template>

        <template v-if="editType == 'change'">
          <ts-form ref="changeForm" :model="editData">
            <ts-form-item
              label="变更事由"
              prop="changesReason"
              :rules="rules.requiredRow"
            >
              <ts-input
                v-model="editData.changesReason"
                type="textarea"
                rows="4"
                resize="none"
                maxlength="300"
              ></ts-input>
            </ts-form-item>

            <ts-form-item label="备注">
              <ts-input
                v-model="editData.changesRemark"
                type="textarea"
                rows="4"
                resize="none"
                maxlength="200"
              ></ts-input>
            </ts-form-item>
          </ts-form>
        </template>
      </div>
      <template slot="footer">
        <ts-button v-if="editType == 'add'" @click="handleSubmitUsingFund(0)">
          存草稿
        </ts-button>
        <ts-button type="primary" @click="handleModelSubmit">
          确定
        </ts-button>
        <ts-button @click="handleCancelEditFundModal">取消</ts-button>
      </template>
    </ts-dialog>

    <preview-board :visible.sync="previewVisible" :editData="editData" />
    <UsingHelpModal ref="usingHelpModal" />
    <ts-user-dept-select ref="TsUserDeptSelect" @ok="handleOk" />
  </div>
</template>

<script>
import indexJs from './index.js';

import usingFundBoard from './components/using-fund-board.vue';
import BaseUpload from '@/components/base-upload/index.vue';
import previewBoard from './components/preview-board.vue';
import UsingHelpModal from '../components/using-help-modal.vue';
import TsUserDeptSelect from '@/components/ts-user-dept-select';

export default {
  mixins: [indexJs],
  components: {
    usingFundBoard,
    BaseUpload,
    previewBoard,
    UsingHelpModal,
    TsUserDeptSelect
  },
  data() {
    return {
      title: '新增',
      editData: {},
      editType: 'add',
      visible: false,

      rules: {
        requiredRow: { required: true, message: '必填' }
      },

      previewVisible: false
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  watch: {
    'editData.leaderApproverName'(val) {
      if (!val) {
        delete this.editData.leaderApproverCode;
      }
    }
  },
  methods: {
    refresh() {
      this.getFundTypes();
      this.$refs.table?.triggerRefresh();
    },
    handleShowUsingStep() {
      this.$refs.usingHelpModal?.show();
    },
    async handleOpenEnterFundModal(row = {}, type = 'add') {
      this.editData = row;
      this.editType = type;
      this.title = {
        add: '新增',
        edit: '编辑',
        approval: '审批',
        remibur: '经费报销',
        reSubmit: '重新提交',
        change: '预算变更'
      }[type];

      if (this.title == '新增') {
        this.editData = {
          userName: this.userInfo.employeeName
        };
      } else {
        let res = {};
        if (type == 'approval') {
          res = await this.ajax.getUsingFundsDataDetailSpecial({
            id: row.id,
            assigneeNo: this.userInfo.employeeNo
          });
        } else {
          res = await this.ajax.getUsingFundsDataDetail(row.id);
        }

        if (!res.success) {
          this.$message.error(res.message || '经费使用详情获取失败');
          return;
        }
        this.editData = res.object;
        this.editData.fundBudgetList = this.editData.fundBudgetList.map(
          (item, index) => {
            return { ...item, pageIndex: index + 1, editting: null };
          }
        );
        this.editData.userName = this.editData.leaderUserName;
        if (type == 'reSubmit') {
          this.editData.isResubmit = 1;
        }
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
        this.$refs.form.refreshData();
        this.$refs.approvalForm?.clearValidate();
        this.$refs.remiburForm?.clearValidate();
        this.$refs.changeForm?.clearValidate();
      });
    },
    handleCancelEditFundModal() {
      this.editData = {};
      this.visible = false;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleModelSubmit() {
      switch (this.editType) {
        case 'edit':
          this.handleUpdateUsingFundData();
          break;
        case 'add':
          this.handleSubmitUsingFund(1);
          break;
        case 'approval':
          this.handleApprovalUsingFundData();
          break;
        case 'remibur':
          this.handleRemiburFund();
          break;
        case 'reSubmit':
          this.handleReSubmitUsingFund();
          break;
        case 'change':
          this.handleChangeUsingFund();
          break;
      }
    },
    /**@desc 获取详情 */
    getDetial(row) {
      return this.ajax
        .handleGetAllUsingFundData({ id: row.id, entryItemId: row.entryItemId })
        .then(res => {
          if (!res.success) {
            this.$message.error(res.message || '详情数据获取失败');
            return false;
          }
          return res.object;
        })
        .catch(res => {
          return false;
        });
    },
    /**@desc 显示详情 */
    async handleShowPreview(row) {
      let res = await this.getDetial(row);

      if (!res) {
        this.$message.error(res.message || '经费使用详情获取失败');
        return;
      }
      this.editData = res;
      this.previewVisible = true;
    },
    /**@desc 提交 */
    async handleSubmitUsingFundByTableData(row) {
      const confirm = await this.$confirm(
        '提交后将不能进行更改，确定提交吗？',
        '提示',
        {
          type: 'warning'
        }
      );

      if (confirm == 'cancel') {
        return;
      }

      let res = await this.ajax.getUsingFundsDataDetail(row.id);
      if (!res.success) {
        this.$message.error(res.message || '经费使用详情获取失败');
        return;
      }

      let data = {
        ...res.object,
        status: 1
      };

      this.ajax.handleUpdateUsingFundData(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '提交失败');
          return;
        }

        this.$message.success('提交成功');
        this.handleCancelEditFundModal();
        this.refresh();
      });
    },
    /**@desc 编辑 */
    async handleUpdateUsingFundData() {
      let validate = await this.$refs.form.validate();
      if (!validate) {
        return;
      }

      let res = await this.ajax.handleUpdateUsingFundData({
        ...this.editData
      });

      if (!res.success) {
        this.$message.error(res.message || '编辑失败');
        return;
      }

      this.$message.success('编辑成功');
      this.handleCancelEditFundModal();
      this.refresh();
    },
    //提交、保存
    async handleSubmitUsingFund(status) {
      let validate = await this.$refs.form.validate();
      if (!validate) {
        return;
      }

      let data = {
        ...this.editData,
        status: status
      };

      this.ajax.handleAddFundUsingData(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '新增失败');
          return;
        }
        this.$message.success('新增成功');
        this.handleCancelEditFundModal();
        this.refresh();
      });
    },
    /**@desc 删除 */
    async handleDeleteUsingFundData(row) {
      const confirm = await this.$confirm('您确定删除此项吗？', '提示', {
        type: 'warning'
      });

      if (confirm == 'cancel') {
        return;
      }

      this.ajax.handleDeleteUsingFundData(row.id).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.handleCancelEditFundModal();
        this.refresh();
      });
    },
    /**@desc 审批 */
    async handleApprovalUsingFundData() {
      let validate = await this.$refs.approvalForm.validate().then(res => res);
      if (!validate) {
        return;
      }

      this.ajax.handleApprovalUsingFundData(this.editData).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '审批失败');
          return;
        }

        this.$message.success('审批成功');
        this.handleCancelEditFundModal();
        this.refresh();
      });
    },
    /**@desc 报销经费 */
    async handleRemiburFund() {
      let validate = await this.$refs.remiburForm.validate().then(res => res),
        formValidate = await this.$refs.form.validate();
      if (!validate || !formValidate) {
        return;
      }

      this.ajax
        .handleUpdateUsingFundData({
          ...this.editData,
          status: 5
        })
        .then(res => {
          if (!res.success) {
            this.$message.error(res.message || '报销失败');
            return;
          }

          this.$message.success('报销成功');
          this.handleCancelEditFundModal();
          this.refresh();
        });
    },
    /**@desc 重新提交 */
    async handleReSubmitUsingFund() {
      let validate = await this.$refs.form.validate(),
        formValidate = await this.$refs.remiburForm.validate().then(res => res);
      if (!validate || !formValidate) {
        return;
      }
      let data = {
        ...this.editData,
        status: this.editData.status == 9 ? 5 : 1
      };
      this.ajax.handleUpdateUsingFundData(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '提交失败');
          return;
        }

        this.$message.success('提交成功');
        this.handleCancelEditFundModal();
        this.refresh();
      });
    },
    /**@desc 预算变更 */
    async handleChangeUsingFund() {
      let validate = await this.$refs.form.validate(),
        formValidate = await this.$refs.changeForm.validate().then(res => res);
      if (!validate || !formValidate) {
        return;
      }
      let data = {
        ...this.editData,
        status: 3
      };
      data.fundBudgetChangeList = this.$refs.form.fundBudgetList;

      let res = await this.ajax.handleUpdateUsingFundData(data);

      if (!res.success) {
        this.$message.error(res.message || '变更失败');
        return;
      }

      this.$message.success('变更成功');
      this.handleCancelEditFundModal();
      this.refresh();
    },
    /**@desc 打开人员选择弹窗 */
    handleOpenSelectUser() {
      let empList = [];
      if (this.editData.leaderApproverCode) {
        let codeList = this.editData.leaderApproverCode.split(','),
          nameList = this.editData.leaderApproverName.split(',');
        empList = codeList.map((code, index) => ({
          empCode: code,
          empName: nameList[index]
        }));
      }
      this.$refs.TsUserDeptSelect.open('typeApproverData', {
        showCheckbox: false,
        title: '选择',
        empList,
        deptList: [],
        appendToBody: true,
        isRadio: false
      });
    },
    handleOk(res) {
      let { typeApproverData = {} } = res || {},
        { empList = [] } = typeApproverData,
        codeList = [],
        nameList = [];
      empList.map(item => {
        let { empName, empCode } = item;
        codeList.push(empCode);
        nameList.push(empName);
      });

      this.$set(this.editData, 'leaderApproverName', nameList.join(','));
      this.$set(this.editData, 'leaderApproverCode', codeList.join(','));
    },
    async handlePageChangeOpenModal(id) {
      let res = await this.getDetial({ id });
      if (!res) {
        return;
      }
      res.status == 1 && this.handleOpenEnterFundModal(res, 'approval');
    }
  },
  mounted() {
    this.$event.create('mainMessage').listen('messageToastEvent', val => {
      let { businessId, path } = val;
      if (
        location.pathname !=
        '/container/ts-web-contract/fundManagement/usingFunds'
      ) {
        return;
      }
      this.handlePageChangeOpenModal(businessId);
    });
  }
  // beforeRouteEnter(to, from, next) {
  //   if (to.query && to.query.hasOwnProperty('businessId')) {
  //     next(vm => {
  //       vm.handlePageChangeOpenModal(to.query.businessId);
  //     });
  //     return;
  //   }
  //   next();
  // }
};
</script>

<style lang="scss" scoped>
.action-cell {
  color: $primary-blue;
  cursor: pointer;
}
.trasen-container {
  display: flex;
  flex-direction: column;
}
.action-content {
  justify-content: space-between;
}
/deep/ .fund-type .el-tabs__header {
  margin: 0;
}
.section-title {
  display: flex;
  align-items: center;
  font-size: $seconde-title-text-size;
  font-weight: 600;
  margin: $primary-spacing 0;
  &::before {
    content: ' ';
    height: 16px;
    width: 4px;
    border-radius: 8px;
    background-color: $primary-blue;
    margin-right: 4px;
  }
}
.dialog-content {
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /deep/ .form-content {
    flex: 1;
  }
}
.error-span {
  color: $error-color;
}
.full-percent /deep/ .el-progress-bar__inner {
  background-color: $error-color !important;
}
</style>
