<template>
  <ts-dialog :visible.sync="insideVisible" title="绩效考核详情" type="large">
    <ts-form :model="data">
      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            label="项目负责人"
            prop="leaderUserName"
            :rules="requiredRow"
          >
            <ts-select v-model="data.leaderUserName" disabled>
              <ts-option
                :label="data.leaderUserName"
                :value="data.leaderUserName"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <ts-form-item label="项目名称" prop="itemName" :rules="requiredRow">
            <ts-select v-model="data.itemName" disabled>
              <ts-option
                :label="data.itemName"
                :value="data.itemName"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item
            label="核定项目经费"
            prop="approvedItemFund"
            :rules="requiredRow"
          >
            <ts-input
              v-model="data.approvedItemFund"
              placeholder="请输入核定项目经费(元)"
              disabled
            >
              <template slot="append">元</template>
            </ts-input>
          </ts-form-item>

          <ts-form-item>
            <div class="cost-line">余额：{{ balanceList[0] || 0 }}元</div>
          </ts-form-item>
        </ts-col>
        <ts-col :span="12">
          <template v-if="data.approvedItemFund">
            <ts-form-item
              label="医院配套经费"
              prop="supportingFund"
              :rules="requiredRow"
            >
              <ts-input
                v-model="data.supportingFund"
                placeholder="请输入医院配套经费(元)"
                disabled
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
            <ts-form-item>
              <div class="cost-line">余额：{{ balanceList[1] || 0 }}元</div>
            </ts-form-item>
          </template>
        </ts-col>
      </ts-row>
    </ts-form>

    <base-table ref="table" border stripe :hasPage="false" :columns="columns">
    </base-table>

    <template slot="footer">
      <ts-button @click="insideVisible = false">确定</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import BaseUpload from '@/components/base-upload/index.vue';

export default {
  props: {
    data: Object,
    visible: Boolean
  },
  data() {
    return {
      insideVisible: false,

      requiredRow: { required: true, message: '必填' },
      balanceList: [],

      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '文件名称',
          minWidth: 250,
          formatter: row => {
            return (
              <div>
                <BaseUpload
                  attrs={{
                    onlyRead: true,
                    businessId: row.monitoringReportFileids,
                    actions: ['preview', 'downLoad']
                  }}></BaseUpload>
                <BaseUpload
                  attrs={{
                    onlyRead: true,
                    businessId: row.monitoringTableFileids,
                    actions: ['preview', 'downLoad']
                  }}></BaseUpload>
              </div>
            );
          }
        },
        {
          label: '上传人',
          prop: 'createUserName'
        },
        {
          label: '上传时间',
          prop: 'createDate',
          width: 160
        }
      ]
    };
  },
  watch: {
    insideVisible(val) {
      this.$emit('update:visible', val);
    },
    visible(val) {
      this.insideVisible = val;
    },
    data(val = {}) {
      let {
        approvedItemFund = null,
        supportingFund = null,
        actualAmount = null
      } = val;

      let approvalBalance = approvedItemFund - actualAmount,
        supportBalance = 0;
      if (approvalBalance < 0) {
        supportBalance = supportingFund + approvalBalance;
        approvalBalance = 0;
      } else {
        supportBalance = supportingFund;
      }

      approvalBalance && (approvalBalance = approvalBalance.toFixed(2));
      supportBalance && (supportBalance = supportBalance.toFixed(2));

      this.balanceList = [approvalBalance, supportBalance];

      this.$nextTick(() => {
        this.$refs.table?.refresh({
          rows: val.fundChecklist || []
        });
      });
    }
  }
};
</script>

<style></style>
