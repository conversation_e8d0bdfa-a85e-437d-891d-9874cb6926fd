<template>
  <div name="budgetItem">
    <p class="section-title">预算信息</p>
    <ts-table
      ref="table"
      :data="tableData"
      :columns="columns"
      border
      stripe
      show-summary
      :summary-method="handleSummary"
      :span-method="handleMergeTable"
      :row-class-name="computedRowClassName"
      @row-click="hanldeRowClick"
      class="inform-table"
    >
    </ts-table>
  </div>
</template>

<script>
import { inputTowDecimalPlaces } from '@/util/index.js';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  model: {
    prop: 'data',
    event: 'input'
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: () => 'edit'
    },
    status: [String, Number]
  },
  computed: {
    isEdit() {
      return ['edit', 'add', 'change'].includes(this.type);
    },
    lastTwoColEditable() {
      return ['remibur', 'reSubmit'].includes(this.type);
    },
    tableData() {
      if (['add', 'edit', 'change'].includes(this.type)) {
        return this.data.concat([{ actionRow: true }]);
      }
      return this.data;
    }
  },
  data() {
    return {
      currentRow: null,
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 60,
          formatter: row => {
            if (!row.actionRow) {
              return row.pageIndex;
            }
            return (
              <div
                name="actionRowContent"
                style="display: flex; justify-content: center; padding: 8px 0;">
                <TsButton type="primary" onclick={() => this.handleAddRow()}>
                  新增行
                </TsButton>
                <TsButton type="danger" onclick={() => this.handleDeleteRow()}>
                  删除行
                </TsButton>
                <TsButton
                  onclick={() => this.handleMoveTableRow()}
                  disabled={
                    !this.currentRow ||
                    (this.currentRow && this.currentRow.pageIndex == 1)
                  }>
                  上移一行
                </TsButton>
                <TsButton
                  onclick={() => this.handleMoveTableRow(true)}
                  disabled={
                    !this.currentRow ||
                    (this.currentRow &&
                      this.currentRow.pageIndex == this.data.length)
                  }>
                  下移一行
                </TsButton>
              </div>
            );
          }
        },
        {
          label: '类别',
          prop: 'type',
          formatter: row => {
            if (!this.isEdit && this.status != 7) {
              return row.type;
            }

            return (
              <div
                class="edit-cell-box"
                on={{
                  dblclick: e => this.handleDbClick(e, row, 'type')
                }}>
                {row.editting != 'type' && row.type}
                <div vShow={row.editting == 'type'} class="ts-input el-input">
                  <input
                    class="el-input__inner"
                    vModel={row.type}
                    placeholder="请输入"
                    on={{
                      blur: () => (row.editting = null),
                      keypress: e => e.keyCode == 13 && (row.editting = null)
                    }}
                  />
                </div>
              </div>
            );
          }
        },
        {
          label: '项目',
          prop: 'smallItem',
          formatter: row => {
            if (!this.isEdit && this.status != 7) {
              return row.smallItem;
            }

            return (
              <div
                class="edit-cell-box"
                on={{
                  dblclick: e => this.handleDbClick(e, row, 'smallItem')
                }}>
                {row.editting != 'smallItem' && row.smallItem}
                <div
                  vShow={row.editting == 'smallItem'}
                  class="ts-input el-input">
                  <input
                    class="el-input__inner"
                    vModel={row.smallItem}
                    placeholder="请输入"
                    on={{
                      blur: () => (row.editting = null),
                      keypress: e => e.keyCode == 13 && (row.editting = null)
                    }}
                  />
                </div>
              </div>
            );
          }
        },
        {
          label: '预算金额(元)',
          prop: 'budgetAmount',
          width: 120,
          align: 'right',
          formatter: row => {
            if (!this.isEdit && this.status != 7 && this.type != 'change') {
              return row.budgetAmount;
            }

            return (
              <div
                class="edit-cell-box"
                on={{
                  dblclick: e => this.handleDbClick(e, row, 'budgetAmount')
                }}>
                {row.editting != 'budgetAmount' && row.budgetAmount}
                <div
                  vShow={row.editting == 'budgetAmount'}
                  class="ts-input el-input">
                  <input
                    class="el-input__inner"
                    vModel={row.budgetAmount}
                    placeholder="请输入"
                    on={{
                      input: e =>
                        this.validateTowDecimalPlaces(
                          e.target.value,
                          row,
                          'budgetAmount'
                        ),
                      blur: () =>
                        this.handleTowDecimalPlacesBlur(row, 'budgetAmount'),
                      keypress: e =>
                        e && e.keyCode == 13 && (row.editting = null)
                    }}
                  />
                </div>
              </div>
            );
          }
        },
        {
          label: '实际执行金额(元)',
          prop: 'actualAmount',
          width: 150,
          align: 'right',
          formatter: row => {
            if (
              !this.lastTwoColEditable ||
              (this.type == 'reSubmit' && this.status != 9)
            ) {
              return row.actualAmount;
            }

            return (
              <div
                class="edit-cell-box"
                on={{
                  dblclick: e => this.handleDbClick(e, row, 'actualAmount')
                }}>
                {row.editting != 'actualAmount' && row.actualAmount}
                <div
                  vShow={row.editting == 'actualAmount'}
                  class="ts-input el-input">
                  <input
                    class="el-input__inner"
                    vModel={row.actualAmount}
                    placeholder="请输入"
                    on={{
                      input: e =>
                        this.validateTowDecimalPlaces(
                          e.target.value,
                          row,
                          'actualAmount'
                        ),
                      blur: () =>
                        this.handleTowDecimalPlacesBlur(row, 'actualAmount'),
                      keypress: e =>
                        e && e.keyCode == 13 && (row.editting = null)
                    }}
                  />
                </div>
              </div>
            );
          }
        },
        {
          label: '预算执行率',
          prop: 'percent',
          width: 120,
          align: 'right',
          formatter: row => {
            if (!row.actualAmount) {
              return;
            }

            return (
              (
                ((row.actualAmount * 100) / (row.budgetAmount * 100)) *
                100
              ).toFixed(2) + '%'
            );
          }
        }
      ]
    };
  },
  methods: {
    /**@desc 聚焦行 */
    focusTableRow(index = 0) {
      this.$nextTick(() => {
        let table = this.$refs.table.$el,
          rows = table.querySelectorAll(
            '.el-table__body-wrapper tr.el-table__row'
          ),
          actionRow = rows[index];

        actionRow && actionRow.click();
      });
    },
    hanldeRowClick(row, column, click) {
      if (
        (row && row.actionRow) ||
        !['add', 'edit', 'change', 'remibur', 'reSubmit'].includes(this.type)
      ) {
        return;
      }

      this.currentRow = row;
    },
    /**@desc 新增行 */
    handleAddRow() {
      this.$emit(
        'input',
        this.data.concat([
          {
            pageIndex: this.data.length + 1,
            editting: null
          }
        ])
      );
    },
    /**@desc 删除行 */
    handleDeleteRow() {
      if (!this.currentRow) {
        this.$message.warning('请选择需要删除的行');
        return;
      }
      this.$confirm(`确定要删除该数据吗？`, '提示', {
        type: 'warning'
      }).then(() => {
        let index = this.data.findIndex(
          item => item.pageIndex == this.currentRow.pageIndex
        );

        this.currentRow = null;
        this.data.splice(index, 1);
        this.data.map((row, index) => (row.pageIndex = index + 1));
      });
    },
    /**@desc 上移 下移 */
    handleMoveTableRow(type = false) {
      if (!this.currentRow) {
        this.$message.warning('请选择要操作的数据行');
        return;
      }

      let index = this.data.findIndex(
          item => item.pageIndex == this.currentRow.pageIndex
        ),
        row = this.data.splice(index, 1)[0];
      this.data.splice(index + (type ? 1 : -1), 0, row);
      this.data.map((row, index) => (row.pageIndex = index + 1));
    },
    /**@desc 计算 总计 */
    handleSummary({ columns, data }) {
      let sums = ['总价', '', ''];
      columns.map((col, index) => {
        if (index <= 2) {
          return;
        }

        let sum = 0;
        data.map(row => {
          let num = parseFloat(row[col.property]);
          if (isNaN(num)) {
            return;
          }
          sum += num * 100;
        });
        sums[index] = (sum / 100).toFixed(2);
      });
      sums[5] = sums[5] + '%';
      if (sums[3] / 1 > 0) {
        sums[5] = ((sums[4] / sums[3]) * 100).toFixed(2) + '%';
      }
      return sums;
    },
    /**@desc 计算 表格合并 */
    handleMergeTable({ row, column, rowIndex, columnIndex }) {
      if (row.actionRow) {
        return columnIndex == 0 ? [1, 6] : [1, 0];
      }
      return [1, 1];
    },
    /**@desc 计算 行 className */
    computedRowClassName({ row, rowIndex }) {
      if (this.currentRow && this.currentRow.pageIndex == row.pageIndex) {
        return 'select-row';
      }
      if (row.actionRow) {
        return 'action-row';
      }
    },
    async handleDbClick(e, row, key) {
      row.editting = row.editting == key ? null : key;
      await this.$nextTick();
      let node = e.target.querySelector('input');
      node?.focus();
    },
    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, row, attr) {
      let newVal = inputTowDecimalPlaces(value);
      this.$set(row, attr, newVal);
    },
    /**@desc 规范两位小数输入框内容 */
    handleTowDecimalPlacesBlur(row, attr, e) {
      let val = (row && row[attr]) || '';
      if (val) {
        row[attr] = parseFloat(val);
      }
      row.editting = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.section-title {
  font-size: $seconde-title-text-size;
  font-weight: 600;
  display: flex;
  align-items: center;
  &::before {
    content: ' ';
    background-color: $primary-blue;
    border-radius: 4px;
    width: 4px;
    height: 16px;
    margin-right: 4px;
  }
}
.edit-cell-box {
  min-height: 30px;
  line-height: inherit;
  text-align: inherit;
  font-size: inherit;
  font-weight: inherit;
}
/deep/ .inform-table .el-table__body {
  tr td:first-child .cell {
    max-width: 100%;
  }
  .select-row {
    background: $ts-table-sort-bg !important;
    &:hover {
      background: mix($ts-table-sort-bg, #fff, 80%) !important;
    }
    td {
      background: transparent !important;
    }
  }
}
.ts-input.el-input {
  min-width: unset;
}
/deep/ .action-row .cell {
  width: 100% !important;
}
</style>
