<template>
  <ts-dialog
    class="dialog-add-group"
    :title="title"
    :visible.sync="visible"
    @close="close"
  >
    <ts-form ref="form" :model="form" labelWidth="80px">
      <ts-row>
        <ts-col :span="14">
          <ts-form-item
            label="分组名称:"
            prop="groupName"
            :rules="rules.required"
          >
            <ts-input v-model="form.groupName" placeholder="请输入分组名称" />
          </ts-form-item>
        </ts-col>
      </ts-row>
      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            label="分组排序:"
            prop="groupSort"
            :rules="rules.required"
          >
            <ts-input v-model="form.groupSort" placeholder="请输入分组排序" />
          </ts-form-item>
        </ts-col>
      </ts-row>
    </ts-form>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">保 存</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import api from '@/api/ajax/contract-form-design.js';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean,
      default: () => false
    },
    editGroupData: {
      type: Object,
      default: () => {}
    }
  },
  data: function() {
    return {
      title: '',
      type: 'add',
      visible: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  watch: {
    show: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.$nextTick(() => {
            this.$refs.form?.clearValidate();
          });

          if (JSON.stringify(this.editGroupData) !== '{}') {
            this.title = '编辑分组';
            this.type = 'edit';
            this.form = deepClone(this.editGroupData);
          } else {
            this.title = '新增分组';
            this.type = 'add';
            this.form = {};
          }
        }
      }
    }
  },
  methods: {
    async submit() {
      try {
        await this.$refs.form.validate();
        let API = null;
        if (this.type === 'add') {
          API = api.contractFieldGroupSave;
        } else {
          API = api.contractFieldGroupUpdate;
        }

        const res = await API(this.form);
        if (res.success) {
          this.$message.success('操作成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-group {
}
</style>
