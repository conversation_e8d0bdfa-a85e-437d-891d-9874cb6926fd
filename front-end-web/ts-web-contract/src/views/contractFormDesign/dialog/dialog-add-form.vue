<template>
  <ts-dialog
    class="dialog-add-form"
    title="新增表单"
    fullscreen
    :visible.sync="visible"
    @close="close"
  >
    <div class="content">
      <div class="left">
        <ts-form ref="form" :model="form" labelWidth="80px">
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="表单名称:"
                prop="formName"
                :rules="rules.required"
              >
                <ts-input
                  v-model="form.formName"
                  :disabled="onlyRead"
                  placeholder="请输入表单名称"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
        </ts-form>

        <hr
          style="background-color: #ccc; height:1px; border:none;margin:0 0 9px 0;"
        />
        <ul
          class="checked-list"
          v-for="(item, index) in fieldGroupList"
          :key="index"
        >
          <li class="list-item">
            <p>{{ item.groupName }}</p>
            <draggable class="checked-table" v-model="item.checkedFieldArr">
              <div
                v-for="(check, checkIndex) in item.checkedFieldArr"
                :key="checkIndex"
                class="checked-title"
              >
                {{ check.label }}
              </div>
            </draggable>
          </li>
        </ul>
      </div>
      <div class="right">
        <div class="top-tips">表单字段选择</div>
        <ul class="group-parent-box">
          <li v-for="(item, index) in fieldGroupList" :key="index">
            <h4>{{ item.groupName }}</h4>
            <ul class="children-field-box">
              <ts-checkbox
                v-for="field in item.contractFieldList"
                v-model="field.checked"
                :disabled="onlyRead"
                :title="field.fieldDesc"
                :key="field.id"
                :label="field.id"
                @change="handleCheckboxChange(field, item)"
                >{{ field.fieldDesc }}</ts-checkbox
              >
            </ul>
          </li>
        </ul>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">取 消</ts-button>
      <ts-button v-show="!onlyRead" type="primary" @click="submit"
        >确 定</ts-button
      >
    </span>
  </ts-dialog>
</template>

<script>
import api from '@/api/ajax/contract-form-design.js';
import draggable from 'vuedraggable';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  components: {
    draggable
  },
  props: {
    show: {
      type: Boolean,
      default: () => false
    },
    renderType: {
      type: String,
      default: ''
    },
    eachData: {
      type: Object,
      default: () => {}
    }
  },
  data: function() {
    return {
      title: '',
      visible: false,
      fieldGroupList: [],

      form: {
        formName: ''
      },
      rules: {
        required: [{ required: true, message: '必填' }]
      }
    };
  },
  computed: {
    onlyRead() {
      return this.renderType === 'check';
    }
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          await this.handleGetGroupList();
          this.$refs.form?.clearValidate();
          this.initRender();
        }
        this.visible = val;
      }
    }
  },
  methods: {
    initRender() {
      if (this.renderType === 'check' || this.renderType === 'edit') {
        const eachData = deepClone(this.eachData);
        // 回显
        this.form.formName = this.eachData.formName;

        eachData.formFieldList.forEach(item => {
          let ids = item.fieldId.split(',');
          let names = item.fieldName.split(',');

          // 找到分组对象
          let findGroup = this.fieldGroupList.filter(
            group => group.id === item.groupId
          )[0];

          // 将分组对象 选中字段 勾选上
          findGroup.contractFieldList
            .filter(setItem => ids.includes(setItem.id))
            .forEach(select => (select.checked = true));

          // 回显左侧 选中table
          findGroup.checkedFieldArr = ids.map((id, idIndex) => {
            return {
              id,
              label: names[idIndex]
            };
          });
        });
      }
    },
    async handleGetGroupList() {
      const res = await api.getContractFieldGroup();
      if (res.success && res.statusCode === 200) {
        res.object.forEach(item => {
          item.contractFieldList = item.contractFieldList || [];

          item.contractFieldList.forEach(fieldItem => {
            fieldItem.checked = false;
          });

          item.checkedFieldArr = item.checkedFieldArr || [];
        });
        this.fieldGroupList = deepClone(res.object);
      }
    },
    // 字段 切换事件
    handleCheckboxChange(field, item) {
      // 分组 内选中id数组
      const ids = item.checkedFieldArr.map(item => item.id);
      // change字段项的id
      const id = field.id;
      // 是否存在
      const result = ids.includes(id);

      // 存在 将其删除
      if (result) {
        item.checkedFieldArr = item.checkedFieldArr.filter(
          list => list.id !== id
        );
      } else {
        // 不存在 push
        item.checkedFieldArr.push({
          id,
          label: field.fieldDesc
        });
      }
    },
    async submit() {
      try {
        // 验证表单
        await this.$refs.form.validate();
        const formFieldList = [];

        // 处理数据
        for (let index = 0; index < this.fieldGroupList.length; index++) {
          const item = this.fieldGroupList[index];
          if (item.checkedFieldArr.length === 0) {
            break;
          }
          const checkedArr = item.checkedFieldArr;
          const groupId = item.id;
          const fieldId = checkedArr.map(item => item.id).join(',');
          const fieldName = checkedArr.map(item => item.label).join(',');
          const itemData = {
            groupId,
            fieldId,
            fieldName
          };

          formFieldList.push(itemData);
        }

        const data = {
          formFieldList,
          formName: this.form.formName
        };

        let API = null;
        switch (this.renderType) {
          case 'add':
            API = api.contractFormSave;
            break;
          case 'edit':
            data.id = this.eachData.id;
            API = api.contractFormUpdate;
            break;
        }
        // 提交
        const result = await API(data);
        if (result.statusCode === 200 && result.success) {
          this.$message.success('操作成功');
          this.$emit('refresh');
          this.close();
        } else {
          this.$message.error(result.message || '操作失败');
        }
      } catch (error) {
        console.error(error, 'error');
      }
    },
    close() {
      this.fieldGroupList = [];
      this.form.formName = '';
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-form {
  * {
    &::-webkit-scrollbar {
      width: 6px;
    }
    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  .content {
    max-height: calc(100vh - 143px);
    display: flex;
    .left,
    .right {
      flex: 1;
      max-height: calc(100vh - 143px);
      overflow-y: auto;
    }
    .left {
      padding-right: 8px;
      .checked-list {
        .list-item {
          p {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #000;
          }
          .checked-table {
            display: flex;
            flex-wrap: wrap;
            .checked-title {
              width: 50%;
              text-align: center;
              border: 1px solid #eee;
              height: 30px;
              line-height: 30px;
              cursor: pointer;
              &:nth-child(2n) {
                border-left: 0px;
              }
              &:nth-child(2) ~ .checked-title {
                border-top: 0px;
              }
            }
          }
        }
      }
    }
    .right {
      .top-tips {
        height: 44px;
        line-height: 44px;
        font-size: 18px;
        font-weight: 500;
        color: #000;
      }
      .group-parent-box {
        height: calc(100% - 44px);
        overflow-y: auto;
        > li {
          padding: 8px;
          border: 1px solid #eee;
          ::v-deep .children-field-box {
            display: flex;
            flex-wrap: wrap;
            .el-checkbox {
              width: 25%;
              margin-right: 0px;
              height: 30px;
              display: flex;
              align-items: center;
              .el-checkbox__label {
                flex: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }
        }
        &:first-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
