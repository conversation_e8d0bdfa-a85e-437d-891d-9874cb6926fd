<template>
  <div class="contract-form-design-box">
    <div class="head">
      <ts-tabs v-model="tabs">
        <ts-tab-pane label="字段管理" name="first"></ts-tab-pane>
        <ts-tab-pane label="表单管理" name="second"></ts-tab-pane>
        <ts-tab-pane label="编号规则" name="three"></ts-tab-pane>
      </ts-tabs>

      <div class="operate-box">
        <ts-button
          type="primary"
          @click="handleOpenAddGroupDialog"
          v-if="tabs === 'first'"
          >新增分组</ts-button
        >
        <ts-button
          v-if="tabs === 'second'"
          type="primary"
          @click="handleOpenFormDialog"
          >新增</ts-button
        >
        <ts-button
          v-if="tabs === 'three'"
          type="primary"
          @click="handleOpenNumberDialog"
          >新增</ts-button
        >
      </div>
    </div>

    <div class="content">
      <field-management ref="fieldManagement" v-if="tabs === 'first'" />
      <form-management ref="formManagement" v-if="tabs === 'second'" />
      <number-management ref="numberManagement" v-if="tabs === 'three'" />
    </div>
  </div>
</template>

<script>
import fieldManagement from './components/management-field.vue';
import formManagement from './components/management-form.vue';
import numberManagement from './components/management-number.vue';
export default {
  components: {
    fieldManagement,
    formManagement,
    numberManagement
  },
  data: function() {
    return {
      tabs: 'first'
    };
  },
  methods: {
    handleOpenAddGroupDialog() {
      this.$refs.fieldManagement.showAddGroupDialog();
    },
    handleOpenFormDialog() {
      this.$refs.formManagement.showAddFormDialog();
    },
    handleOpenNumberDialog() {
      this.$refs.numberManagement.showAddNumberDialog();
    }
  }
};
</script>

<style lang="scss" scoped>
.contract-form-design-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  .head {
    position: relative;
    .operate-box {
      position: absolute;
      right: 0;
      top: 4px;
    }
  }
  .content {
    height: calc(100% - 44px);
  }
}
</style>
