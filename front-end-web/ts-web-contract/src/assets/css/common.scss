
*{
  margin: 0;
  padding: 0;
  font: 14px/1.4 "Microsoft Yahei", Arial, Helvetica, sans-serif;
  list-style-type: none;
  text-decoration: none;
  box-sizing: border-box;
  background-color: transparent;
}
.qiankun-child-container{
  width: 100%;
  height: 100%;
}
body,html,.qiankun-child-container,#app{
  width: 100%;
  height: 100%;
  background-color: transparent;
}

// flex设
.flex{
  display: flex;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-row-center{
  display: flex;
  justify-content: center;
}
.flex-row-between{
  display: flex;
  justify-content: space-between;
}
.flex-col-center{
  display: flex;
  align-items: center;
}
.flex-row-evenly{
  display: flex;
  justify-content: space-evenly;
}

// ts-tabs样式不生效 进行修复
.el-tabs__nav {
  height: 36px !important;
}

.el-tabs__nav .el-tabs__item {
  font-weight: 400;
}

// base-table组件底部滚动条加粗 改变颜色
.table-content {
  .el-scrollbar__bar {
    &.is-horizontal {
      height: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover{
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
    &.is-vertical{
      width: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover{
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
  }
}