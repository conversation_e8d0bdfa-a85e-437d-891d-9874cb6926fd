import { $api } from '@/api/ajax';

const contractTypeSave = function(data) {
  return $api({
    url: `/ts-oa/api/contractType/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

const contractTypeUpdate = function(data) {
  return $api({
    url: `/ts-oa/api/contractType/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

const contractTypeDelete = function(id) {
  return $api({
    url: `/ts-oa/api/contractType/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

// 合同分类列表
const contractTypeList = function(data) {
  return $api({
    url: `/ts-oa/api/contractType/list`,
    method: 'get',
    data
  });
};

export default {
  contractTypeSave,
  contractTypeUpdate,
  contractTypeDelete,
  contractTypeList
};
