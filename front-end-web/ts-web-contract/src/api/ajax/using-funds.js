import { $api } from '@/api/ajax';
import { chixiao } from '@/api/config';

export default {
  /**@desc 获取经费使用列表 */
  getUsingFundsDataList(params) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/list`,
      method: 'get',
      params
    });
  },
  /**@desc 获取经费使用单条项目详情 */
  getUsingFundsDataDetail(id) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/${id}`,
      method: 'get'
    });
  },
  /**@desc 获取项目经理负责的项目 */
  getLeaderUserProjectDataList(params) {
    return $api({
      url: `${chixiao.oa()}/api/fundEntry/getByleaderUserlist`,
      method: 'get',
      params
    });
  },
  /**@desc 申请使用项目经费 */
  handleAddFundUsingData(data) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/save`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 审批 */
  handleApprovalUsingFundData(data) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/approval`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 编辑经费状态 */
  handleUpdateUsingFundData(data) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/update`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 删除 */
  handleDeleteUsingFundData(id) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/delete/${id}`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 获取全部详情 */
  handleGetAllUsingFundData(params) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/getdetailed`,
      method: 'get',
      params
    });
  },

  /**@desc 获取详情 -审批专用 */
  getUsingFundsDataDetailSpecial(params) {
    return $api({
      url: `${chixiao.oa()}/api/fundUse/selectApprovalDetails`,
      method: 'get',
      params
    });
  }
};
