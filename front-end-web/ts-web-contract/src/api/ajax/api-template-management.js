import { $api } from '@/api/ajax';

const contractTemplateSave = function(data) {
  return $api({
    url: `/ts-oa/api/contractTemplate/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

const contractTemplateUpdate = function(data) {
  return $api({
    url: `/ts-oa/api/contractTemplate/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: JSON.stringify(data)
  });
};

const contractTemplateDelete = function(id) {
  return $api({
    url: `/ts-oa/api/contractTemplate/delete/deleteContractTemplateById?id=${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

// 模版列表
const contractTemplateList = function(data) {
  return $api({
    url: `/ts-oa/api/contractTemplate/list`,
    method: 'get',
    data
  });
};

export default {
  contractTemplateSave,
  contractTemplateUpdate,
  contractTemplateList,
  contractTemplateDelete
};
