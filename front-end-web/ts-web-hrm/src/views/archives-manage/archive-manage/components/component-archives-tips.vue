<template>
  <div class="component-archives-tips" v-if="contentStagingData || editStatus">
    <div class="tips-box only-staging" v-if="contentStagingData && !editStatus">
      已默认自动填充暂存信息，黄色字段为暂存项
    </div>

    <div
      class="tips-box approval-in-progress"
      v-else-if="!contentStagingData && editStatus && editStatus.status === '1'"
    >
      <div>
        个人信息正在审批中,审批完成才能再次编辑
      </div>
      <div>（注：<span class="red">红色字段</span>为修改审批项）</div>
    </div>

    <div
      class="tips-box approval-pass"
      v-else-if="
        editStatus &&
          editStatus.status === '2' &&
          Array.isArray(editStatus.data) &&
          !editStatus.data.length
      "
    >
      <div class="staging-font" v-if="contentStagingData">
        已默认自动填充暂存信息，黄色字段为暂存项
      </div>
      <div>
        个人信息审批结束
        <span>（提示：全部通过）</span>
      </div>
    </div>

    <div
      class="tips-box approval-pass-has-unqualified"
      v-else-if="
        editStatus &&
          editStatus.status === '2' &&
          Array.isArray(editStatus.data) &&
          editStatus.data.length
      "
    >
      <div class="staging-font" v-if="contentStagingData">
        已默认自动填充暂存信息，黄色字段为暂存项
      </div>
      <div>
        个人信息审批结束
        <span class="field-fail-tips">
          （<span class="red">红色字段</span>为不合格项）
        </span>
      </div>
      <div class="field-fail-tips">
        请您按照不合格提示项进行修改提交
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    editStatus: {
      type: Object
    },
    contentStagingData: {
      type: Boolean
    }
  },
  data() {
    return {};
  }
};
</script>

<style scoped lang="scss">
.component-archives-tips .tips-box {
  position: fixed;
  top: 8px;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 9999;
  text-align: center;
  padding: 8px 18px;
  border-radius: 4px;

  .red {
    color: red;
  }

  &.only-staging {
    background-color: #ffff8f !important;
    box-shadow: 0 0 10px #cecfdd;
    color: #000;
  }

  &.approval-in-progress {
    background: #cfd2f4;
    color: rgba(189, 49, 36, 0.79);
    box-shadow: 0 0 10px #cecfdd;
  }

  &.approval-pass,
  &.approval-pass-has-unqualified {
    color: #333;
    background: #dfe4ff;
    box-shadow: 0 0 10px #cecfdd;

    .staging-font {
      color: rgb(145, 145, 6) !important;
    }
  }
}
</style>
