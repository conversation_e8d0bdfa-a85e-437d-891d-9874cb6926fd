<template>
  <ts-form
    class="component-person-self-setting"
    ref="ruleForm"
    :model="form"
    labelWidth="140px"
  >
    <colmun-head title="基本信息" />
    <ts-row>
      <ts-col :span="12">
        <ts-form-item label="姓名">
          <ts-input v-model="form.employeeName" disabled />
        </ts-form-item>
      </ts-col>
      <ts-col :span="12">
        <ts-form-item label="所在科室">
          <ts-input v-model="form.orgName" disabled />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-form-item label="兼职科室" v-show="rowDetails.showParttimeSwitch">
      <ts-ztree-multi-select
        v-if="JSON.stringify(form) != '{}'"
        ref="tree"
        v-model="form.partTimeDeptIds"
        :echoValue="form.partTimeDeptNames"
        :data="treeData"
        Y=""
        N=""
      />
    </ts-form-item>

    <colmun-head title="提醒设置" />
    <ts-row>
      <ts-col :span="12">
        <ts-form-item label="开启短信提醒">
          <vxe-switch
            v-model="form.isSmsReminder"
            openValue="1"
            openLabel="开启"
            closeValue="0"
            closeLabel="关闭"
          />
        </ts-form-item>
      </ts-col>
      <ts-col :span="12">
        <ts-form-item label="开启微信消息推送">
          <vxe-switch
            v-model="form.isWxReminder"
            openValue="1"
            openLabel="开启"
            closeValue="0"
            closeLabel="关闭"
          />
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-row>
      <ts-form-item
        class="label-line-height-20"
        label="开启号码显示保护（隐藏部分数字）"
      >
        <vxe-switch
          v-model="form.isDisplayPhoneNo"
          openValue="1"
          openLabel="开启"
          closeValue="0"
          closeLabel="关闭"
        />
      </ts-form-item>
    </ts-row>

    <colmun-head title="流程设置" />
    <ts-form-item label="是否启用流程代理人">
      <vxe-switch
        v-model="form.isEnableProcessAgent"
        openValue="1"
        openLabel="开启"
        closeValue="0"
        closeLabel="关闭"
      />
    </ts-form-item>

    <ts-form-item label="流程代理人">
      <ts-input
        v-model="form.agentNames"
        readonly
        placeholder="请选择"
        style="width: 100%;"
      >
        <template v-slot:suffix>
          <img
            class="person-icon"
            src="@/assets/img/defUserPhoto.png"
            @click="handleOpenSelectPerson('agentIds')"
          />
        </template>
      </ts-input>
    </ts-form-item>

    <!-- 代理有效期： agentStartTime agentEndTime -->
    <ts-form-item label="代理有效期">
      <form-date-bar v-model="form.date" />
    </ts-form-item>

    <colmun-head title="签章设置" />
    <ts-form-item label="是否启用电子签章">
      <vxe-switch
        v-model="form.isUseSignature"
        openValue="1"
        openLabel="开启"
        closeValue="0"
        closeLabel="关闭"
      />
    </ts-form-item>

    <ts-form-item label="电子签章">
      <ts-upload
        class="avatar-uploader"
        ref="AvatarUploader"
        accept="image/*"
        action="/ts-basics-bottom/fileAttachment/upload?moduleName=hrm"
        :show-file-list="false"
        :before-upload="beforeAvatarUpload"
        :on-success="handleAvatarSuccess"
      >
        <div v-if="form.signatureImgName" class="avatar-container">
          <i
            class="delet-btn oaicon oa-icon-cuowu"
            @click.stop="handleDelete"
          />
          <img :src="form.signatureImgName" class="avatar" />
        </div>
        <i v-else class="el-icon-picture avatar-uploader-icon"></i>
      </ts-upload>
    </ts-form-item>

    <colmun-head title="文档管理" />
    <ts-row>
      <ts-col :span="12">
        <ts-form-item label="个人文档容量" class="mt8">
          <ts-input
            v-model="form.uploadFileSize"
            :maxlength="4"
            placeholder="请输入"
            @input="
              value => (form.uploadFileSize = (value.match(/\d+/g) || [''])[0])
            "
          >
            <template slot="append">MB</template>
          </ts-input>
        </ts-form-item>
      </ts-col>
    </ts-row>

    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleOk" />
  </ts-form>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';

export default {
  data() {
    return {
      treeData: [],
      rowDetails: {},

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async init({ rowDetails = {} }) {
      this.rowDetails = {};
      this.treeData = [];
      this.form = {};

      this.rowDetails = rowDetails;
      await this.handleGetTree();
      let formDetails = await this.handleGetSettingDetails();
      if (formDetails) {
        this.$set(this, 'form', formDetails);
      } else {
        this.$emit('details-fail');
      }
    },

    async handleGetTree() {
      try {
        let res = await this.ajax.getDeptTreeList();
        if (!res.success) {
          let error = '获取科室信息失败, 请联系管理员!';
          this.$newMessage('error', error);
          throw error;
        }
        this.treeData = cloneDeep(res.object || []);
      } catch (error) {
        console.error(error);
      }
    },

    async handleGetSettingDetails() {
      try {
        let res = await this.ajax.customEmployeeBaseGetEmployeeDetail(
          this.rowDetails.employee_no
        );
        if (!res.success) {
          let error = '获取个人设置信息失败, 请联系管理员!';
          this.$newMessage('error', error);
          throw error;
        }

        let data = cloneDeep(res.object || {});
        if (
          Array.isArray(data.organizationParttimeList) &&
          data.organizationParttimeList.length
        ) {
          data.organizationParttimeList = data.organizationParttimeList.filter(
            item => item.isBelong == 0
          );

          data.partTimeDeptIds = data.organizationParttimeList.map(m => {
            return { id: m.orgId, name: m.orgName };
          });
          data.partTimeDeptNames = data.organizationParttimeList
            .map(m => m.orgName)
            .join(',');
        } else {
          data.organizationParttimeList = [];
          data.partTimeDeptIds = [];
          data.partTimeDeptNames = '';
        }

        if (data.agentStartTime && data.agentEndTime) {
          data.date = [data.agentStartTime, data.agentEndTime];
        }
        return data;
      } catch (error) {
        console.error(error);
      }
    },

    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$newMessage('error', '上传签证图片大小不能超过 10MB!');
      }
      return isLt10M;
    },

    handleAvatarSuccess(res, file) {
      if (!res.success) {
        this.$newMessage('error', '【上传电子签章】失败！');

        this.$set(this.form, 'signatureImgName', '');
        return false;
      }
      this.$set(this.form, 'signatureImgName', res.object[0].filePath);
    },

    handleDelete() {
      this.$set(this.form, 'signatureImgName', '');
    },

    handleOpenSelectPerson(key) {
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        isRadio: false,
        echoData: {
          agentIds: this.form.agentIds
        },
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp: ['agentNames', 'agentIds']
        }
      });
    },

    handleOk(result, key) {
      switch (key) {
        case 'agentIds':
          const { allNames: agentNames, agentIds } = result[key];
          this.$set(this.form, 'agentNames', agentNames);
          this.$set(this.form, 'agentIds', agentIds);
          break;
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.ruleForm.validate();
        let submitData = cloneDeep(this.form);
        const { partTimeDeptIds = [] } = submitData;
        const {
          employee_id,
          employee_no,
          employee_name,
          org_id,
          orgName
        } = this.rowDetails;

        Object.assign(submitData, {
          employeeId: employee_id,
          employeeNo: employee_no
        });

        const generateParttimeList = (isBelong, orgId, orgName) => ({
          employeeId: employee_id,
          employeeNo: employee_no,
          employeeName: employee_name,
          isBelong,
          orgId,
          orgName
        });

        submitData.organizationParttimeList = partTimeDeptIds.map(
          ({ id, name }, index) => generateParttimeList(0, id, name)
        );

        submitData.organizationParttimeList.unshift(
          generateParttimeList(1, org_id, orgName)
        );
        if (
          Array.isArray(submitData.date) &&
          submitData.date.filter(Boolean).length === 2
        ) {
          const [agentStartTime, agentEndTime] = submitData.date;
          Object.assign(submitData, { agentStartTime, agentEndTime });
        } else {
          Object.assign(submitData, { agentStartTime: '', agentEndTime: '' });
        }
        delete submitData.date;
        delete submitData.partTimeDeptIds;
        delete submitData.partTimeDeptNames;

        this.submitLoading = true;
        const res = await this.ajax.customEmployeeBaseUpdateUserConfig(
          submitData
        );
        if (!res.success) {
          let error = '【保存】失败！';
          this.$newMessage('error', error);
          throw error;
        }
        this.$newMessage('success', '【保存】成功');
        return true;
      } catch (error) {
        console.error(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.component-person-self-setting {
  ::v-deep {
    .search-content {
      justify-content: start;
    }

    .label-line-height-20 {
      .el-form-item__label {
        line-height: 20px;
      }
    }

    .mt8 {
      margin-top: 8px;
    }

    .person-icon {
      margin-top: 3px;
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    .avatar-uploader {
      .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        .avatar-uploader-icon {
          font-size: 40px;
          color: #8c939d;
          width: 128px;
          height: 128px;
          line-height: 128px;
          text-align: center;
        }
        .avatar-container {
          position: relative;

          .delet-btn {
            position: absolute;
            right: 2px;
            top: -2px;
            color: red;
            background-color: #fff;
            border-radius: 100%;
            font-size: 22px;
          }

          .avatar {
            width: 128px;
            height: 128px;
            display: block;
          }
        }
        &:hover {
          border-color: $primary-blue;
        }
      }
    }
  }
}
</style>
