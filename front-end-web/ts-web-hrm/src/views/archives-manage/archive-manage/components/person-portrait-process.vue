<template>
  <div class="person-portrait-process">
    <div class="progress-chart"></div>

    <div class="hospitalized">
      <span class="date">{{ entryDate }}</span>
      <span class="icon">入院</span>
    </div>

    <span class="switch-progress" @click="handleSwitchProgress">
      {{ switchProgress ? '展开' : '收起' }}
    </span>

    <div class="table-container">
      <div class="annual-paper">
        <div class="year-annual">
          <div class="table-title">年度考评</div>
          <div class="summary-table" id="YearEvaluation"></div>
        </div>

        <div class="paper">
          <div class="table-title">科研论文</div>
          <div class="summary-table" id="ScientResearch"></div>
        </div>
      </div>
      <div class="rewards-punishments-train">
        <div class="rewards-punishments">
          <div class="table-title">奖惩情况</div>
          <div class="summary-table" id="BonusPenalty"></div>
        </div>

        <div class="train">
          <div class="table-title">培训/进修情况</div>
          <table class="summary-table" id="TraiEducation"></table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';

export default {
  props: {
    entryDate: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      switchProgress: false
    };
  },
  methods: {
    handleSwitchProgress() {
      this.switchProgress = !this.switchProgress;
      this.$emit('switch-progress', this.switchProgress);
    },
    renderTimeline(progressArr) {
      let numUp = Math.ceil(progressArr.length / 5);
      let timeHtml = '';

      for (let i = 0; i < numUp; i++) {
        const isEnd = i == numUp - 1 ? 'end-group' : '';
        const groupClass =
          (i + 1) % 2 == 1 ? 'odd-group floatLeft' : 'even-group floatRight';

        timeHtml += `
            <div class="group ${groupClass} clearfix ${isEnd}">
              <div class="progress-arrow left-arrow"></div>
              <div class="g-line"></div>
              <div class="progress-arrow right-arrow"></div>
              <div class="time-detail clearfix">
          `;

        for (let j = 5 * i; j < 5 * (i + 1); j++) {
          const item = progressArr[j];
          if (item) {
            let itemStr = item.fieldValues
              .filter(Boolean)
              .map(f => `<p title=${f} >${f}</p>`)
              .join('');
            timeHtml += `
                <div class="detail-c">
                  <div class="circle">
                    <div class="circle-son"></div>
                  </div>
                  <div class="year-time">${item.firstTimeFormat}</div>
                  <div class="detail-m">${itemStr}</div>
                </div>`;
          }
        }

        timeHtml += `</div></div>`;
      }

      const chart = document.querySelector(
        '.person-portrait-process .progress-chart'
      );
      chart.innerHTML = timeHtml;

      const groups = chart.querySelectorAll('.group');
      const arrowToHide = groups.length % 2 ? '.right-arrow' : '.left-arrow';

      const lastGroup = groups[groups.length - 1];
      lastGroup.querySelector(arrowToHide).style.display = 'none';
    },
    renderRchxTable(rchxObject, personalEditSetting) {
      let ParentHtml = document.querySelector('.person-portrait-process'),
        YearEvaluation = ParentHtml.querySelector('#YearEvaluation'),
        ScientResearch = ParentHtml.querySelector('#ScientResearch'),
        BonusPenalty = ParentHtml.querySelector('#BonusPenalty'),
        TraiEducation = ParentHtml.querySelector('#TraiEducation');

      let tableObject = cloneDeep(rchxObject);
      let groupData = cloneDeep(personalEditSetting);
      let { ndkp, kylw, jcqk, jxgp } = tableObject;

      let itemStr = ``;
      for (const key in ndkp) {
        const value = ndkp[key];
        itemStr += `
          <div class="item-row" key="ndkp">
            <div class="label">${key}</div>
            <div class="value">${value || '-'}</div>
          </div>
        `;
      }
      YearEvaluation.innerHTML = itemStr;
      itemStr = ``;

      for (const key in kylw) {
        const value = kylw[key];
        let size = value.size ? value.size + '篇' : '0篇',
          details = value.size ? 'details' : '';

        itemStr += `
          <div class="item-row" key="kylw">
            <div class="label">${key}</div>                         
            <div class="value ${details}" key="${key}">${size}</div>
          </div>
        `;
      }
      ScientResearch.innerHTML = itemStr;
      itemStr = ``;

      for (const key in jcqk) {
        const value = jcqk[key];
        let size = value.size ? value.size + '项' : '0项',
          details = value.size ? 'details' : '';

        itemStr += `
          <div class="item-row" key="jcqk">
            <div class="label">${key}</div>
            <div class="value ${details}" key="${key}">${size}</div>
          </div>
        `;
      }
      BonusPenalty.innerHTML = itemStr;
      itemStr = ``;

      for (const key in jxgp) {
        const value = jxgp[key];
        let size = value.size ? value.size + '项' : '0项',
          details = value.size ? 'details' : '';

        itemStr += `
          <div class="item-row" key="jxgp">
            <div class="label">${key}</div>                          
            <div class="value ${details}" key="${key}">${size}</div>
          </div>
        `;
      }
      TraiEducation.innerHTML = itemStr;
      itemStr = ``;

      const detailsElements = ParentHtml.querySelectorAll('.details');
      detailsElements.forEach(element => {
        element.addEventListener('click', event => {
          let key = event.target.closest('.item-row')?.getAttribute('key');
          let dataKey = event.target.getAttribute('key');
          if (!key || !dataKey) return false;

          let dic = {
            kylw: '8',
            jcqk: 'jc',
            jx: 'jx',
            gp: 'gp',
            zyysgfhpx: '9'
          };

          let groupItem = groupData.find(f => {
              let pengdingKey = key;
              if (dataKey === '规范化培训') pengdingKey = 'zyysgfhpx';
              if (dataKey === '进修') pengdingKey = 'jx';
              if (dataKey === '规培') pengdingKey = 'gp';

              return f.id == dic[pengdingKey];
            }),
            groupFields = groupItem.fields,
            fieldsArr = groupFields.map(item => ({
              label: item.showName,
              prop: item.fieldName,
              fieldType: item.fieldType,
              align: 'center'
            })),
            tableData = tableObject[key][dataKey].data;
          this.$emit('open-portrait-item-details', fieldsArr, tableData);
        });
      });
    }
  }
};
</script>

<style lang="scss">
.person-portrait-process {
  position: relative;
  flex: 1;
  overflow: auto;
  .switch-progress {
    position: absolute;
    right: 40px;
    top: 10px;
    font-size: 14px;
    color: #5260ff;
    cursor: pointer;
    z-index: 9999;
  }
  .hospitalized {
    position: absolute;
    left: 0;
    top: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .date {
      margin-bottom: 4px;
      font-size: 12px;
      font-weight: 700;
      color: #333;
    }
    .icon {
      border-radius: 25px;
      padding: 2px 10px;
      border: 1px solid #d8d8d8;
      background-color: #fff;
      color: rgba(0, 0, 0, 0.65);
      text-align: center;
      font-size: 12px;
    }
  }
  .progress-chart {
    width: 100%;
    padding: 0 40px;
    padding-top: 40px;
    padding-left: 20px;
    box-sizing: border-box;
    position: relative;
    > .group {
      width: 100%;
      position: relative;
      margin-top: -2px;
    }
    > div.end-group {
      margin-bottom: 110px;
    }
    .odd-group {
      .time-detail {
        position: absolute;
        left: 30px;
        right: 30px;
        /* top: -73px; */
        top: 8px;
      }
      .left-arrow {
        display: none;
      }
    }
    .even-group {
      .time-detail {
        position: absolute;
        left: 30px;
        right: 30px;
        top: 6px;
      }
      .right-arrow {
        display: none;
      }
    }

    .time-detail {
      .detail-c {
        width: 20%;
        float: left;
        position: relative;
        padding: 0 30px;
        box-sizing: border-box;
        .circle {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 1px solid #5260ff;
          position: absolute;
          z-index: 99;
          background: #ffffff;
          box-sizing: border-box;
          .circle-son {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #5260ff;
            margin: 2px;
            box-sizing: border-box;
          }
        }
      }
      .year-time {
        position: absolute;
        top: -30px;
        left: 30px;
        font-weight: 600;
        color: #333;
      }
    }

    .g-line {
      height: 2px;
      background: #5260ff;
      position: absolute;
      left: 60px;
      right: 60px;
      top: 0;
    }
    .odd-group {
      .circle {
        top: -13px;
      }
    }

    .even-group {
      .circle {
        top: -12px;
      }
    }

    .floatRight {
      .detail-c {
        float: right;
      }
    }
    .floatLeft {
      .detail-c {
        float: left;
      }
    }
    .detail-m {
      max-width: 100%;
      display: inline-block;
      box-sizing: border-box;
      h4 {
        font-weight: bold;
      }
      p {
        color: rgba(0, 0, 0, 0.75);
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 0;
        &:first-child {
          font-weight: 600;
        }
      }
    }

    .progress-arrow {
      width: 60px;
      border: 2px solid #5260ff;
      min-height: 132px;
      box-sizing: border-box;
      &.right-arrow {
        border-top-right-radius: 50px;
        border-bottom-right-radius: 50px;
        border-left: none;
        float: right;
      }
      &.left-arrow {
        border-top-left-radius: 50px;
        border-bottom-left-radius: 50px;
        border-right: none;
        float: left;
      }
    }
  }

  .table-container {
    /* display: flex; */
    padding-top: 16px;
    .table-title {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: rgba(0, 0, 0, 0.65);
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 8px;
      &::before {
        content: '';
        width: 6px;
        height: 16px;
        background: rgb(82, 96, 255);
        border-radius: 4px;
        margin-right: 8px;
      }
    }
    .summary-table {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #eee;
      margin-bottom: 16px;
      box-sizing: border-box;
      .item-row {
        width: 33.33%;
        .label {
          background-color: #eee;
          font-weight: 700;
          line-height: 30px;
          text-align: center;
        }
        .value {
          background-color: #fff;
          line-height: 30px;
          text-align: center;
          &.details {
            color: #5260ff;
            cursor: pointer;
          }
        }
      }
    }
    .annual-paper {
      display: flex;
      .paper {
        flex: 1;
      }
      .year-annual {
        flex: 1;
        margin-right: 8px;
      }
    }
    .rewards-punishments-train {
      display: flex;
      .rewards-punishments {
        flex: 1;
        margin-right: 8px;
      }
      .train {
        flex: 1;
      }
    }
  }
}
</style>
