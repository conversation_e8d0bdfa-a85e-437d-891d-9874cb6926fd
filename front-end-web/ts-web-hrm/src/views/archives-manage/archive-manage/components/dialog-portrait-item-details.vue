<template>
  <ts-dialog
    custom-class="dialog-portrait-item-details custom-dialog-title-styles"
    width="85%"
    title="详情"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="container" v-if="visible">
      <TsVxeTemplateTable
        id="table_portrait_item_details"
        ref="table"
        :hasPage="false"
        :columns="columns"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      visible: false,
      columns: []
    };
  },
  methods: {
    async open({ fieldsArr, tableData }) {
      this.columns = [
        {
          label: '序号',
          type: 'seq',
          align: 'center',
          width: 50
        },
        ...fieldsArr
      ];

      this.columns.forEach(f => {
        if (f.fieldType === 'file') {
          f.width = 300;
          f.render = (h, { column, row }) => {
            return row[column['field']]
              ? h('base-upload', {
                  props: {
                    onlyRead: true,
                    businessId: row[column['field']],
                    type: 'text'
                  }
                })
              : null;
          };
        }
      });

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.table.refresh({
          rows: tableData
        });
      });
    },

    close() {
      this.columns = [];
      this.$refs.table.refresh({
        rows: []
      });
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-portrait-item-details {
    .el-dialog__body {
      height: 400px;
      padding: 8px !important;
      margin: 8px !important;
      margin-bottom: 0px !important;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .container {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
      }
    }
  }
}
</style>
