<template>
  <ts-dialog
    custom-class="dialog-my-archives custom-dialog-title-styles"
    fullscreen
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="container" v-if="tableStr">
      <ts-button
        class="export-btn"
        :loading="exportLoading"
        type="primary"
        @click="handleExport"
      >
        导 出
      </ts-button>
      <div v-html="tableStr" id="EmpCollection"></div>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';

export default {
  components: {},
  data() {
    return {
      visible: false,
      title: '',
      employeeId: '',
      tableStr: '',
      exportLoading: false
    };
  },
  methods: {
    async open({ title, employeeId }) {
      this.title = title;
      this.employeeId = employeeId;
      await this.handleGetCusotmEmployeeGrxxcjb();

      this.visible = true;
    },

    handleExport() {
      this.exportLoading = true;

      const cells = document.querySelectorAll(
        '#EmpCollection #EmpCollectionFormTable tr td'
      );
      cells.forEach(cell => {
        cell.style.border = '1px solid #000';
        cell.style.textAlign = 'center';
      });

      const caijishijian = document.querySelector(
        '#EmpCollection #caijishijian'
      );
      if (caijishijian) caijishijian.style.textAlign = 'left';

      this.tableToExcel('EmpCollectionFormTable', '个人信息采集');
      setTimeout(() => {
        cells.forEach(cell => {
          cell.style.border = '0px solid #000';
        });
        if (caijishijian) caijishijian.style.textAlign = 'center';
        this.exportLoading = false;
      }, 1000);
    },

    tableToExcel(tableid, exprotName) {
      var uri = 'data:application/vnd.ms-excel;base64,',
        template =
          '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
        base64 = function(s) {
          return window.btoa(unescape(encodeURIComponent(s)));
        },
        dataFormat = function(s, c) {
          return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
          });
        };
      //根据ID获取table表格HTML
      var table = document.getElementById(tableid);
      var ctx = {
        worksheet: 'Worksheet',
        table: table.innerHTML
      };

      var alink = document.createElement('a');
      alink.href = uri + base64(dataFormat(template, ctx));
      alink.download = `${exprotName}.xls`;
      alink.click();
    },

    async handleGetCusotmEmployeeGrxxcjb() {
      try {
        let res = await this.ajax.customEmployeeBaseGrxxcjb(this.employeeId);
        if (!res.success) {
          this.$message.error(res.message || '获取人员信息失败,请联系管理员!');
          return false;
        }

        let data = cloneDeep(res.object || {});

        // 学习经历
        let lyzXuexi = data.lyzXuexi || [{}];
        let lyzXuexiTr = `<tr>
                            <td rowspan=${lyzXuexi.length + 1}>学习经历</td>
                            <td>学历</td>
                            <td>入学时间</td>
                            <td>毕业时间</td>
                            <td colspan="2">毕业院校</td>
                            <td>所学专业</td>
                            <td>学位</td>
                          </tr>`;

        lyzXuexi.forEach((item, index) => {
          let tr = '';
          tr += `
                <tr>
                    <td>${item.v1 || ''}</td>
                    <td>${item.v2 || ''}</td>
                    <td>${item.v3 || ''}</td>
                    <td colspan="2">${item.v4 || ''}</td>
                    <td>${item.v5 || ''}</td>
                    <td>${item.v6 || ''}</td>
                  </tr>
                  `;
          lyzXuexiTr += tr;
        });

        // 工作经历
        let lyzYuanwai = data.lyzYuanwai || [{}];
        let lyzYuannei = data.lyzYuannei || [{}];
        let workLength = lyzYuanwai.length + lyzYuannei.length;

        let lyzYuanwaiTr = '';
        lyzYuanwai.forEach((item, index) => {
          let tr = `<tr>
                      <td>${item.v1 || ''}</td>
                      <td>${item.v2 || ''}</td>
                      <td colspan="2">${item.v3 || ''}</td>
                      <td>${item.v4 || ''}</td>
                      <td>${item.v5 || ''}</td>
                    </tr>`;
          lyzYuanwaiTr += tr;
        });
        let lyzYuanneiTr = '';
        lyzYuannei.forEach((item, index) => {
          let tr = `<tr>
                      <td>${item.v1 || ''}</td>
                      <td>${item.v2 || ''}</td>
                      <td colspan="2">${item.v3 || ''}</td>
                      <td>${item.v4 || ''}</td>
                      <td>${item.v5 || ''}</td>
                    </tr>
                    `;
          lyzYuanneiTr += tr;
        });
        let workTr = `
                      <tr>
                        <td rowspan=${workLength + 2}>工作经历</td>
                        <td rowspan=${lyzYuanwai.length + 1}>外院任职经历</td>
                        <td>开始时间</td>
                        <td>结束时间</td>
                        <td colspan="2">工作单位</td>
                        <td>所在部门</td>
                        <td>岗位</td>
                      </tr>
                      ${lyzYuanwaiTr}
                      <tr>
                        <td rowspan=${lyzYuannei.length + 1}>本院工作经历</td>
                        <td>开始时间</td>
                        <td>结束时间</td>
                        <td colspan="2">工作单位</td>
                        <td>所在部门</td>
                        <td>岗位</td>
                      </tr>
                      ${lyzYuanneiTr}
                      `;

        // 职称简历
        let lyzZhicheng = data.lyzZhicheng || [{}];
        let lyzZhichengTr = `
                            <tr>
                              <td rowspan=${lyzZhicheng.length + 1}>
                                职称简历
                              </td>
                              <td>职称类别</td>
                              <td>职称取得时间</td>
                              <td colspan="3">职称名称</td>
                              <td colspan="2">专业名称</td>
                            </tr>
                            `;
        lyzZhicheng.forEach((item, index) => {
          let tr = '';
          tr += `
                <tr>
                  <td>${item.v1 || ''}</td>
                  <td>${item.v2 || ''}</td>
                  <td colspan="3">${item.v3 || ''}</td>
                  <td colspan="2">${item.v4 || ''}</td>
                </tr>
                  `;
          lyzZhichengTr += tr;
        });

        // 进修经历
        let lyzJinxiu = data.lyzJinxiu || [{}];
        let lyzJinxiuTr = `
                          <tr>
                            <td rowspan=${lyzJinxiu.length + 1}>进修经历</td>
                              <td colspan="2">进修开始时间</td>
                              <td colspan="2">进修结束时间</td>
                              <td colspan="3">进修医院</td>
                          </tr>
                        `;
        lyzJinxiu.forEach((item, index) => {
          let tr = '';
          tr += `
                <tr>
                  <td colspan="2">${item.v1 || ''}</td>
                  <td colspan="2">${item.v2 || ''}</td>
                  <td colspan="3">${item.v3 || ''}</td>
                </tr>
                `;
          lyzJinxiuTr += tr;
        });

        // 规培经历
        let lyzGuipei = data.lyzGuipei || [{}];
        let lyzGuipeiTr = `
                          <tr>
                            <td rowspan=${lyzGuipei.length + 1}>规培经历</td>
                            <td colspan="2">规培开始时间</td>
                            <td colspan="2">规培结束时间</td>
                            <td colspan="3">规培医院</td>
                          </tr>
                        `;
        lyzGuipei.forEach((item, index) => {
          let tr = '';
          tr += `
                <tr>
                  <td colspan="2">${item.v1 || ''}</td>
                  <td colspan="2">${item.v2 || ''}</td>
                  <td colspan="3">${item.v3 || ''}</td>
                </tr>
                `;
          lyzGuipeiTr += tr;
        });

        this.tableStr = `
          <table id="EmpCollectionFormTable">
            <thead>
              <tr>
                <td colspan="8">个人信息采集表</td>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>采集时间</td>
                <td colspan="7" id="caijishijian">
                  ${data.caijishijian || ''}
                </td>
              </tr>
              <tr>
                <td style="width: 12.5%;">姓名</td>
                <td style="width: 12.5%;">${data.employeeName || ''}</td>
                <td style="width: 12.5%;">性别</td>
                <td style="width: 12.5%;">${data.genderText || ''}</td>
                <td style="width: 12.5%;">民族</td>
                <td style="width: 12.5%;">${data.nationalityName || ''}</td>
                <td style="width: 12.5%;">出生年月</td>
                <td style="width: 12.5%;">${data.birthday || ''}</td>
              </tr>
              <tr>
                <td>科室</td>
                <td>${data.orgName || ''}</td>
                <td>最高学历</td>
                <td>${data.zuigaoxueli || ''}</td>
                <td>编制类型</td>
                <td>${data.establishmentTypeText || ''}</td>
                <td>年龄</td>
                <td>${data.empAge || ''}</td>
              </tr>
              <tr>
                <td>参加工作时间</td>
                <td>${data.workStartDate || ''}</td>
                <td>入院时间</td>
                <td>${data.entryDate || ''}</td>
                <td>职务</td>
                <td>${data.positionName || ''}</td>
                <td>岗位名称</td>
                <td>${data.personalIdentityName || ''}</td>
              </tr>
              <tr>
                <td>联系电话(长码)</td>
                <td>${data.phoneNumber || ''}</td>
                <td>政治面貌</td>
                <td>${data.politicalStatusText || ''}</td>
                <td>入党时间</td>
                <td>${data.partyDate || ''}</td>
                <td>现有职称</td>
                <td>${data.xianzyouzhicheng || ''}</td>
              </tr>
              <tr>
                <td>身份证</td>
                <td colspan="3">${data.idCard + ';' || ''}</td>
                <td>籍贯</td>
                <td colspan="3">${data.birthplace || ''}</td>
              </tr>
              <tr>
                <td>现住址</td>
                <td colspan="7" style="text-align: left;padding-left: 16px;">
                  ${data.address || ''}
                </td>
              </tr>
              ${lyzXuexiTr}
              ${workTr}
              ${lyzZhichengTr}
              ${lyzJinxiuTr}
              ${lyzGuipeiTr}
            </tbody>
          </table>
        `;
      } catch (error) {
        console.error(error);
      }
    },

    async close() {
      this.title = '';
      this.employeeId = '';
      this.tableStr = '';
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-my-archives {
    .el-dialog__header {
      padding: 8px !important;
    }
    .el-dialog__body {
      width: calc(100% - 16px) !important;
      min-height: calc(100vh - 96px) !important;
      max-height: calc(100vh - 96px) !important;
      padding: 8px !important;
      margin: 8px !important;
      margin-bottom: 0px !important;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .container {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        position: relative;
        .export-btn {
          position: fixed;
          right: 38px;
          top: 56px;
        }
      }
    }
    .el-dialog__footer {
      width: calc(100% - 16px) !important;
    }
  }
}
</style>

<style>
.dialog-my-archives #EmpCollection table {
  width: 88%;
  height: auto;
  border-right: 1px solid #8e8e8e !important;
}

.dialog-my-archives #EmpCollection table tbody tr {
  width: 100%;
  height: 32px;
}

.dialog-my-archives #EmpCollection table tr td,
.dialog-my-archives #EmpCollection table tr th {
  border-top: 1px solid #8e8e8e !important;
  border-left: 1px solid #8e8e8e !important;
  border-right: 1px solid #8e8e8e !important;
  border-bottom: 0 !important;
  text-align: center;
}

.dialog-my-archives #EmpCollection table tbody {
  height: 100%;
}

.dialog-my-archives #EmpCollection table thead {
  width: 100%;
  height: 55px;
}

.dialog-my-archives #EmpCollection table tbody tr:last-child {
  border-bottom: 1px solid #8e8e8e !important;
}
</style>
