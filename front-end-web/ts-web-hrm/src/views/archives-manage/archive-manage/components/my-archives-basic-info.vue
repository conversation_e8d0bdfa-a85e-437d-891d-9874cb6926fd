<template>
  <div class="basic-info-container">
    <img
      class="basic-info-avatar"
      id="avatar"
      :src="details.avatar"
      alt=""
      @click="() => handleReviewAvatar(details)"
    />
    <div class="left-info">
      <p class="top-name-img">
        <span class="emp-name" id="employeeName"
          >{{ details.employeeName }}
        </span>
        <span class="emp-status" id="employeeStatus"
          >{{ details.employeeStatus }}
        </span>
      </p>
      <ul>
        <li>
          <span class="label">科室：</span>
          <span class="value" id="orgName">{{ details.orgName }}</span>
        </li>
        <li>
          <span class="label">性别：</span>
          <span class="value" id="genderText">{{ details.genderText }}</span>
        </li>
        <li>
          <span class="label">年龄：</span>
          <span class="value" id="empAge">{{ details.empAge }}</span>
        </li>
        <li>
          <span class="label">政治面貌：</span>
          <span class="value" id="politicalStatusText">
            {{ details.politicalStatusText }}
          </span>
        </li>
        <li>
          <span class="label">入院日期：</span>
          <span class="value" id="entryDate">{{ details.entryDate }}</span>
        </li>
        <li>
          <span class="label">岗位：</span>
          <span class="value" id="personalIdentityName">
            {{ details.personalIdentityName }}
          </span>
        </li>
        <li>
          <span class="label">最高学历：</span>
          <span class="value" id="zuigaoxueli">{{ details.zuigaoxueli }}</span>
        </li>
        <li>
          <span class="label">专业：</span>
          <span class="value" id="zhuanye">{{ details.zhuanye }}</span>
        </li>
        <li>
          <span class="label">职务：</span>
          <span class="value" id="positionName">
            {{ details.positionName }}
          </span>
        </li>
        <li>
          <span class="label">联系电话：</span>
          <span class="value" id="phoneNumber">{{ details.phoneNumber }}</span>
        </li>
      </ul>
    </div>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    />
  </div>
</template>

<script>
export default {
  props: {
    details: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      previewFile: '',
      previewFileList: []
    };
  },
  methods: {
    handleReviewAvatar(row) {
      if (row.defaultAvatar) return;

      this.previewFile = location.origin + row.avatar;
      this.previewFileList = [location.origin + row.avatar];
      this.$nextTick(() => {
        this.$refs.preview.clickHandler();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.basic-info-container {
  display: flex;
  margin-bottom: 8px;
  min-height: 100px;
  img {
    width: 85px;
    height: 85px;
    border-radius: 50%;
    margin-right: 8px;
    cursor: pointer;
  }
  .left-info {
    flex: 1;
    .top-name-img {
      display: flex;
      align-items: center;
      margin-bottom: 0px;
      .emp-name {
        display: flex;
        font-size: 24px;
        color: #000;
        margin-right: 8px;
      }
      .emp-status {
        background-color: #5260ff;
        padding: 2px 8px;
        color: #fff;
        display: inline-block;
        border-radius: 8px;
        font-size: 12px;
      }
    }
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    li {
      width: 20%;
      margin-top: 8px;
      .label {
        font-size: 14px;
        color: #333;
      }
      .value {
        font-size: 14px;
        color: #000;
      }
    }
  }
}
</style>
