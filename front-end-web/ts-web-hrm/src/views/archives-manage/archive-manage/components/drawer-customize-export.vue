<template>
  <el-drawer
    custom-class="drawer-customize-export"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
    size="50%"
  >
    <template slot="title">
      <span class="dialog-title">
        自定义导出

        <el-tooltip
          class="item"
          effect="dark"
          content="勾选需要导出的字段或分组，此操作个人通用"
          placement="top-start"
        >
          <img class="item-tips" src="@/assets/img/dd_tips.svg" alt="" />
        </el-tooltip>
      </span>
    </template>

    <div class="form-container">
      <div class="content">
        <div class="class-fields-container">
          <div class="fields-head">
            <ts-checkbox @change="handleChangeFieldsCheckbox">
              个人信息
            </ts-checkbox>
          </div>

          <div class="fields-body">
            <div
              class="checkbox-item"
              v-for="item in fieldsList"
              :key="item.id"
              :title="item.showName"
            >
              <ts-checkbox
                v-model="item.setValue"
                :true-label="1"
                :false-label="0"
                :disabled="item.disabled === 1"
              >
                {{ item.showName }}
              </ts-checkbox>
            </div>
          </div>
        </div>

        <div class="class-fields-container">
          <div class="fields-head">
            <ts-checkbox @change="handleChangeGroupsCheckbox">
              其他分组信息
            </ts-checkbox>
          </div>

          <div class="fields-body">
            <div
              class="checkbox-item"
              v-for="item in groupList"
              :key="item.id"
              :title="item.groupName"
            >
              <ts-checkbox
                v-model="item.setValue"
                :true-label="1"
                :false-label="0"
              >
                {{ item.groupName }}
              </ts-checkbox>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer-footer flex-end">
        <ts-button :loading="submitLoading" type="primary" @click="submit">
          确 定
        </ts-button>
        <ts-button @click="close">取 消</ts-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { customizeHeadItem } from '@/views/archives-manage/config/dictionary.js';
export default {
  components: {},
  data() {
    return {
      visible: false,
      submitLoading: false,

      fieldsList: [],
      groupList: [],
      archivesType: null,
      fieldType: '3',
      groupType: '4'
    };
  },
  methods: {
    async open({ archivesType }) {
      this.fieldsList = [];
      this.groupList = [];
      this.visible = true;
      this.archivesType = archivesType;
      this.$nextTick(() => {
        this.handleGetPersonDetailsFieldList();
      });
    },

    handleChangeFieldsCheckbox(result) {
      this.fieldsList.forEach(f => {
        f.setValue = Number(result);
      });
    },

    handleChangeGroupsCheckbox(result) {
      this.groupList.forEach(f => {
        f.setValue = Number(result);
      });
    },

    async handleGetPersonDetailsFieldList() {
      try {
        const [fieldsRes, groupRes] = await Promise.all([
          this.ajax.customEmployeeFieldHeadGetListByType({
            type: this.fieldType
          }),
          this.ajax.customEmployeeFieldHeadGetGroupList({
            archivesType: this.archivesType
          })
        ]);
        if (!fieldsRes.success) {
          this.$message.error(
            fieldsRes.message || '获取字段失败,请联系管理员!'
          );
          return;
        }
        this.fieldsList = cloneDeep(fieldsRes.object || []);
        this.fieldsList.forEach(f => {
          if (customizeHeadItem.includes(f.fieldName)) {
            f.disabled = 1;
            f.setValue = 1;
          }
        });

        if (!groupRes.success) {
          this.$message.error(groupRes.message || '获取分组失败,请联系管理员!');
          return;
        }
        this.groupList = cloneDeep(groupRes.object || []);
      } catch (error) {}
    },

    async submit() {
      try {
        this.submitLoading = true;

        const formatSubmitData = (list, type) =>
          list.map(m => {
            let data = {
              groupId: m.groupId || m.id,
              setType: type,
              setValue: m.setValue
            };
            if (type == '3') data.fieldId = m.id;
            return data;
          });

        let hasFieldCheckExport = this.fieldsList.some(s => s.setValue === 1);
        if (hasFieldCheckExport) {
          this.groupList.unshift({
            id: '1',
            setValue: 1
          });
        }

        const [fieldData, groupData] = [
          formatSubmitData(this.fieldsList, this.fieldType),
          formatSubmitData(this.groupList, this.groupType)
        ];

        const [fieldRes, groupRes] = await Promise.all([
          this.ajax.customEmployeeFieldHeadSaveFieldHead(fieldData),
          this.ajax.customEmployeeFieldHeadSaveFieldHead(groupData)
        ]);

        if (!fieldRes.success || !groupRes.success) {
          this.$message.error(
            fieldRes.message || groupRes.message || '操作失败!'
          );
          return false;
        }

        this.$message.success('操作成功!');
        this.$emit('export');
        this.close();
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .drawer-customize-export {
    height: 100%;
    overflow: hidden;
    .el-drawer__header {
      margin-bottom: 8px;
      padding: 8px 8px 0px;

      .item-tips {
        width: 20px;
        height: 20px;
        transform: translateY(-2px);
      }

      .dialog-title {
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 4px;
      }
      .el-drawer__close-btn {
        position: relative;
        top: -9px;
      }
    }
    .el-drawer__body {
      padding: 0 10px 10px;
      overflow: hidden;

      .form-container {
        display: flex;
        flex-direction: column;
        overflow: auto;
        width: 100%;
        height: 100%;
        margin: 0 auto;

        .content {
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          .class-fields-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: auto;
            .fields-head {
              height: 30px;
              line-height: 30px;
              padding-left: 8px;
              background-color: #eee;
              border-radius: 4px;
            }
            .fields-body {
              display: flex;
              flex-wrap: wrap;
              padding-bottom: 8px;
              .checkbox-item {
                width: 25%;
                padding-right: 12px;
                margin-top: 4px;
                cursor: pointer;
                overflow: hidden;
                &:nth-child(4n) {
                  padding-right: 0;
                }

                .ts-checkbox {
                  width: 100%;
                  border: 1px solid #eee;
                  padding: 2px 4px;
                  overflow: hidden;
                  display: flex;
                  align-items: center;

                  .el-checkbox__label {
                    display: inline-block;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }

                  .el-checkbox__input.is-checked + .el-checkbox__label {
                    color: $primary-blue !important;
                  }
                }
              }
            }
          }
        }
      }
    }
    .drawer-footer {
      padding-top: 8px;
    }
  }
}
</style>
