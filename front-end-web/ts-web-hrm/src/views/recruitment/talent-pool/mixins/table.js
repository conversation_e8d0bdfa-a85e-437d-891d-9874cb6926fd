export default {
  data() {
    return {
      searchForm: {},
      actions: [
        {
          label: '新增',
          prop: { type: 'primary' },
          click: this.handleAdd
        },
        {
          label: '设置',
          click: this.handleSetting
        },
        {
          label: '导出',
          click: this.handleExport
        }
      ],

      searchList: [
        {
          label: '',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '姓名'
          }
        },
        {
          label: '人才类型',
          value: 'rclx',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        }
      ],

      columns: [
        {
          type: 'selection',
          align: 'center',
          width: 50
        },
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'name',
          width: 130,
          align: 'center',
          formatter: row => {
            let showTurnIcon = row.isAddemp === '1';
            return (
              <span
                onClick={() => {
                  this.handleDetails(row);
                }}
                class="details-span">
                {showTurnIcon && <span class="turn-span">转</span>}
                {row.name}
              </span>
            );
          }
        },
        {
          label: '身份证号码',
          prop: 'tableData.identityCard',
          width: 160,
          align: 'center'
        },
        {
          label: '性别',
          prop: 'tableData.gender',
          width: 80,
          align: 'center'
        },
        {
          label: '年龄',
          prop: 'age',
          width: 80,
          align: 'center'
        },
        {
          label: '学历',
          prop: 'educationLable',
          width: 80,
          align: 'center'
        },
        {
          label: '全日制',
          prop: 'fullTime',
          width: 80,
          align: 'center'
        },
        {
          label: '应聘职位',
          width: 150,
          prop: 'postName',
          align: 'center'
        },
        {
          label: '人才类型',
          prop: 'rclxLable',
          width: 120,
          align: 'center'
        },
        {
          label: '笔试成绩',
          prop: 'examScore',
          align: 'center',
          formatter: row => {
            // class="details-span"
            return (
              <span
                onClick={() => {
                  this.handleExamDetails(row);
                }}>
                {row.examScore}
              </span>
            );
          }
        },
        {
          label: '是否通过',
          prop: 'examStatus',
          align: 'center',
          formatter: row => {
            const label =
              row.examStatus == '2'
                ? '通过'
                : row.examStatus == '1'
                ? '不通过'
                : '';
            return label;
          }
        },
        {
          label: '面试成绩',
          prop: 'interviewScore',
          align: 'center'
        },
        {
          label: '是否通过',
          prop: 'interviewStatus',
          align: 'center',
          formatter: row => {
            const label =
              row.interviewStatus == '2'
                ? '通过'
                : row.interviewStatus == '1'
                ? '不通过'
                : '';
            return label;
          }
        },
        {
          label: '实操成绩',
          prop: 'manipulateScore',
          align: 'center'
        },
        {
          label: '是否通过',
          prop: 'manipulateStatus',
          align: 'center',
          formatter: row => {
            const label =
              row.manipulateStatus == '2'
                ? '通过'
                : row.manipulateStatus == '1'
                ? '不通过'
                : '';
            return label;
          }
        },
        {
          label: '是否应届',
          prop: 'freshGraduate',
          align: 'center'
        },
        {
          label: '联系方式',
          prop: 'tableData.iphone',
          width: 120,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let infoBtn = [
              {
                label: '录用信息',
                event: this.handleEmploymentInformation
              }
            ];
            if (row.isAddemp === '1') {
              infoBtn = [];
            }
            let arr = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              ...infoBtn,
              {
                label: '删除',
                event: this.handleDel
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      this.selectList = [];
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 'create_date',
          sord: 'desc'
        };
      let res = await this.ajax.getTalentpoolTableList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          tableData: JSON.parse(item.otherData),
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
