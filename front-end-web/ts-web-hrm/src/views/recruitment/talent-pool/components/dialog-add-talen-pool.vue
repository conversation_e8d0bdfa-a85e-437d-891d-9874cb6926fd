<template>
  <ts-dialog
    class="dialog-add-talen-pool"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <component
        ref="component"
        :is="formTemplate"
        :talenPoolList="talenPoolList"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import cscjkForm from '@/components/recruit-template/cscjk-form.vue';

export default {
  components: {
    cscjkForm
  },
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    eachData: {
      type: Object
    },
    formTemplate: {
      type: String
    },
    talenPoolList: {
      type: Array
    },
    postId: {
      type: String
    },
    planId: {
      type: String
    },
    successCallBack: {
      type: Function
    }
  },
  data() {
    return {
      visible: false,

      title: '',
      type: ''
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (JSON.stringify(this.eachData) === '{}') {
          this.type = 'add';
          this.title = '新增';

          this.$nextTick(() => {
            this.$refs.component.form = {
              studyCareer: [],
              workCareer: [],
              trainCareer: [],
              titleCareer: [],
              familyCareer: []
            };
          });
        } else {
          this.type = 'edit';
          this.title = '编辑';

          this.$nextTick(() => {
            this.$refs.component.form = this.eachData;
          });
        }

        this.visible = val;
        this.$nextTick(() => {
          this.$refs.component.clear();
        });
      }
    }
  },
  methods: {
    async submit() {
      try {
        const data = await this.$refs.component.submit();

        if (!data) {
          throw '表单必填项未填写!';
        }

        data.talentPool = '1';

        if (this.postId) {
          data.postId = this.postId;
        }
        if (this.planId) {
          data.planId = this.planId;
        }

        let API = null;
        if (this.type === 'add') {
          API = this.ajax.userJKSignUpApi;
        } else {
          API = this.ajax.talentpoolUpdate;
        }

        const res = await API(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.successCallBack();
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-talen-pool {
  ::v-deep {
    .el-dialog__body {
      width: 1366px !important;
    }

    ::-webkit-scrollbar {
      width: 6px;
      height: 8px;
      border-radius: 8px;
      -webkit-border-radius: 8px;
    }

    ::-webkit-scrollbar-track-piece {
      background-color: #ffff;
      border-radius: 8px;
      -webkit-border-radius: 8px;
    }

    ::-webkit-scrollbar-thumb:vertical {
      height: 8px;
      border-radius: 8px;
      -webkit-border-radius: 8px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }

    ::-webkit-scrollbar-thumb:horizontal {
      width: 6px;
      border-radius: 8px;
      -webkit-border-radius: 8px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }

    .el-dialog__body {
      overflow: auto;
    }
  }
}
</style>
