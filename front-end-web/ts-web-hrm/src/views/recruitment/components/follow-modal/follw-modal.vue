<template>
  <ts-dialog title="跟踪" :visible.sync="insideVisible" type="large">
    <div
      class="model-content"
      v-if="followUserList && followUserList.length > 0"
    >
      <div class="user-list">
        <el-scrollbar
          ref="scroll"
          style="height: 100%;"
          wrap-class="options-scrollbar"
          wrap-style="overflow-x: hidden;"
        >
          <ul>
            <li
              v-for="user of followUserList"
              :key="user.zpglempid"
              :class="{
                active: activeUser.zpglempid == user.zpglempid
              }"
            >
              <div
                class="follow-user-item"
                @click="handleCheckFollowUser(user)"
              >
                <div class="top-content">
                  <div class="user-name">
                    <div class="title">
                      <div class="add-type" v-show="user.employeeStatusText">
                        {{ user.employeeStatusText }}
                      </div>
                      {{ user.employeeName }}
                      <span>({{ user.gendertext }})</span>
                    </div>
                  </div>
                  <div class="job-name">
                    {{ user.studyoutJobtext }}
                  </div>
                </div>
                {{ user.iphone }}
              </div>
            </li>
          </ul>
        </el-scrollbar>
      </div>
      <div class="form-content">
        <ts-form ref="form" :model="form" labelWidth="100px">
          <ts-form-item
            label="跟踪结果"
            prop="traceStatus"
            :rules="requiredRow"
          >
            <ts-radio-group v-model="form.traceStatus">
              <ts-radio :label="1">继续跟踪</ts-radio>
              <ts-radio :label="2">结束跟踪</ts-radio>
            </ts-radio-group>
          </ts-form-item>
          <ts-form-item
            :label="form.traceStatus == 2 ? '原因' : '跟踪内容'"
            prop="traceResult"
            :rules="requiredRow"
          >
            <ts-input
              v-model="form.traceResult"
              type="textarea"
              rows="6"
              resize="none"
              maxlength="100"
              placeholder="请输入"
              show-word-limit
            ></ts-input>
          </ts-form-item>
        </ts-form>
      </div>
    </div>
    <div class="tips-title" v-else>
      您今日暂无跟踪人员~
    </div>

    <template slot="footer">
      <ts-button
        v-if="followUserList && followUserList.length > 0"
        type="primary"
        @click="handleSave"
        >保存</ts-button
      >
      <ts-button @click="handleCancel">取消</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

import {
  getZpglTraceResultAllList,
  zpglTraceResultSave
} from '@/api/ajax/NewRecruit/interview.js';
export default {
  props: {
    visible: Boolean
  },
  data() {
    return {
      insideVisible: false,

      activeUser: {},
      followUserList: [],

      form: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  watch: {
    visible(newVal) {
      this.insideVisible = newVal || false;
      if (newVal) {
        this.$set(this, 'form', {});
        this.getFollowUserList();
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      }
    },
    insideVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    async getFollowUserList() {
      let res = await getZpglTraceResultAllList();
      this.followUserList = res.rows || [];
    },
    handleCheckFollowUser(user) {
      this.activeUser = user;
    },
    async handleSave() {
      if (!this.activeUser.zpglempid) {
        this.$message.warning('请选择需要跟踪的数据！');
        return;
      }
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        data.zpglempid = this.activeUser.zpglempid;

        const res = await zpglTraceResultSave(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');

          this.getFollowUserList();
          this.activeUser = {};
          this.form = {};
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    handleCancel() {
      this.insideVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.tips-title {
  text-align: center;
  margin-top: 10px;
}
.model-content {
  display: flex;
  overflow: hidden;
  .user-list {
    width: 270px;
    height: 400px;
    border-right: 1px solid #eee;
    li {
      padding: 0 10px;
      .title {
        display: flex;
        align-items: center;
        .add-type {
          padding: 4px 8px;
          background: #479ad0 !important;
          color: #fff;
          margin-right: 8px;
          border-radius: 4px;
        }
      }
      &.active {
        background-color: $list-hover-color;
        .user-name {
          color: $primary-blue;
        }
      }
      &:not(:last-child) .follow-user-item {
        border-bottom: 1px solid #eee;
      }
    }

    .follow-user-item {
      cursor: pointer;
      padding: $primary-spacing 0;
      color: $primary-black;
      .top-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        .user-name {
          font-weight: 600;
          span {
            font-weight: 400;
            color: $primary-light-black;
          }
        }
      }
    }
  }
  .form-content {
    flex: 1;
  }
}
</style>
