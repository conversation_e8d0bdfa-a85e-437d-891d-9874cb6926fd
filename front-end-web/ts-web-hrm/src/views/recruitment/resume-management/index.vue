<template>
  <div class="resume-management">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <!-- <template v-slot:applicantPost>
        <ts-ztree-select
          ref="treeSelect"
          :inpText.sync="searchForm.allPath"
          :inpVal.sync="searchForm.applicantPost"
          :data="treeData"
          defaultExpandAll
          @before-change="handleTreeBeforeChange"
          @change="handleChangeTree"
          @clear="handleClearTree"
        />
      </template> -->
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :propPageSize="40"
      :pageSizes="[20, 40, 60, 80, 100, 200]"
      v-loading="loading"
      :columns="columns(false)"
      @refresh="handleRefreshTable"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-set-recruit-info
      ref="DialogSetRecruitInfo"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-interview-evaluation
      ref="DialogInterviewEvaluation"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-change
      ref="DialogChange"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />

    <dialog-add-talent-pool
      ref="DialogAddTalentPool"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogSetRecruitInfo from '@/views/recruitment/components/dialog-set-recruit-info/dialog-set-recruit-info.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogAddTalentPool from '@/views/recruitment/components/dialog-add-talent-pool/dialog-add-talent-pool.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';
import DialogChange from '@/views/recruitment/induction-management/dialog/dialog-change.vue';
import DialogInterviewEvaluation from '@/views/recruitment/interview-management/component/dialog-interview-evaluation.vue';

import { getZpglEmployeeList } from '@/api/ajax/NewRecruit/resume';
import { talentPoolTemplateTree } from '@/api/ajax/NewRecruit/template.js';

export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    DialogAdd,
    DialogSetRecruitInfo,
    DialogPersonDetails,
    DialogChange,
    DialogTrack,
    DialogInterviewEvaluation,
    DialogAddTalentPool
  },
  data() {
    return {
      actions: [
        {
          label: '录入简历',
          prop: { type: 'primary' },
          click: this.handleAddOrEditPerson
        }
      ],

      treeData: []
    };
  },
  async created() {
    this.handleGetTreeData();
  },
  methods: {
    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    async handleGetTreeData() {
      // 获取人才库岗位
      const res = await talentPoolTemplateTree();
      if (res.success == false) {
        this.$message.error(res.message || '获取人才库岗位失败');
        return;
      }
      this.treeData = res.object;
    },
    // handleChangeTree(e) {
    //   const paths = e.getPath();
    //   const names = paths.map(item => item.name) || [];
    //   let allPath = names.join('>');
    //   const { id, name } = e;

    //   this.searchForm.allPath = allPath;
    //   this.searchForm.applicantPost = id;
    //   this.searchForm.applicantPosttext = name;
    // },
    // handleTreeBeforeChange(e, treeObj, fn) {
    //   if (e.children && e.children.length > 0) {
    //     fn(false);
    //     return false;
    //   }
    // },
    // handleClearTree() {},
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.searchForm.searchStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.searchEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      delete this.searchForm.date;

      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 't1.create_date',
          sord: 'desc'
        };

      searchForm.interviewResultStatusList = (
        searchForm.interviewResultStatusList || []
      ).join(',');

      searchForm.personnelCategoryList = (
        searchForm.personnelCategoryList || []
      ).join(',');

      let res = await getZpglEmployeeList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.resume-management {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  @import url('../styles/recruitment-table.css');
}
</style>
