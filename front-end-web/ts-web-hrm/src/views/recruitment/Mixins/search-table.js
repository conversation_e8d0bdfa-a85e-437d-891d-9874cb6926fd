export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      searchList: [
        // {
        //   label: '岗位',
        //   value: 'applicantPost'
        // },
        {
          label: '姓名',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名'
          }
        },
        {
          label: '岗位',
          value: 'allPath',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入岗位'
          }
        },
        {
          label: '创建时间',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            type: 'daterange',
            valueFormat: 'YYYY-MM-DD',
            rangeSeparator: '至',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期'
          }
        },
        {
          label: '职称',
          value: 'zhichengmingcheng',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '学历',
          value: 'xueli',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '专业',
          value: 'zhuanye',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入专业'
          }
        },
        {
          label: '毕业院校',
          value: 'biyeyuanxiao',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入毕业院校'
          }
        },
        {
          label: '性别',
          value: 'gender',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '初审结果',
          value: 'conform',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '未筛选', value: '3', element: 'ts-option' },
            { label: '通过', value: '1', element: 'ts-option' },
            { label: '不通过', value: '2', element: 'ts-option' }
          ]
        },
        {
          label: '面试结果',
          value: 'interviewResultStatusList',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: [
            { label: '符合', value: '1', element: 'ts-option' },
            { label: '不符合', value: '2', element: 'ts-option' },
            { label: '待定', value: '3', element: 'ts-option' },
            { label: '未面试', value: '5', element: 'ts-option' }
          ]
        },
        {
          label: '是否入职',
          value: 'zpglEmployeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '已入职', value: '4', element: 'ts-option' },
            { label: '未入职', value: '3', element: 'ts-option' }
          ]
        },
        {
          label: '员工类别',
          value: 'personnelCategoryList',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        }
      ],
      interviewColumns: [
        {
          label: '面试时间',
          prop: 'interviewDate',
          align: 'center',
          width: 115
        },
        {
          label: '是否签到',
          prop: 'signInText',
          align: 'center',
          width: 75
        },
        {
          label: '签到时间',
          prop: 'signInDate',
          align: 'center',
          width: 115
        }
      ],
      inductionColumns: [
        {
          label: '面试通过时间',
          prop: 'interviewDate',
          align: 'center',
          width: 150
        },
        {
          label: '入职时间',
          prop: 'comeEntryDate',
          align: 'center',
          width: 90
        }
      ]
    };
  },
  computed: {
    columns() {
      return (
        showSelection = false,
        afterColumns = [],
        postColumns = {
          label: '应聘岗位',
          prop: 'interviewPath'
        }
      ) => {
        let columns = [
          {
            label: '序号',
            type: 'index',
            align: 'center',
            fixed: 'left'
          },
          {
            label: '操作',
            align: 'center',
            width: 100,
            fixed: 'left',
            headerSlots: 'action',
            formatter: row => {
              let { zpglEmployeeStatusText: status, addTalentPool } = row;
              let arr = [
                {
                  label: '编辑',
                  event: this.handleAddOrEditPerson,
                  show: status != '已入职'
                },
                {
                  label: '加入人才库',
                  event: this.handleAddTalentPool,
                  show: status != '已入职' && addTalentPool != '1'
                },
                {
                  label: '跟踪',
                  show: status != '已入职',
                  event: this.handleTrack
                },
                {
                  label: '删除',
                  event: this.handleDelete,
                  show: status != '已入职'
                },
                {
                  label: '简历筛选',
                  event: data => {
                    this.handleSetRecruitInfo(data, 'set');
                  },
                  show:
                    ['待筛选', '已终止'].includes(status) &&
                    addTalentPool != '1'
                },
                {
                  label: '修改筛选信息',
                  event: data => {
                    this.handleSetRecruitInfo(data, 'edit');
                  },
                  show:
                    ['筛选不通过', '待面试'].includes(status) &&
                    addTalentPool != '1'
                },
                {
                  label: '进入待面试',
                  event: data => {
                    this.handleContinueInterview(data);
                  },
                  show: ['面试待定'].includes(status)
                },
                {
                  label: '面试评价',
                  event: data => {
                    this.handleEvaluate(data, 'set');
                  },
                  show: ['待面试'].includes(status) && addTalentPool != '1'
                },
                {
                  label: '修改面试评价',
                  event: data => {
                    this.handleEvaluate(data, 'edit');
                  },
                  show:
                    ['面试待定', '面试通过', '面试不通过'].includes(status) &&
                    addTalentPool != '1'
                },
                {
                  label: '进入待入职',
                  event: data => {
                    this.handleSubmit(data);
                  },
                  show: [
                    '待筛选',
                    '已终止',
                    '待面试',
                    '面试待定',
                    '面试通过'
                  ].includes(status)
                },
                {
                  label: '修改入职时间',
                  event: data => {
                    this.handleChange(data);
                  },
                  show: ['待入职'].includes(status) && addTalentPool != '1'
                },
                {
                  label: '入职归档',
                  event: data => {
                    this.handleKeepOnFile(data);
                  },
                  show: [
                    '待筛选',
                    '已终止',
                    '待面试',
                    '面试待定',
                    '面试通过',
                    '待入职'
                  ].includes(status)
                }
              ];
              return (
                <BaseActionCell
                  actions={arr.filter(f => f.show)}
                  on={{ 'action-select': e => e(row) }}
                />
              );
            }
          },
          {
            label: postColumns.label,
            prop: postColumns.prop,
            align: 'center',
            minWidth: 225
          },
          {
            label: '姓名',
            prop: 'employeeName',
            width: 70,
            align: 'center',
            formatter: row => {
              return (
                <span
                  class="details-span"
                  onClick={() => {
                    this.handleDetails(row);
                  }}>
                  {row.employeeName}
                </span>
              );
            }
          },
          {
            label: '性别',
            prop: 'gendertext',
            width: 45,
            align: 'center'
          },
          {
            label: '年龄',
            prop: 'age',
            width: 45,
            align: 'center'
          },
          {
            label: '籍贯',
            prop: 'birthplace',
            minWidth: 120,
            align: 'center'
          },
          {
            label: '学历',
            prop: 'xuelitext',
            align: 'center',
            width: 70
          },
          {
            label: '职称专业',
            prop: 'zhuenye',
            align: 'center',
            minWidth: 90
          },
          {
            label: '学校',
            prop: 'biyeyuanxiao',
            width: 105,
            align: 'center'
          },
          {
            label: '职称',
            prop: 'zhichengmingchengtext',
            minWidth: 75,
            align: 'center'
          },
          {
            label: '工作单位',
            prop: 'workUnit',
            minWidth: 85,
            align: 'center'
          },
          {
            label: '联系电话',
            prop: 'iphone',
            align: 'center',
            width: 110
          },
          {
            label: '创建时间',
            prop: 'createDate',
            align: 'center',
            width: 95
          },
          {
            label: '初审结果',
            prop: 'conform',
            width: 80,
            align: 'center',
            formatter: row => {
              let obj = {
                1: {
                  label: '通过',
                  className: 'label success'
                },
                2: {
                  label: '不通过',
                  className: 'label pengding'
                },
                3: {
                  label: '未筛选',
                  className: 'label'
                }
              };
              const label = obj[row.conform || '3'].label;
              const className = obj[row.conform || '3'].className;
              return <div class={className || ''}>{label}</div>;
            }
          },
          {
            label: '面试结果',
            prop: 'interviewResultStatus',
            width: 80,
            align: 'center',
            formatter: row => {
              let obj = {
                1: {
                  label: '通过',
                  className: 'label success'
                },
                2: {
                  label: '不通过',
                  className: 'label pengding'
                },
                3: {
                  label: '待定',
                  className: 'label error'
                }
              };
              const label = obj[row.interviewResultStatus]?.label;
              const className = obj[row.interviewResultStatus]?.className;
              return <div class={className || ''}>{label || '未面试'}</div>;
            }
          },
          {
            label: '是否入职',
            prop: 'zpglEmployeeStatus',
            width: 75,
            align: 'center',
            formatter: row => {
              let val = row.zpglEmployeeStatus === '4' ? '4' : '0';
              let obj = {
                4: {
                  label: '是',
                  className: 'label success'
                },
                0: {
                  label: '否',
                  className: 'label pengding'
                }
              };
              const label = obj[val]?.label;
              const className = obj[val]?.className;
              return <div class={className || ''}>{label || '-'}</div>;
            }
          },
          ...afterColumns,
          {
            label: '当前状态',
            prop: 'zpglEmployeeStatusText',
            minWidth: 85,
            align: 'center'
          },
          {
            label: '最近操作时间',
            prop: 'updateDate',
            width: 95,
            align: 'center'
          }
        ];

        if (showSelection) {
          let selection = {
            type: 'selection',
            align: 'center',
            width: 55
          };

          columns.unshift(selection);
        }
        return columns;
      };
    }
  }
};
