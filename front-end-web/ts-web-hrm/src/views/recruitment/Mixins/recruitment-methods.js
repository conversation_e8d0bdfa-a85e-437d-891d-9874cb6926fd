import { deepClone } from '@/unit/commonHandle.js';

import { zpglInterviewMessageInfo } from '@/api/ajax/NewRecruit/interview.js';
import { deleteZpglEmployeeListDetails } from '@/api/ajax/NewRecruit/resume';
import { getZpglInterviewResult } from '@/api/ajax/NewRecruit/interview.js';
import { zpglInterviewResultContinue } from '@/api/ajax/NewRecruit/interview.js';
import { zpglInterviewResultSubmit } from '@/api/ajax/NewRecruit/interview';
import { zpglInterviewResultEntry } from '@/api/ajax/NewRecruit/induction.js';

export default {
  data() {
    return {
      dialogAddBoolean: false,
      addEachData: {}
    };
  },
  methods: {
    // 跟踪
    handleTrack(row) {
      this.$refs.DialogTrack.open({
        data: {
          zpglempid: row.zpglempid
        }
      });
    },

    // 加入人才库
    handleAddTalentPool(row) {
      let pathKey = '';
      switch (row.zpglEmployeeStatusText) {
        case '待筛选':
          pathKey = 'interviewPath';
          break;
        case '筛选不通过':
        case '待面试':
          pathKey = 'msgInterviewPath';
          break;
        case '面试通过':
        case '面试不通过':
        case '面试待定':
        case '待入职':
          pathKey = 'resInterviewPath';
          break;
      }
      this.$refs.DialogAddTalentPool.open({
        data: {
          oldAllPath: row[pathKey],
          zpglempid: row.zpglempid
        }
      });
    },

    // 设置面试信息
    async handleSetRecruitInfo(row, type) {
      let data = null;
      if (type == 'set') {
        data = {
          zpglempid: row.zpglempid,
          allPath: row.interviewPath || '',
          interviewDept: row.applicantDept || '',
          interviewDepttext: row.applicantDeptname || '',
          interviewJob: row.applicantPost || '',
          interviewJobtext: row.applicantPosttext || ''
        };
      }
      if (type == 'edit') {
        const res = await zpglInterviewMessageInfo(row.messageId);
        if (res.success == true) {
          let details = res.object;
          data = {
            id: row.messageId,
            allPath: details.msgInterviewPath,
            zpglempid: details.zpglempid,
            conform: details.conform,
            interviewDept: details.interviewDept,
            interviewDepttext: details.interviewDepttext,
            interviewJob: details.interviewJob,
            interviewJobtext: details.interviewJobtext,
            writtenEmpNo: details.writtenEmpNo,
            writtenName: details.writtenName,
            writtenDate: details.writtenDate,
            operationEmpNo: details.operationEmpNo,
            operationName: details.operationName,
            operationDate: details.operationDate,
            interviewEmpNo: details.interviewEmpNo,
            interviewName: details.interviewName,
            interviewDate: details.interviewDate,
            remark: details.remark
          };
        }
      }
      this.$refs.DialogSetRecruitInfo.open({
        data,
        type
      });
    },

    // 设置面试评价
    async handleEvaluate(row = {}, setType) {
      let object = {};
      let data = {};
      data.zpglempid = row.zpglempid;
      data.interviewResultStatus = row.interviewResultStatus || '';

      if (setType == 'set') {
        data.allPath = row.msgInterviewPath;
        data.studyoutDept = row.interviewDept;
        data.studyoutDepttext = row.interviewDepttext;
        data.studyoutJob = row.interviewJob;
        data.studyoutJobtext = row.interviewJobtext;
      }

      if (setType == 'edit') {
        data.allPath = row.resInterviewPath;
        data.studyoutDept = row.studyoutDept;
        data.studyoutDepttext = row.studyoutDepttext;
        data.studyoutJob = row.studyoutJob;
        data.studyoutJobtext = row.studyoutJobtext;

        if (row.resultId) {
          const res = await getZpglInterviewResult(row.resultId);
          if (!res.success) {
            this.$message.error(res.message || '操作失败');
            return false;
          }
          object = {
            resultId: row.resultId,
            writtenResult: res.object.writtenResult || '',
            operationResult: res.object.operationResult || '',
            interviewResult: res.object.interviewResult || '',
            entryDate: res.object.entryDate || '',
            remark: res.object.remark || ''
          };
        }
      }

      this.$refs.DialogInterviewEvaluation.open({
        data: Object.assign(data, object),
        title: '面试评价',
        setType
      });
    },

    // 进入待面试
    async handleContinueInterview(row) {
      try {
        await this.$confirm('该面试者是否继续面试？', '提示', {
          type: 'warning'
        });
        let data = {
          interviewPath: row.resInterviewPath,
          zpglempid: row.zpglempid,
          resultId: row.resultId,
          messageId: row.messageId
        };
        const res = await zpglInterviewResultContinue(data);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          if (this.handleRefreshTable) {
            this.handleRefreshTable();
          } else {
            this.handleChangePersonListData();
          }
        } else {
          this.$message.error(res.message || '操作失败');
          return false;
        }
      } catch (e) {
        console.error(e);
        return false;
      }
    },

    // 进入待入职
    async handleSubmit(row) {
      const { zpglempid } = row;
      try {
        await this.$confirm(
          '提交后该面试者进入待入职，是否确定提交？',
          '提示',
          {
            type: 'warning'
          }
        );
        const res = await zpglInterviewResultSubmit(zpglempid);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          if (this.handleRefreshTable) {
            this.handleRefreshTable();
          } else {
            this.handleChangePersonListData();
          }
        } else {
          this.$message.error(res.message);
          return false;
        }
      } catch (e) {
        console.error(e);
        return false;
      }
    },

    // 修改入职时间
    handleChange(row) {
      let data = {
        allPath: row.resInterviewPath,
        resultId: row.resultId || row.zpglempid,
        entryDate: row.comeEntryDate,
        studyoutDept: row.studyoutDept,
        studyoutDepttext: row.studyoutDepttext,
        studyoutJob: row.studyoutJob,
        studyoutJobtext: row.studyoutJobtext
      };
      this.$refs.DialogChange.open({
        data
      });
    },

    //入职归档
    async handleKeepOnFile(row) {
      try {
        await this.$confirm(
          '存档后，该人员信息进入人员档案，确定吗？',
          '存档',
          {
            type: 'warning'
          }
        );
        const { zpglempid } = row;
        const res = await zpglInterviewResultEntry(zpglempid);
        if (res.success == false) {
          this.$message.error(res.message || '操作失败!');
          return false;
        }
        this.$message.success('操作成功');
        if (this.handleRefreshTable) {
          this.handleRefreshTable();
        } else {
          this.handleChangePersonListData();
        }
      } catch (e) {
        return false;
      }
    },

    // 详情
    handleDetails(data) {
      this.$refs.DialogPersonDetails.open({
        data
      });
    },

    // 编辑
    async handleAddOrEditPerson(row = {}) {
      const { zpglempid = '' } = row;
      if (zpglempid) {
        this.addEachData = {
          zpglempid
        };
      } else {
        this.addEachData = deepClone(row);
      }
      this.dialogAddBoolean = true;
    },

    // 删除
    async handleDelete(row) {
      try {
        const { zpglempid } = row;

        await this.$confirm('确定删除该条数据吗？', '提示', {
          type: 'warning'
        });

        let string = location.pathname.includes('/resume-management')
          ? '?type=1'
          : '';
        const res = await deleteZpglEmployeeListDetails(zpglempid, string);

        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.handleRefreshTable();
        this.$message.success(res.message || '删除成功');
      } catch (e) {
        console.error(e, '?e');
      }
    }
  }
};
