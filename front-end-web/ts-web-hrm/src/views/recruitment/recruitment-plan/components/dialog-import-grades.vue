<template>
  <ts-dialog
    class="dialog-add-setting"
    title="导入成绩"
    width="450px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-row>
          <ts-col :span="24">
            <ts-form-item
              class="down-box"
              label="考试成绩："
              prop="file"
              :rules="rules.file"
            >
              <recruit-xlsx-upload
                v-if="visible"
                accept=".xlsx"
                getLocalFile
                tips=""
                fileText="上传文件"
                @SendLoaclFile="SendLoaclFile"
              />
              <span class="down-template" @click="handleDownTemplate">
                下载模版
              </span>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </ts-form>

      <ul class="file-box" v-if="form.file">
        <li>
          <div style="width: 120px"></div>
          <p>{{ form.file.name }}</p>
          <span class="del" @click="handleLoaclFileDel">删除</span>
        </li>
      </ul>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit"
        >提 交</ts-button
      >
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    downTemplateApi: {
      type: String
    },
    postId: {
      type: String
    },
    saveHandle: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      primaryBlue,

      visible: false,

      submitLoading: false,

      form: {
        file: null
      },

      rules: {
        file: {
          required: true,
          message: '必填',
          validator: (prop, value, callback) => {
            if (!value || value.length === 0) {
              callback('false');
              return;
            }

            callback();
          }
        }
      }
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.initData();
          this.$nextTick(() => {
            this.$refs.form?.clearValidate();
          });
        }
        this.visible = val;
      }
    }
  },
  methods: {
    initData() {
      this.form.file = null;
    },
    handleLoaclFileDel() {
      this.form.file = null;
    },
    SendLoaclFile(data) {
      this.form.file = data;
    },
    handleDownTemplate() {
      let xhr = new XMLHttpRequest();
      xhr.open('post', this.downTemplateApi, true);
      xhr.responseType = 'blob';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send();
      xhr.onload = function() {
        if (this.status === 200) {
          let url = window.URL.createObjectURL(new Blob([this.response]));
          let link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', '考试成绩模版.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      };
    },
    async submit() {
      try {
        await this.$refs.form.validate();

        let formData = new FormData();
        formData.append('file', this.form.file);
        formData.append('postId', this.postId);

        this.submitLoading = true;
        const res = await this.saveHandle(formData);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.submitLoading = false;
          this.$emit('refresh');
          this.close();
        } else {
          this.submitLoading = false;
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-setting {
  ::v-deep {
    .el-dialog__body {
      height: calc(100% - 100px);
      overflow: auto;

      .content {
        height: 75px;

        .down-box {
          position: relative;

          .down-template {
            position: absolute;
            left: 90px;
            top: 6px;
            color: $primary-blue;
            cursor: pointer;
          }
        }

        .file-box {
          display: flex;
          flex-direction: column;

          li {
            display: flex;
            align-items: center;

            p {
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              font-size: 14px;
              flex: 1 0;
              color: #333333;
            }

            .del {
              font-size: 12px;
              color: #e24242;
              line-height: 16px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}
</style>
