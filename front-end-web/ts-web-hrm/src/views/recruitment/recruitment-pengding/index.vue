<template>
  <div class="recruitment-pengding">
    <ts-tabs v-model="activeTab">
      <ts-tab-pane label="待筛选" name="1"></ts-tab-pane>
      <ts-tab-pane label="筛选不通过" name="2"></ts-tab-pane>
    </ts-tabs>

    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :propPageSize="40"
      :pageSizes="[20, 40, 60, 80, 100, 200]"
      v-loading="loading"
      :columns="columns(true, [], { label: '应聘岗位', prop: postProp })"
      @refresh="handleRefreshTable"
      @selection-change="handleTableSelectChange"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-set-recruit-info
      ref="DialogSetRecruitInfo"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />

    <dialog-add-talent-pool
      ref="DialogAddTalentPool"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogAddTalentPool from '@/views/recruitment/components/dialog-add-talent-pool/dialog-add-talent-pool.vue';
import DialogSetRecruitInfo from '@/views/recruitment/components/dialog-set-recruit-info/dialog-set-recruit-info.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';

import { zpglEmployeeDsclist } from '@/api/ajax/NewRecruit/recruitment-pengding.js';
import { talentPoolTemplateTree } from '@/api/ajax/NewRecruit/template.js';

export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    DialogAdd,
    DialogTrack,
    DialogAddTalentPool,
    DialogSetRecruitInfo,
    DialogPersonDetails
  },
  data() {
    return {
      tableSelectedRows: [],

      activeTab: '1',
      treeData: [],

      dialogAddTalentPool: false,
      addTalentPoolDto: {}
    };
  },
  async created() {
    this.handleGetTreeData();
  },
  watch: {
    activeTab: {
      handler(val) {
        if (val == '1') {
          this.actions = [
            {
              label: '批量筛选',
              prop: { type: 'primary' },
              click: this.handleSelectionSetInfo
            }
          ];
        } else {
          this.actions = [];
        }

        this.$nextTick(() => {
          this.handleRefreshTable();
        });
      },
      immediate: true
    }
  },
  computed: {
    postProp() {
      return this.activeTab == '1' ? 'interviewPath' : 'msgInterviewPath';
    }
  },
  methods: {
    handleTableSelectChange(val) {
      this.tableSelectedRows = val || [];
    },
    async handleSelectionSetInfo() {
      if (!this.tableSelectedRows.length) {
        this.$message.warning('请选择需要操作的数据!');
        return;
      }

      try {
        const result = this.tableSelectedRows.some(
          item => item.conform !== null
        );

        if (result) {
          await this.$confirm('含有已设置面试信息，是否再次设置?', '提示', {
            type: 'warning'
          });
        }

        const zpglempid = this.tableSelectedRows
          .map(item => item.zpglempid)
          .join(',');

        let type = this.activeTab == '1' ? 'set' : 'edit';
        this.handleSetRecruitInfo({ zpglempid }, type);
      } catch (error) {}
    },

    async handleGetTreeData() {
      // 获取人才库岗位
      const res = await talentPoolTemplateTree();
      if (res.success == false) {
        this.$message.error(res.message || '获取人才库岗位失败');
        return;
      }
      this.treeData = res.object;
    },
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.searchForm.searchStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.searchEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      delete this.searchForm.date;

      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 't1.create_date',
          sord: 'desc',
          searchDsx: this.activeTab
        };
      searchForm.interviewResultStatusList = (
        searchForm.interviewResultStatusList || []
      ).join(',');

      searchForm.personnelCategoryList = (
        searchForm.personnelCategoryList || []
      ).join(',');
      let res = await zpglEmployeeDsclist(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.recruitment-pengding {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  @import url('../styles/recruitment-table.css');
}
</style>
