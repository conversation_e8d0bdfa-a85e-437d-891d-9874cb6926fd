<template>
  <div class="recruitment-report-table-container">
    <div
      class="table-item-container"
      v-for="(val, key, index) in tableData"
      :key="key"
    >
      <h1>
        表{{ index + 1 }}：招聘数据
        <span class="red-font">（{{ computedTypeName(key) }}）</span>
      </h1>
      <table>
        <thead>
          <tr>
            <th :rowspan="2" style="width: 5%;">
              类别
            </th>
            <th :rowspan="2" style="width: 12%;">
              指标名称
            </th>
            <th colspan="12">
              月份
            </th>
            <th colspan="4">
              季度小计
            </th>
            <th :rowspan="2" style="width: 9%;">
              年度统计
            </th>
          </tr>
          <tr>
            <th style="width: 4%;">1</th>
            <th style="width: 4%;">2</th>
            <th style="width: 4%;">3</th>
            <th style="width: 4%;">4</th>
            <th style="width: 4%;">5</th>
            <th style="width: 4%;">6</th>
            <th style="width: 4%;">7</th>
            <th style="width: 4%;">8</th>
            <th style="width: 4%;">9</th>
            <th style="width: 4%;">10</th>
            <th style="width: 4%;">11</th>
            <th style="width: 4%;">12</th>
            <th style="width: 6.5%;">第一季度</th>
            <th style="width: 6.5%;">第二季度</th>
            <th style="width: 6.5%;">第三季度</th>
            <th style="width: 6.5%;">第四季度</th>
          </tr>
        </thead>
        <tbody :id="'RecruitmentReportTbody' + key"></tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: { type: Object, default: () => {} },
    typeOptions: { type: Array, default: () => [] }
  },
  data() {
    return {};
  },
  computed: {
    // 使用data属性作为参数的computed属性
    computedTypeName() {
      return function(val) {
        let ids = JSON.parse(val);
        let labels = [];

        if (ids && ids.length) {
          labels = ids.reduce((p, v) => {
            let find = this.typeOptions.find(f => f.value == v) || {};
            p.push(find?.label || '');
            return p;
          }, []);
        }
        let names = labels.join(',');
        return names;
      };
    }
  },
  methods: {
    render() {
      for (const key in this.tableData) {
        let dom = document.getElementById(`RecruitmentReportTbody${key}`);
        const { reportDataList, reportIndexList } = this.tableData[key];

        (reportDataList || []).forEach((f, i) => {
          let tr = document.createElement('tr');
          if (i == 0) {
            let td = this.createTd(
              '招聘数据',
              undefined,
              0,
              reportDataList.length,
              'width: 5%;writing-mode: vertical-rl;letter-spacing: 12px;'
            );
            tr.appendChild(td);
          }

          f.forEach((text, index) => {
            let tdColor = '';
            if (text != '0' && index != 0) tdColor = 'color: rgb(255,98,0)';
            tr.appendChild(
              this.createTd(text, index, f.length, false, tdColor)
            );
          });

          dom.appendChild(tr);
        });

        (reportIndexList || []).forEach((f, i) => {
          let tr = document.createElement('tr');
          if (i == 0) {
            let td = this.createTd(
              '招聘指标',
              undefined,
              0,
              reportIndexList.length,
              'width: 5%;writing-mode: vertical-rl;letter-spacing: 12px;'
            );
            tr.appendChild(td);
          }

          f.forEach((text, index) => {
            let tdColor = '';
            if (text != '0%' && index != 0) tdColor = 'color: rgb(255,98,0)';
            tr.appendChild(
              this.createTd(text, index, f.length, false, tdColor)
            );
          });

          dom.appendChild(tr);
        });
      }
    },

    createTd(text, index, trLength, rowSpan, style) {
      let td = document.createElement('td');
      td.innerText = text;
      if (rowSpan) td.rowSpan = rowSpan;

      if (typeof undefined != 'undefined') {
        if (index === 0) {
          td.style = 'width: 12%;';
        } else if (index >= 1 && index <= 12) {
          td.style = 'width: 4%;';
        } else if (index === trLength - 1) {
          td.style = 'width: 9%;';
        } else {
          td.style = 'width: 6.5%;';
        }
      }

      if (style) td.style = style;

      return td;
    }
  }
};
</script>
