<template>
  <div class="interview-management">
    <ts-tabs v-model="interviewStatus">
      <ts-tab-pane label="待面试" name="1"></ts-tab-pane>
      <ts-tab-pane label="面试通过" name="2"></ts-tab-pane>
      <ts-tab-pane label="面试不通过" name="3"></ts-tab-pane>
      <ts-tab-pane label="待定" name="4"></ts-tab-pane>
      <ts-tab-pane label="面试签到表" name="5"></ts-tab-pane>
    </ts-tabs>

    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    />

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :propPageSize="40"
      :pageSizes="[20, 40, 60, 80, 100, 200]"
      v-loading="loading"
      :columns="columns(true, interviewColumns, tableShowKey)"
      @refresh="handleRefreshTable"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-set-recruit-info
      ref="DialogSetRecruitInfo"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-interview-evaluation
      ref="DialogInterviewEvaluation"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-add-talent-pool
      ref="DialogAddTalentPool"
      :treeData="treeData"
      @successCallBack="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />

    <sign-in-qrcode v-show="interviewStatus == '5'" />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogAddTalentPool from '@/views/recruitment/components/dialog-add-talent-pool/dialog-add-talent-pool.vue';
import DialogSetRecruitInfo from '@/views/recruitment/components/dialog-set-recruit-info/dialog-set-recruit-info.vue';
import DialogInterviewEvaluation from '@/views/recruitment/interview-management/component/dialog-interview-evaluation.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';

import signInQrcode from '@/views/recruitment/example-management/components/sign-in-qrcode/index.vue';

import { talentPoolTemplateTree } from '@/api/ajax/NewRecruit/template.js';
import { zpglInterviewMessageList } from '@/api/ajax/NewRecruit/interview.js';
export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    DialogAdd,
    DialogPersonDetails,
    DialogAddTalentPool,
    DialogSetRecruitInfo,
    DialogInterviewEvaluation,
    DialogTrack,
    signInQrcode
  },
  data() {
    return {
      dialogAddTalentPool: false,
      addTalentPoolDto: {},

      interviewStatus: '1',
      treeData: []
    };
  },
  computed: {
    tableShowKey() {
      let data = null;
      if (this.interviewStatus == '1') {
        data = {
          label: '应聘岗位',
          prop: 'msgInterviewPath'
        };
      } else {
        data = {
          label: '面试岗位',
          prop: 'resInterviewPath'
        };
      }
      return data;
    }
  },
  created() {
    this.handleGetTreeData();
  },
  watch: {
    interviewStatus: {
      handler(val) {
        if (val == '5') return;
        this.refresh();
      }
    }
  },
  methods: {
    search() {
      this.searchForm.searchStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.searchEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      delete this.searchForm.date;

      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    refresh() {
      this.handleRefreshTable();
      this.$refs.table.computedTableWidth();
    },
    async handleGetTreeData() {
      // 获取人才库岗位
      const res = await talentPoolTemplateTree();
      if (res.success == false) {
        this.$message.error(res.message || '获取人才库岗位失败');
        return;
      }
      this.treeData = res.object;
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          interviewStatus: this.interviewStatus,
          pageNo,
          pageSize,
          sidx: 't1.create_date',
          sord: 'desc'
        };

      searchForm.interviewResultStatusList = (
        searchForm.interviewResultStatusList || []
      ).join(',');

      searchForm.personnelCategoryList = (
        searchForm.personnelCategoryList || []
      ).join(',');

      let res = await zpglInterviewMessageList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.interview-management {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @import url('../styles/recruitment-table.css');
}
</style>
