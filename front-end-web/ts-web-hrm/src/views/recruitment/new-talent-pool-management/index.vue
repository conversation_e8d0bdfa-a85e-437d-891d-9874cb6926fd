<template>
  <div class="new-talent-pool-management">
    <div class="content">
      <div class="left">
        <search-tree
          ref="searchTree"
          isAll
          @tree="getTreeSuccess"
          placeholder="输入科室名进行搜索"
          @beforeClick="clickItemTree"
        />
      </div>

      <div class="right">
        <ts-search-bar
          v-model="searchForm"
          :actions="actions"
          :formList="searchList"
          :elementCol="14"
          @search="search"
          @reset="reset"
          :resetData="{}"
        />

        <base-table
          class="form-table"
          ref="table"
          border
          stripe
          :propPageSize="40"
          :pageSizes="[20, 40, 60, 80, 100, 200]"
          v-loading="loading"
          :columns="
            columns(true, [], {
              label: '应聘岗位',
              prop: 'interviewPool'
            })
          "
          @selection-change="handleSelectionChange"
          @refresh="handleRefreshTable"
        />
      </div>
    </div>

    <dialog-new-talent-pool
      ref="DialogNewTalentPool"
      :treeData="treeData"
      :titleList="titleList"
      @successCallBack="handleRefreshTable"
    />

    <dialog-add
      v-model="dialogAddBoolean"
      formTemplate="cscjk-form"
      :eachData="addEachData"
      :treeData="treeData"
      :titleList="titleList"
      :successCallBack="handleRefreshTable"
    />

    <dialog-person-details
      ref="DialogPersonDetails"
      formTemplate="cscjk-form"
      :treeData="treeData"
      :titleList="titleList"
      @refresh="handleRefreshTable"
    />

    <dialog-track ref="DialogTrack" @successCallBack="handleRefreshTable" />

    <dialog-set-tag v-model="dialogSetTag" :selectList="selectList" />
  </div>
</template>

<script>
import SearchTable from '@/views/recruitment/Mixins/search-table.js';
import SearchData from '@/views/recruitment/Mixins/search-data.js';
import RecruitmentMethods from '@/views/recruitment/Mixins/recruitment-methods.js';

import SearchTree from '@/components/search-tree/index.vue';
import DialogAdd from '@/views/recruitment/resume-management/dialog/dialog-add.vue';
import DialogTrack from '@/views/recruitment/new-talent-pool-management/dialog/dialog-track.vue';
import DialogPersonDetails from '@/views/recruitment/components/dialog-person-details/dialog-person-details.vue';
import DialogSetTag from '@/views/recruitment/components/dialog-set-tag/dialog-set-tag.vue';
import DialogNewTalentPool from './dialog/dialog-new-talent-pool.vue';

import {
  getZpglAddTalentpoolList,
  zpglAddTalentpoolAddEntery,
  zpglAddTalentpoolDelete
} from '@/api/ajax/NewRecruit/talentPool';

export default {
  mixins: [SearchTable, SearchData, RecruitmentMethods],
  components: {
    SearchTree,
    DialogAdd,
    DialogNewTalentPool,
    DialogPersonDetails,
    DialogTrack,
    DialogSetTag
  },
  data() {
    return {
      actions: [
        {
          label: '新增',
          prop: { type: 'primary' },
          click: this.handleAddTalentPool
        },
        {
          label: '批量打标签',
          prop: { type: 'danger' },
          click: this.handleSelectSetTag
        },
        {
          label: '导出',
          click: this.handleExport
        }
      ],
      treeData: [],
      treeLevel: '',
      affiliationJob: '',

      selectList: [],

      dialogSetTag: false,

      addEachData: {}
    };
  },
  async created() {
    this.$nextTick(() => {
      this.handleRefreshTable();
    });
  },
  methods: {
    handleChangeTree(e) {
      const paths = e.getPath();
      const names = paths.map(item => item.name) || [];
      let allPath = names.join('>');
      const { id, name } = e;

      this.searchForm.allPath = allPath;
      this.searchForm.interviewJob = id;
      this.searchForm.interviewJobtext = name;
    },
    handleTreeBeforeChange(e, treeObj, fn) {
      if (e.children && e.children.length > 0) {
        fn(false);
        return false;
      }
    },
    // 树加载成功
    getTreeSuccess(data, id, name) {
      this.treeData = data;

      this.clickTreeItemId = id;
      this.clickTreeItemName = name;
    },
    // 树 item点击
    clickItemTree(node, data, allParentValArr) {
      this.treeLevel = node.level + 1;
      this.affiliationJob = node.id;

      this.handleRefreshTable();
    },
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.searchForm.searchStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.searchEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      this.handleRefreshTable();
    },
    reset() {
      this.treeLevel = '';
      this.affiliationJob = '';
      this.$refs.searchTree.$refs.tsTree.treeObj.cancelSelectedNode();
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          treeLevel: this.treeLevel,
          affiliationJob: this.affiliationJob,
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.date;
      searchForm.interviewResultStatusList = (
        searchForm.interviewResultStatusList || []
      ).join(',');

      searchForm.personnelCategoryList = (
        searchForm.personnelCategoryList || []
      ).join(',');
      let res = await getZpglAddTalentpoolList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    },

    // 人才库新增
    handleAddTalentPool(row = {}) {
      this.$refs.DialogNewTalentPool.open({
        data: {},
        type: 'add'
      });
    },

    // 人才库编辑
    handleAddOrEditPerson(row = {}) {
      if (row.addSource === '1') {
        this.$refs.DialogNewTalentPool.open({
          data: {
            id: row.id,
            zpglempid: row.zpglempid,
            employeeName: row.employeeName,
            iphone: row.iphone,
            zhichengmingcheng: row.zhichengmingcheng,
            interviewPool: row.interviewPool,
            affiliationJobtext: row.affiliationJobtext,
            affiliationJob: row.affiliationJob
          },
          type: 'edit'
        });
      } else {
        if (row.zpglempid) {
          this.addEachData = {
            zpglempid
          };
        }
        this.dialogAddBoolean = true;
      }
    },

    // 人才库删除
    async handleDelete(row) {
      try {
        await this.$confirm('您确认删除此数据吗？', '提示', {
          type: 'warning'
        });
        const res = await zpglAddTalentpoolDelete(row.zpglempid);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    handleDetails(row) {
      // 人才库查看
      if (row.addSource === '1') {
        this.$refs.DialogNewTalentPool.open({
          data: {
            id: row.id,
            zpglempid: row.zpglempid,
            employeeName: row.employeeName,
            iphone: row.iphone,
            zhichengmingcheng: row.zhichengmingcheng,
            interviewPool: row.interviewPool,
            affiliationJobtext: row.affiliationJobtext,
            affiliationJob: row.affiliationJob
          },
          type: 'details',
          readOnly: true
        });
      } else if (row.addSource === null) {
        this.$refs.DialogPersonDetails.open({
          data
        });
      }
    },

    handleSelectionChange(e) {
      this.selectList = e;
    },

    handleSelectSetTag() {
      if (this.selectList.length === 0) {
        this.$message.warning('请至少选择一条数据操作!');
        return;
      }

      this.dialogSetTag = true;
    },

    handleExport() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo || 1,
        pageSize = this.$refs.table.pageSize || 15,
        { date = [] } = this.searchForm,
        [searchStartDate = null, searchEndDate = null] = date,
        data = {
          pageNo,
          pageSize,
          ...this.searchForm,
          searchStartDate,
          searchEndDate,
          sord: 'desc',
          sidx: 'a.create_date'
        };
      delete data.date;
      data.interviewResultStatusList = (
        data.interviewResultStatusList || []
      ).join(',');

      data.personnelCategoryList = (data.personnelCategoryList || []).join(',');

      let queryData = Object.keys(data)
        .map(key => key + '=' + data[key])
        .join('&');
      let a = document.createElement('a');
      a.href = '/ts-hrms/api/zpglAddTalentpool/exportAll?' + queryData;
      a.click();
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.new-talent-pool-management {
  width: 100%;
  height: 100%;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: hidden;
    .left {
      width: 216px;
      height: 100%;
      padding: 8px;
      margin-right: 8px;
      background: #fff;
    }
    .right {
      flex: 1;
      height: 100%;
      padding: 8px 0 8px 8px;
      background: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .add-type {
        padding: 4px 8px;
        background: #479ad0 !important;
        color: #fff;
        margin-right: 8px;
        border-radius: 4px;
      }
      @import url('../styles/recruitment-table.css');
    }
  }
}
</style>
