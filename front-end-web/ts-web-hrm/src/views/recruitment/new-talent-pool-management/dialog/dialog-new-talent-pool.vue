<template>
  <ts-dialog
    class="dialog-add-talent-pool"
    title="新增"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <ts-form ref="ruleForm" :model="form" labelWidth="100px">
      <ts-row>
        <ts-form-item label="姓名" prop="employeeName" :rules="rules.required">
          <ts-input
            v-model="form.employeeName"
            :disabled="readOnly"
            placeholder="请输入"
          />
        </ts-form-item>
      </ts-row>
      <ts-row>
        <ts-form-item label="联系电话" prop="iphone" :rules="rules.required">
          <ts-input
            v-model="form.iphone"
            :disabled="readOnly"
            placeholder="请输入"
          />
        </ts-form-item>
      </ts-row>
      <ts-row>
        <ts-form-item
          label="职称"
          prop="zhichengmingcheng"
          :rules="rules.required"
        >
          <ts-select
            style="width: 100%"
            v-model="form.zhichengmingcheng"
            clearable
            :disabled="readOnly"
            placeholder="请选择"
          >
            <ts-option
              v-for="item of titleList"
              :key="item.jobtitleId"
              :label="item.jobtitleName"
              :value="item.jobtitleId"
            ></ts-option>
          </ts-select>
        </ts-form-item>
      </ts-row>
      <ts-row>
        <ts-form-item
          label="归属人才库"
          prop="affiliationJob"
          :rules="rules.required"
        >
          <ts-ztree-select
            ref="treeSelect"
            :inpText.sync="form.interviewPool"
            :inpVal.sync="form.affiliationJob"
            :data="treeData"
            defaultExpandAll
            :disabled="readOnly"
            @change="handleChangeTree"
            @before-change="handleTreeBeforeChange"
          />
        </ts-form-item>
      </ts-row>
    </ts-form>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit" v-if="!readOnly">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import {
  zpglAddTalentpoolAdd,
  zpglAddTalentpoolUpdate
} from '@/api/ajax/NewRecruit/talentPool.js';
export default {
  components: {},
  props: {
    treeData: {
      type: Array
    },
    titleList: {
      type: Array
    }
  },
  data() {
    return {
      visible: false,
      readOnly: false,

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      type: ''
    };
  },
  methods: {
    open({ data, type, readOnly = false }) {
      this.form = deepClone(data);
      this.type = type;
      this.readOnly = readOnly;

      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
      this.visible = true;
    },
    handleChangeTree(e) {
      const paths = e.getPath();
      const names = paths.map(item => item.name) || [];
      let allPath = names.join('>');
      const { id, name } = e;

      this.form.affiliationJob = id;
      this.form.affiliationJobtext = name;
      this.form.interviewPool = allPath;
    },
    handleTreeBeforeChange(e, treeObj, fn) {
      if (e.children && e.children.length > 0) {
        fn(false);
        return false;
      }
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();

        const data = deepClone(this.form);
        let API = zpglAddTalentpoolAdd;
        if (this.type === 'edit') {
          API = zpglAddTalentpoolUpdate;
        }
        const res = await API(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.close();
          this.$emit('successCallBack', 3);
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-talent-pool {
}
</style>
