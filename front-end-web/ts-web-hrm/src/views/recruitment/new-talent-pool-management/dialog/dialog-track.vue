<template>
  <ts-dialog
    class="dialog-track"
    width="950px"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="left">
        <div class="title-top">跟踪记录</div>
        <ts-steps
          v-if="trackRecord && trackRecord.length"
          direction="vertical"
          type="logs"
          style="margin-right: 25px;"
        >
          <ts-step v-for="item in trackRecord" :key="item.id">
            <span slot="title">【跟踪】</span>
            <ul slot="description">
              <!-- <p>
                <span>跟踪结果：</span>
                <span class="value-title">{{ item.label }}</span>
              </p> -->
              <p>
                <span>跟踪内容：</span>
                <span class="value-title">{{ item.traceResult }}</span>
              </p>
              <p>
                <span>跟踪人：</span>
                <span class="value-title">{{ item.createUserName }}</span>
              </p>
              <p>
                <span>跟踪时间：</span>
                <span class="value-title">{{ item.createDate }}</span>
              </p>
            </ul>
          </ts-step>
        </ts-steps>
        <div v-else>暂无记录</div>
      </div>
      <div class="right">
        <ts-form ref="ruleForm" :model="form" labelWidth="150px">
          <ts-row>
            <ts-form-item label="下一次跟踪时间">
              <ts-date-picker
                style="width: 100%"
                v-model="form.nextTime"
                :disabledDate="disabledDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-row>
          <ts-row>
            <ts-form-item
              label="跟踪内容"
              prop="traceResult"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.traceResult"
                type="textarea"
                class="textarea"
                maxlength="200"
                show-word-limit
              />
            </ts-form-item>
          </ts-row>
        </ts-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';
import { zpglTraceResultSave } from '@/api/ajax/NewRecruit/interview.js';
import { getZpglTraceResultList } from '@/api/ajax/NewRecruit/interview.js';

export default {
  components: {},
  data() {
    return {
      visible: false,
      title: '跟踪',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },

      trackRecord: [],
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      }
    };
  },
  methods: {
    async open({ data }) {
      this.form = {};
      this.eachData = deepClone(data);

      await this.getHandleTrackRecordList();
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });

      this.visible = true;
    },
    async getHandleTrackRecordList() {
      let res = await getZpglTraceResultList(this.eachData.zpglempid);
      if (res.success == false) {
        this.$message.error(res.message || '获取跟踪记录失败');
        return;
      }
      this.trackRecord = (res.object || []).map(item => {
        let label =
          item.traceStatus === '1'
            ? '继续跟踪'
            : item.traceStatus === '2'
            ? '结束跟踪'
            : '';

        return {
          label,
          id: item.id,
          traceResult: item.traceResult,
          createDate: item.createDate,
          createUserName: item.createUserName
        };
      });
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();

        let data = deepClone(this.form);
        data = {
          ...data,
          ...this.eachData
        };
        const res = await zpglTraceResultSave(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.close();
          this.$emit('successCallBack');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-track {
  ::v-deep {
    .el-dialog__body {
      height: 480px;

      .title-top {
        font-weight: 500;
        font-size: 16px;
        margin-bottom: 8px;
      }
      .content {
        height: 100%;
        display: flex;
        overflow: hidden;

        .left {
          width: 300px;
          overflow: auto;
        }
        .right {
          flex: 1;
        }
      }
    }
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
