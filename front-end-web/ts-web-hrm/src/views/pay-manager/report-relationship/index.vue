<template>
  <div class="table-container">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="right">
        <ts-button type="primary" @click="handleAdd">
          上报关系
        </ts-button>
      </template>
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :hasPage="false"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-setting-relationship
      ref="DialogSettingRelationship"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import XeUtilsGroupBy from 'xe-utils/groupBy';

import tableMixins from './mixins/tableMixins';
import DialogSettingRelationship from './components/dialog-setting-relationship';
export default {
  mixins: [tableMixins],
  components: {
    DialogSettingRelationship
  },
  data() {
    return {};
  },
  methods: {
    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    search() {
      this.handleRefreshTable();
    },

    handleAdd() {
      this.$refs.DialogSettingRelationship.open({
        title: '上报关系维护',
        type: 'add',
        data: {}
      });
    },

    async handleEmpty({ timekeeperId, timeEmployeeName }) {
      try {
        if (!timekeeperId) return false;
        await this.$confirm(
          `是否清除${timeEmployeeName}的上报关系数据?`,
          '提示',
          {
            type: 'warning'
          }
        );
        const res = await this.ajax.timekeeperDelete({
          uploadType: '1',
          timekeeperId
        });
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleRefreshTable() {
      let pageNo = 1,
        pageSize = 999999,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          uploadType: '1',
          sidx: 't4.name',
          sord: 'asc'
        };
      let res = await this.ajax.timekeeperGetDataList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        return {
          index: (pageNo - 1) * pageSize + i + 1,
          ...item
        };
      });

      const result = [];
      let groupByData = XeUtilsGroupBy(rows, 'timeEmployeeNo');
      for (const timeEmployeeNo in groupByData) {
        const items = groupByData[timeEmployeeNo];
        const timeEmployeeName = items[0].timeEmployeeName;
        const timekeeperId = items[0].timekeeperId;
        groupByData[timeEmployeeNo] = {
          timekeeperId,
          timeEmployeeName,
          timeEmployeeNo,
          id: timeEmployeeNo,
          children: items
        };

        result.push(groupByData[timeEmployeeNo]);
      }

      this.$refs.table.refresh({
        ...res,
        rows: result
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
    }
  }
}
</style>
