<template>
  <div class="left-tree-container flex-column">
    <div class="flex-row-between">
      <ts-input
        placeholder="输入关键字进行过滤"
        @input="searchInput"
        style="width: 180px;"
        v-model="searchVal"
      />

      <div class="flex-col-center">
        选中人员：
        <ts-input
          class="select-input"
          disabled
          v-model="modelValue.timekeeperName"
        />
      </div>
    </div>

    <el-scrollbar
      class="flex-column tree-scrollbar"
      style="flex: 1;"
      wrap-style="overflow-x: hidden;"
    >
      <ts-ztree
        ref="leftTree"
        :data="treeData"
        emptyText="暂无数据"
        :defaultExpandAll="defaultExpandAll"
        @before-click="handleLeftBeforeClick"
      >
      </ts-ztree>
    </el-scrollbar>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  model: {
    prop: 'modelValue',
    event: 'change'
  },
  props: {
    modelValue: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchVal: '',
      defaultExpandAll: false,
      searchInput: () => {},

      treeData: [],
      treeMap: {}
    };
  },
  methods: {
    async init(treeData) {
      this.searchInput = this.debounce(this.input, 300);

      this.treeData = deepClone(treeData);
      this.treeToMap(this.treeData);
    },

    handleLeftBeforeClick(treeNode, treeObj, fn) {
      if (treeNode.type == 1) {
        fn(false);
        return false;
      }
      fn(true);
      this.$nextTick(async () => {
        let currentNode = this.$refs.leftTree.getCurrentNode();
        if (currentNode && currentNode.length) {
          let { id: timekeeperId, name: timekeeperName } = currentNode[0];

          this.$emit('change', {
            timekeeperId,
            timekeeperName
          });
        }
      });
    },

    treeToMap(node) {
      for (let item of node) {
        this.treeMap[item.id] = {
          ...item,
          children: item.children ? [] : null
        };
        if (item.children && item.children.length > 0) {
          this.treeToMap(item.children);
        }
      }
    },

    input: function() {
      // 将搜索名称的node树 父级 子级全部扁平绑定至result上
      let result = {};
      for (let key in this.treeMap) {
        let node = deepClone(this.treeMap[key]);

        if (new RegExp(this.searchVal).test(node.name)) {
          result[key] = node;
          this.findParent(result, node);
          this.findChildren(result, node);
        }
      }

      let res = [];
      for (let key in result) {
        let treeItem = result[key];
        let pid = treeItem.pid;
        if (pid === '') {
          res.push(treeItem);
        } else {
          if (!result[pid]) {
            result[pid] = {
              children: []
            };
          }
          result[pid].children.push(treeItem);
        }
      }

      if (this.searchVal.trim()) {
        this.defaultExpandAll = true;
      } else {
        this.defaultExpandAll = false;
      }
      this.treeData = res;
    },

    // 查找node的parent
    findParent(result, node) {
      if (node.pid && node.pid != '') {
        result[node.pid] = deepClone(this.treeMap[node.pid]);
        this.findParent(result, this.treeMap[node.pid]);
      }
    },

    // 查找node的children
    findChildren(result, node) {
      for (let key in this.treeMap) {
        let item = this.treeMap[key];
        if (item.pid === node.id) {
          result[item.id] = deepClone(item);
          this.findChildren(result, item);
        }
      }
    },

    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.left-tree-container {
  ::v-deep {
    .select-input {
      width: 100px;
      min-width: 100px;
    }
  }
}
</style>
