<template>
  <div class="trasen-container flex-column">
    <div class="title-operate">
      <div class="title">
        薪酬档案
        <!-- <el-tooltip
          class="item"
          effect="dark"
          content="薪资档案用于人员进行定薪、调薪，请为新加入的员工办理定薪。"
          placement="bottom"
          :visible-arrow="false"
          offset="-300"
        >
          <img class="item-tips" src="@/assets/img/dd_tips.svg" alt="" />
        </el-tooltip> -->

        <div class="salary-fixing-tips" v-show="unpaidSalaryNum || unoptionNum">
          <img class="tips-img" src="@/assets/img/pay/tips.svg" />
          <span v-show="unpaidSalaryNum">
            在职人员
            <span class="did-not-num">{{ unpaidSalaryNum }}</span>
            人未定薪
            <span
              class="details-span size-mini"
              @click="handleSearchUnpaidSalary"
            >
              查看
            </span>
          </span>

          <span v-show="unoptionNum">
            <span class="unoption-text">
              <span class="did-not-num">{{ unoptionNum }}</span>
              位员工未加入薪酬方案
              <span
                class="details-span size-mini"
                @click="handleSearchUnoption"
              >
                查看
              </span>
            </span>
          </span>

          <span v-show="unoptionNum">
            <span class="unoption-text">
              <span class="did-not-num">{{ unoptionNumTmpEmp }}</span>
              位临时员工未加入薪酬方案
            </span>
          </span>
        </div>
      </div>
      <div class="operate">
        <ts-button
          class="import-btn-width"
          type="primary"
          @click="handleSetSalaryProfileFields"
        >
          设置薪酬档案字段
        </ts-button>
      </div>
    </div>

    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
    >
      <template slot="empFieldValue">
        <ts-select
          style="width: 50%"
          v-model="searchForm.empField"
          clearable
          placeholder="请选择薪酬项"
        >
          <ts-option
            v-for="item in filterBasicItemData"
            :key="item.empField"
            :label="item.basicItemName"
            :value="item.empField"
          />
        </ts-select>
        <ts-input
          style="width: 50%"
          v-model="searchForm.empFieldValue"
          placeholder="请输入百分比"
          clearable
        ></ts-input>
      </template>
      <template slot="gwdj">
        <ts-cascader
          clearable
          v-model="searchForm.gwdj"
          placeholder="请选择岗位等级"
          :options="gwdjList"
          :props="{
            value: 'postId',
            label: 'postName',
            children: 'postLevel',
            expandTrigger: 'hover',
            emitPath: false
          }"
        ></ts-cascader>
      </template>
      <template slot="salaryLevelId">
        <ts-cascader
          clearable
          v-model="searchForm.salaryLevelId"
          placeholder="请选择薪酬等级"
          :options="xcdjList"
          :props="{
            value: 'salaryTypeId',
            label: 'salaryName',
            children: 'salaryLevel',
            expandTrigger: 'hover',
            emitPath: false
          }"
        ></ts-cascader>
      </template>
      <template slot="right">
        <!-- <ts-button type="primary" @click="handleStop">
          批量停薪
        </ts-button> -->
        <ts-button type="primary" @click="handleChangeProfile">
          调整薪酬方案
        </ts-button>

        <ts-button @click="handleExport">导出</ts-button>
        <ts-button @click="() => handleSetPay('edit')">批量定薪</ts-button>
        <ts-button @click="() => handleSetPay('change')">批量调薪</ts-button>
      </template>
    </ts-search-bar>

    <!-- <base-table
      ref="table"
      class="form-table"
      border
      stripe
      :columns="columns"
      :pageSizes="[20, 30, 40, 60, 80, 100]"
      @selection-change="handleSelectionChange"
      @refresh="handleRefreshTable"
      @row-click="handleRowClick"
    /> -->
    <TsVxeTemplateTable
      ref="table"
      id="salary_profile_list"
      :scroll-y="{ enabled: true }"
      :columns="columns"
      @selection-change="handleSelectionChange"
      @refresh="handleRefreshTable"
    />

    <dialog-set-salary-profile-fields ref="DialogSetSalaryProfileFields" />

    <dialog-salary-adjustment
      ref="DialogSalaryAdjustment"
      @refresh="handleRefreshTable"
    />

    <dialog-salary-profile-details ref="DialogSalaryProfileDetails" />

    <dialog-basicitem-batch-adjust
      @refresh="handleRefreshTable"
      ref="DialogBasicitemBatchAdjust"
    />

    <dialog-stop-salary ref="DialogStopSalary" @refresh="handleRefreshTable" />

    <dialog-adjust-profile
      ref="DialogAdjustProfile"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import DialogSetSalaryProfileFields from './components/dialog-set-salary-profile-fields.vue';
import DialogSalaryAdjustment from './components/dialog-salary-adjustment.vue';
import DialogSalaryProfileDetails from './components/dialog-salary-profile-details.vue';
import DialogBasicitemBatchAdjust from './components/dialog-basicitem-batch-adjust.vue';
import DialogStopSalary from './components/dialog-stop-salary.vue';
import DialogAdjustProfile from './components/dialog-adjust-profile.vue';
import { deepClone } from '@/unit/commonHandle.js';
import moment from 'moment';
export default {
  components: {
    DialogSetSalaryProfileFields,
    DialogSalaryAdjustment,
    DialogSalaryProfileDetails,
    DialogBasicitemBatchAdjust,
    DialogStopSalary,
    DialogAdjustProfile
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '姓名/工号',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '员工状态',
          value: 'employeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '定薪状态',
          value: 'salaryAppoint',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '全部', value: '', element: 'ts-option' },
            { label: '未定薪', value: '0', element: 'ts-option' },
            { label: '已定薪', value: '1', element: 'ts-option' }
          ]
        },
        {
          label: '查询百分比',
          value: 'empFieldValue'
        },
        {
          label: '岗位等级',
          value: 'gwdj'
        },
        {
          label: '薪级等级',
          value: 'salaryLevelId'
        },
        {
          label: '是否加入薪酬方案',
          value: 'hasJoinOption',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '全部', value: '', element: 'ts-option' },
            { label: '未加入', value: '0', element: 'ts-option' },
            { label: '已加入', value: '1', element: 'ts-option' }
          ]
        },
        {
          label: '所属部门',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '编制类型',
          value: 'establishmentTypes',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '薪酬方案',
          value: 'optionId',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '政策标准',
          value: 'policyStandardId',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '入职日期',
          value: 'dateFirst',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD'
          }
        },

        {
          label: '转正日期',
          value: 'dateSecond',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD'
          }
        },

        {
          label: '离退休日期',
          value: 'dateThird',
          element: 'ts-range-picker',
          elementProp: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],
      columns: [],
      basicItemData: [], //薪酬档案薪酬项字段
      gwdjList: [],
      xcdjList: [],
      policyStandardData: [],
      multipleSelection: [],
      salaryProfileOptions: [],

      unpaidSalaryNum: undefined, // 在职人员未定薪数量
      unoptionNum: undefined, // 未加入薪酬组数量
      unoptionNumTmpEmp: undefined // 未加入薪酬组的临时员工数量
    };
  },
  created() {
    this.loadBasicItemData();
  },
  computed: {
    filterBasicItemData() {
      return this.basicItemData.filter(item => item.basicItemType == '2');
    }
  },
  methods: {
    /**
     * 加载薪酬档案字段项
     */
    async loadBasicItemData() {
      let res = await this.ajax.salaryBasicColumnListAll();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      this.basicItemData = res.object?.data || [];
    },

    async refresh() {
      this.handleDictItemEmployeeStatus();
      this.handleDicGetEstablishmentType();
      this.handleGetSalaryPolicyStandardData();
      this.getGwdjList();
      this.getXcdjList();
      await this.handleGetNewSalaryOptionAllList();

      let query = this.$route.query;

      if (query.type == 'notJoined') {
        this.$refs.table.pageNo = 1;
        this.$set(this, 'searchForm', {});
        this.$set(this.searchForm, 'hasJoinOption', '0');
      }

      if (query.type == 'searchProgramme' && query.optionId) {
        this.$refs.table.pageNo = 1;
        this.$set(this, 'searchForm', {});

        let findIndex = this.salaryProfileOptions.findIndex(
          f => f.value == query.optionId
        );

        if (findIndex != -1) {
          this.$set(this.searchForm, 'optionId', query.optionId);
        }
      }

      this.handleRefreshTable();
    },

    getGwdjList() {
      this.ajax.getPostLevel({}).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取岗位等级信息失败');
        }
        (res.object || []).forEach(item => {
          (item.postLevel || []).forEach(e => {
            e.postId = e.postLevelId;
          });
        });
        this.gwdjList = res.object;
      });
    },
    getXcdjList() {
      this.ajax.getSalaryLevel().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取薪酬等级信息失败');
        }
        (res.object || []).forEach(item => {
          (item.salaryLevel || []).forEach(e => {
            e.salaryTypeId = e.salaryLevelId;
          });
        });
        this.xcdjList = res.object;
      });
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    handleStop() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要操作的数据!');
        return false;
      }

      let ids = this.multipleSelection.map(f => f.id);
      this.$refs.DialogStopSalary.open({ data: { ids } });
    },

    handleChangeProfile() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要操作的数据!');
        return false;
      }

      let employeeId = this.multipleSelection.map(f => f.employee_id).join(',');
      let effectiveDate = moment()
        .startOf('day')
        .format('YYYY-MM-DD');

      this.$refs.DialogAdjustProfile.open({
        data: {
          employeeId,
          effectiveDate
        },
        salaryProfileOptions: this.salaryProfileOptions
      });
    },

    handleSetPay(type) {
      let employeeIds = [];
      if (type == 'change') {
        if (!this.multipleSelection.length) {
          this.$message.warning('请选择需要调薪的员工数据!');
          return false;
        }
        employeeIds = this.multipleSelection.map(f => f.employee_id);
      }
      this.$refs.DialogBasicitemBatchAdjust.open({
        type,
        employeeIds,
        searchForm: deepClone(this.handleGetSearchFormData())
      });
    },

    handleExport() {
      let searchData = this.handleGetSearchFormData();

      let aDom = document.createElement('a'),
        conditionList = Object.keys(searchData).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      aDom.href =
        '/ts-hrms/api/salaryBasicColumn/export?' + conditionList.join('&');
      aDom.click();
    },

    handleSetSalaryProfileFields() {
      this.$refs.DialogSetSalaryProfileFields.open({
        title: '设置薪酬档案字段'
      });
    },

    // 定薪 调薪
    handleSettingSalary(row, type, title) {
      this.$refs.DialogSalaryAdjustment.open({
        data: row,
        title,
        type
      });
    },

    handleAdd() {
      this.$refs.DialogSalaryGroup.open({
        title: '新增薪酬组'
      });
    },

    handleDetails(row) {
      this.$refs.DialogSalaryProfileDetails.open({
        title: '薪酬档案',
        data: row
      });
    },
    // 获取人员状态
    async handleDictItemEmployeeStatus() {
      let res = await this.ajax.dictItemEmployeeStatus();
      if (res.success == false) {
        this.$message.error(res.message || '人员状态数据获取失败');
        return;
      }

      this.searchList.find(f => f.value == 'employeeStatus').childNodeList = (
        res.rows || []
      ).map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });
    },

    // 获取编制类型
    async handleDicGetEstablishmentType() {
      let res = await this.ajax.getEstablishmentType();
      if (res.success == false) {
        this.$message.error(res.message || '编制类型数据获取失败');
        return;
      }
      this.searchList.find(
        f => f.value == 'establishmentTypes'
      ).childNodeList = (res.object || []).map(m => {
        return {
          label: m.dictName,
          value: m.dictValue,
          element: 'ts-option'
        };
      });
    },

    // 获取薪酬方案
    async handleGetNewSalaryOptionAllList() {
      let res = await this.ajax.newSalaryOptionAllList();
      if (res.success == false) {
        this.$message.error(res.message || '薪酬组数据获取失败');
        return;
      }
      this.salaryProfileOptions = (res.object || []).map(m => {
        return {
          label: m.optionName,
          value: m.id,
          element: 'ts-option'
        };
      });

      this.searchList.find(
        f => f.value == 'optionId'
      ).childNodeList = this.salaryProfileOptions;
    },

    // 获取政策标准
    async handleGetSalaryPolicyStandardData() {
      let res = await this.ajax.selectHrmsSalaryPolicyStandardData();
      if (res.success == false) {
        this.$message.error(res.message || '薪酬组数据获取失败');
        return;
      }
      if (res.object.length) {
        this.policyStandardData = res.object.filter(f => f.isEnable == '1');
      }
      let data = (this.policyStandardData || []).map(m => {
        return {
          label: m.policyStandardName,
          value: m.policyStandardId,
          element: 'ts-option'
        };
      });

      this.searchList.find(
        f => f.value == 'policyStandardId'
      ).childNodeList = data;
    },

    async handleRefreshTable() {
      this.handleGetSalaryBasicColumnGetUncertain();
      this.handleGetUnoption();
      await this.handleGetTableHead();
      await this.handleGetTableData();
    },

    // 获取未定薪人员数量
    async handleGetSalaryBasicColumnGetUncertain() {
      let res = await this.ajax.salaryBasicColumnGetUncertain();
      if (res.success == false) {
        this.$message.error(res.message || '获取未定薪人员数量失败');
        return;
      }
      this.unpaidSalaryNum = res.object;
    },
    //未加入方案人数
    async handleGetUnoption() {
      let UnoptionRes = await this.ajax.salaryBasicColumnGetUnoption({
        computeDate: this.computeDate
      });
      if (UnoptionRes.success == false) {
        this.$message.error(UnoptionRes.message || '数据获取失败');
        return;
      }
      this.unoptionNum = UnoptionRes.object.count;
      this.unoptionNumTmpEmp = UnoptionRes.object.countTmpEmp;
    },

    handleGetSearchFormData() {
      let searchForm = deepClone(this.searchForm);

      if (searchForm.employeeStatus) {
        searchForm.employeeStatus = searchForm.employeeStatus.join(',');
      } else {
        delete searchForm.employeeStatus;
      }

      if (searchForm.establishmentTypes) {
        searchForm.establishmentTypes = searchForm.establishmentTypes.join(',');
      } else {
        delete searchForm.establishmentTypes;
      }

      searchForm.entryDateStartTime =
        searchForm.dateFirst && (searchForm.dateFirst[0] || '');
      searchForm.entryDateEndTime =
        searchForm.dateFirst && (searchForm.dateFirst[1] || '');
      delete searchForm.dateFirst;

      searchForm.positiveTimeStartTime =
        searchForm.dateSecond && (searchForm.dateSecond[0] || '');
      searchForm.positiveTimeEndTime =
        searchForm.dateSecond && (searchForm.dateSecond[1] || '');
      delete searchForm.dateSecond;

      searchForm.retirementTimeStartTime =
        searchForm.dateThird && (searchForm.dateThird[0] || '');
      searchForm.retirementTimeEndTime =
        searchForm.dateThird && (searchForm.dateThird[1] || '');
      delete searchForm.dateThird;

      return searchForm;
    },

    async handleGetTableData() {
      let pageNo = this.$refs.table.pageNo;
      let pageSize = this.$refs.table.pageSize;

      let submitSearchFormData = this.handleGetSearchFormData();
      let searchForm = {
        ...submitSearchFormData,
        pageNo,
        pageSize
      };

      let res = await this.ajax.salaryBasicColumnlistTableData(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    async handleGetTableHead() {
      this.columns = [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          fixed: 'left'
        }
      ];
      let res = await this.ajax.salaryBasicColumnListTableTitle();
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        this.columns.push(...res.object);
        this.columns.push({
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          render: (h, { row }) => {
            let salaryAppoint = row.salary_appoint,
              label = salaryAppoint == 1 ? '调薪' : '定薪',
              type = salaryAppoint == 1 ? 'change' : 'add';

            let actionList = [
              {
                label,
                event: () => {
                  this.handleSettingSalary(row, type, label);
                }
              },
              {
                label: '详情',
                event: this.handleDetails
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        });

        res.object.find(f => {
          if (
            [
              'employee_no',
              'employee_name',
              'org_name',
              'employee_status'
            ].includes(f.prop)
          ) {
            f.fixed = 'left';
          }
        });
      }
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleSearchUnpaidSalary() {
      this.$set(this, 'searchForm', {});
      this.$set(this.searchForm, 'salaryAppoint', '0');
      this.search();
    },

    handleSearchUnoption() {
      this.$set(this, 'searchForm', {});
      this.$set(this.searchForm, 'hasJoinOption', '0');
      this.search();
    }
  }
};
</script>

<style lang="scss">
.unoption-container {
  width: 320px;
  min-height: 110px;
  max-height: 210px;
  overflow: auto;
  .el-popover__title {
    font-size: 16px;
    font-weight: 700;
  }
  .content-container {
    font-size: 16px;
  }
}
</style>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .unoption-text {
    font-size: 16px;
    margin-left: 8px;
    .unoption-num {
      font-size: 16px;
      font-weight: 800;
    }
  }

  .title-operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .title {
      font-size: 18px;
      font-weight: 800;
      display: flex;
      align-items: center;
      &::before {
        content: '1';
        width: 8px;
        display: inline-block;
        background: rgb(82, 96, 255);
        color: rgb(82, 96, 255);
        margin: 0 4px;
        border-radius: 4px;
      }
      .item-tips {
        width: 20px;
        height: 20px;
        margin-left: 4px;
        margin-right: 4px;
      }
    }
    .operate {
      display: flex;
      align-items: center;
    }
  }

  .details-span {
    color: $primary-blue;
    cursor: pointer;

    &.size-mini {
      font-size: 10px;
    }
  }

  .import-btn-width {
    ::v-deep span {
      max-width: 180px !important;
    }
  }

  .salary-fixing-tips {
    padding: 4px 8px;
    background-color: #faeee6;
    color: #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    margin-left: 8px;

    .tips-img {
      width: 24px;
      height: 24px;
    }
    .did-not-num {
      color: #e1853f;
      font-size: 18px;
    }
  }
}
</style>
