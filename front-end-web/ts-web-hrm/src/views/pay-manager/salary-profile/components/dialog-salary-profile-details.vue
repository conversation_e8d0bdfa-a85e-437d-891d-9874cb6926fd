<template>
  <ts-dialog
    custom-class="dialog-salary-profile-details"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="item-tips">个人信息</div>
      <descriptions
        class="person-info-descriptions"
        column="4"
        :columns="personInfoColumns"
        :tableData="personDetail"
      />

      <ts-tabs v-model="tabsStatus">
        <ts-tab-pane label="当前定薪" name="1" />
        <ts-tab-pane label="调薪记录" name="2" />
        <ts-tab-pane label="异动记录" name="3" />
      </ts-tabs>

      <div class="tabs-content">
        <div v-show="tabsStatus === '1'">
          <div class="item-tips small">基础信息</div>
          <descriptions
            v-if="basicColumns.length"
            class="person-info-descriptions"
            column="4"
            :columns="basicColumns"
            :tableData="basicDetail"
          />
          <div class="no-list-tips" v-else>暂无数据</div>

          <div class="item-tips small">工资项(单位：元)</div>
          <descriptions
            v-if="salaryItemColumns.length"
            class="person-info-descriptions"
            column="4"
            :columns="salaryItemColumns"
            :tableData="salaryItemDetail"
          />
          <div class="no-list-tips" v-else>暂无数据</div>
        </div>

        <!-- <base-table
          v-show="tabsStatus === '2'"
          ref="changeTable"
          class="form-table"
          border
          :hasPage="false"
          stripe
          :columns="changeSalaryColumns"
        /> -->
        <TsVxeTemplateTable
          v-show="tabsStatus === '2'"
          ref="changeTable"
          id="table_salary_profile_details_change"
          class="form-table"
          :hasPage="false"
          :columns="changeSalaryColumns"
        />

        <div class="unusual-action-table-container" v-show="tabsStatus === '3'">
          <ts-search-bar
            v-model="searchForm"
            :formList="searchList"
            :elementCol="14"
            @search="unusualActionTableSearch"
          >
          </ts-search-bar>

          <!-- <base-table
            ref="unusualActionTable"
            class="form-table"
            border
            stripe
            :columns="unusualActionColumn"
            @refresh="handleGetUnusualActionInfo"
          /> -->
          <TsVxeTemplateTable
            class="form-table"
            id="dialog-salary-profile-details-unusualAction-table"
            ref="unusualActionTable"
            :columns="unusualActionColumn"
            @refresh="handleGetUnusualActionInfo"
          />
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <dialog-salary-adjustment
      ref="DialogSalaryAdjustment"
      @refresh="handleRefreshTable"
    />
  </ts-dialog>
</template>

<script>
import Descriptions from '@/views/pay-manager/components/descriptions.vue';
import DialogSalaryAdjustment from '@/views/pay-manager/salary-profile/components/dialog-salary-adjustment.vue';
export default {
  components: { DialogSalaryAdjustment, Descriptions },
  data() {
    return {
      visible: false,
      title: '',
      tabsStatus: '',
      infoData: {},

      personInfoColumns: [],
      personDetail: {},

      basicColumns: [],
      basicDetail: {},
      salaryItemColumns: [],
      salaryItemDetail: {},

      changeSalaryColumns: [],
      unusualActionColumn: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          fixed: 'left',
          width: 70
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center',
          fixed: 'left',
          width: 90
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          fixed: 'left',
          width: 100
        },
        {
          label: '科室',
          prop: 'orgName',
          align: 'center',
          fixed: 'left',
          width: 120
        },
        {
          label: '薪酬项目',
          prop: 'salaryName',
          align: 'center',
          fixed: 'left',
          width: 110
        },
        {
          label: '调整时间',
          prop: 'effectiveDate',
          align: 'center',
          fixed: 'left',
          width: 110
        },
        {
          label: '来源',
          prop: 'sourceTypeText',
          align: 'center',
          width: 110
        },
        {
          label: '调整前',
          prop: 'adjustValue',
          align: 'center'
        },
        {
          label: '调整后',
          prop: 'nowValue',
          align: 'center'
        },
        {
          label: '原因',
          prop: 'reason',
          align: 'center'
        },
        {
          label: '操作人',
          prop: 'updateUserName',
          align: 'center',
          width: 90
        },
        {
          label: '操作时间',
          prop: 'updateDate',
          align: 'center',
          width: 160
        }
      ],

      searchForm: {},
      searchList: [
        {
          label: '异动月份',
          value: 'effectiveDate',
          element: 'ts-month-picker',
          elementProp: {
            placeholder: '请选择月份',
            valueFormat: 'YYYY-MM',
            allowClear: false
          },
          event: {
            change: e => {
              this.$set(this.searchForm, 'effectiveDate', e);
              this.handleGetUnusualActionInfo();
            }
          }
        }
      ]
    };
  },
  watch: {
    tabsStatus: {
      async handler(val) {
        if (val === '1') {
          this.handleGetCurrentFixedSalaryInfo();
        }
        if (val === '2') {
          this.handleRefreshTable();
        }
        if (val === '3') {
          this.handleGetUnusualActionInfo();
        }
      },
      immediate: true
    }
  },
  methods: {
    async open({ data: infoData, title }) {
      this.title = title;
      this.infoData = infoData;
      this.visible = true;
      await this.handleGetBasicInfo();

      this.tabsStatus = '1';
    },

    async handleGetBasicInfo() {
      let employeeId = this.infoData.employee_id;
      let res = await this.ajax.salaryBasicItemEmpGetBaseData(employeeId);

      if (res.success == false) {
        this.$message.error(res.message || '个人基本数据获取失败');
        return;
      }

      let keys = Object.getOwnPropertyNames(res.object);

      this.personInfoColumns = keys.map(key => {
        return {
          label: key,
          prop: key
        };
      });

      this.personDetail = res.object;
    },

    async handleGetCurrentFixedSalaryInfo() {
      let res = await this.ajax.salaryBasicItemEmpHistoryCurrentSalary({
        employeeId: this.infoData.employee_id
      });
      if (res.success == false) {
        this.$message.error(res.message || '当前定薪信息数据获取失败');
        return;
      }

      let { baseInfo = {}, salaryInfo = {} } = res.object;

      let baseKeys = Object.keys(baseInfo) || [];
      this.basicColumns = baseKeys.map(m => {
        return {
          label: m,
          prop: m
        };
      });
      this.basicDetail = baseInfo;

      let salaryKeys = Object.keys(salaryInfo) || [];
      this.salaryItemColumns = salaryKeys.map(m => {
        return {
          label: m,
          prop: m
        };
      });
      this.salaryItemDetail = salaryInfo;
    },

    async handleRefreshTable() {
      await this.handleGetChangeTableHead();
      await this.handleGetChangeTableData();
    },

    async handleGetChangeTableHead() {
      let employeeId = this.infoData.employee_id;
      let res = await this.ajax.salaryBasicItemEmpHistoryHeadlist(employeeId);
      if (res.success == false) {
        this.$message.error(res.message || '调薪表头数据获取失败');
        return;
      }

      this.changeSalaryColumns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        }
      ];
      if (Array.isArray(res.object) && res.object.length !== 0) {
        this.changeSalaryColumns.push(...res.object);
      }
    },

    async handleGetChangeTableData() {
      let employeeId = this.infoData.employee_id;
      let res = await this.ajax.salaryBasicItemEmpHistoryList({
        employeeId
      });

      if (res.success == false) {
        this.$message.error(res.message || '调薪列表数据获取失败');
        return;
      }

      this.$nextTick(() => {
        this.$refs.changeTable.refresh({
          rows: res.object.map((m, i) => {
            return {
              ...m,
              index: i + 1
            };
          })
        });
      });
    },

    unusualActionTableSearch() {
      this.$refs.unusualActionTable.pageNo = 1;
      this.handleGetUnusualActionInfo();
    },

    async handleGetUnusualActionInfo() {
      let pageNo = this.$refs.unusualActionTable.pageNo,
        pageSize = this.$refs.unusualActionTable.pageSize,
        employeeId = this.infoData.employee_id,
        searchForm = {
          pageNo,
          pageSize,
          sidx: 'cd.create_date',
          sord: 'desc',
          employeeId,
          ...this.searchForm
        };
      let res = await this.ajax.changesDetailedList(searchForm);

      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.unusualActionTable.refresh({
        ...res,
        rows
      });
    },

    close() {
      this.tabsStatus = undefined;
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-salary-profile-details {
    .el-dialog__footer {
      width: calc(100% - 45px) !important;
      min-width: 1090px !important;
    }

    .el-dialog__body {
      min-width: 1090px !important;
      width: calc(100% - 45px) !important;
      height: calc(100% - 102px) !important;
      padding: 8px !important;
      overflow: auto;

      .no-list-tips {
        margin-bottom: 8px;
      }

      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .item-tips {
          font-size: 16px;
          font-weight: 700;
          margin-right: 8px;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          &.small {
            font-size: 14px;
            font-weight: 400;
          }

          &::before {
            content: '';
            display: inline-block;
            color: rgb(82, 96, 255);
            height: 16px;
            width: 6px;
            border-radius: 4px;
            background-color: rgb(82, 96, 255);
            margin-right: 4px;
          }
        }

        .person-info-descriptions {
          margin-bottom: 8px;
        }

        .tabs-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: auto;

          .unusual-action-table-container {
            height: 100%;
            display: flex;
            flex-direction: column;
          }

          .form-table {
            flex: 1;
            overflow: hidden;
            transform: scale(1);

            .details-span {
              color: $primary-blue;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}
</style>
