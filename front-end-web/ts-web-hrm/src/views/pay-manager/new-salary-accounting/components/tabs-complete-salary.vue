<template>
  <div class="tabs-container" id="TabsCompleteSalary">
    <div class="content-container">
      <ts-button
        :disabled="['2', '3'].includes(details.computeStatus)"
        type="primary"
        class="complete-set-width-btn"
        @click="handleCompleteCompute"
        :loading="btnLoading"
      >
        完成核算
      </ts-button>

      <template v-if="details.computeStatus == '1'">
        <p class="top-tips">
          点击完成后，薪资报表中可以查询相关数据，可手工生成工资条
        </p>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    details: {
      type: Object,
      default: () => {}
    }
  },
  data: () => {
    return {
      btnLoading: false
    };
  },
  methods: {
    // 完成核算
    async handleCompleteCompute() {
      try {
        await this.$confirm('确定完成核算吗？', '提示', {
          type: 'warning'
        });
        this.btnLoading = true;
        const res = await this.ajax.newSalaryOptionPayrollComplete({
          id: this.details.id
        });
        this.btnLoading = false;
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('updateStatus');
          this.$emit('close');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.btnLoading = false;
      }
    }
  }
};
</script>

<style lang="scss">
.complete-set-width-btn {
  width: 120px;
  height: 38px !important;
}
</style>

<style lang="scss" scoped>
#TabsCompleteSalary {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px 0;

  .ts-button {
    &.is-disabled {
      color: rgb(204, 204, 204) !important;
      border-color: rgb(231, 235, 240) !important;
      background-color: rgb(250, 250, 250) !important;
      &:hover {
        cursor: not-allowed;
      }
    }
  }
  .content-container {
    width: 100%;
    height: 100%;

    background-color: #eee;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .top-tips {
      font-size: 14px;
      font-weight: 800;
      margin-top: 8px;
      margin-bottom: 0px;
    }
  }
}
</style>
