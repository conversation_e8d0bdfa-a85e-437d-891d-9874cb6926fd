<template>
  <div class="table-month-salary-summary">
    <!-- <div class="filterate">
      <ts-form ref="ruleForm" :model="filterSearchForm" labelWidth="100px">
        <div style="display: flex;align-items: center;">
          <ts-form-item label="当前筛选器:" style="margin: 8px 8px 0 0">
            <ts-select
              v-model="filterSearchForm.filterName"
              filterable
              clearable
              placeholder="请选择"
              @change="handleUseFilterSearch"
            >
              <ts-option
                v-for="item in filterOptions"
                :key="item.id"
                :label="item.filterName"
                :value="item.id"
              >
              </ts-option>
            </ts-select>
          </ts-form-item>
          <ts-button type="primary" @click="handleSubmitFilter">
            保存
          </ts-button>
          <ts-button @click="handleDeleteFilter">删除</ts-button>
        </div>
      </ts-form>
    </div> -->
    <div class="trasen-container flex-column">
      <ul class="search-type-tabs">
        <li
          :class="{
            'search-item': 'true',
            active: item.value == searchForm.optionId
          }"
          v-for="(item, index) in optionsArr"
          @click="handleClickSearchTabs(item)"
          :key="index"
        >
          {{ item.label }}
        </li>
      </ul>

      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :resetData="resetData"
        :elementCol="14"
        @search="search"
      >
        <template slot="date">
          <el-date-picker
            v-model="searchForm.date"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            @change="
              e => {
                this.$set(this.searchForm, 'date', e);
                this.search();
              }
            "
            :clearable="false"
          />
        </template>
        <template slot="age">
          <ts-number-range v-model="searchForm.age" type="decimal" />
        </template>
        <template slot="deptTree">
          <ts-ztree-select
            placeholder="请选择部门"
            ref="treeSelect"
            :inpText.sync="searchForm.orgName"
            :inpVal.sync="searchForm.code"
            :data="treeData"
            defaultExpandAll
            @change="handleChangeTree"
            @before-change="handleTreeBeforeChange"
          />
        </template>
        <template slot="right">
          <ts-button type="primary" @click="handleOpenAddExport">
            导 出
          </ts-button>
          <!-- <el-dropdown
            class="export-btn"
            split-button
            size="mini"
            type="primary"
            @click="handleOpenAddExport"
          >
            导出
            <el-dropdown-menu class="export-drop-btn" slot="dropdown">
              <el-dropdown-item v-for="item in exportOptions" :key="item.id">
                <div
                  class="dropdowm-text"
                  @click="() => handleSelectExportItem(item)"
                >
                  <span>{{ item.filterName }}</span>
                  <span @click.stop="() => handleDeleteExportItem(item)">
                    删除
                  </span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </template>
      </ts-search-bar>
      <!-- <base-table
        ref="table"
        class="form-table"
        border
        stripe
        :columns="columns"
        @refresh="handleRefreshTable"
      /> -->
      <TsVxeTemplateTable
        id="table_month_salary_summary"
        class="form-table"
        ref="table"
        :columns="columns"
        :span-method="colspanMethod"
        @refresh="handleRefreshTable"
      />
      <div class="footer_total">
        <salary-total-columns :totalData="totalData" />
      </div>
    </div>

    <dialog-filter-form
      :filterSearchId="filterSearchId"
      ref="DialogFilterForm"
      @refreshFilter="handleGetFilterOptions"
    />

    <dialog-export-field
      :filterExportId="filterExportId"
      :personInformation="personInformation"
      :salaryItem="salaryItem"
      ref="DialogExportField"
      @refreshExportFilter="handleGetExportBtn"
    />
  </div>
</template>

<script>
import DialogFilterForm from './dialog-filter-form.vue';
import DialogExportField from './dialog-export-field.vue';
import searchList from '../mixins/searchList';
import { deepClone } from '@/unit/commonHandle.js';
import SalaryTotalColumns from '@/views/pay-manager/components/salary-total-columns.vue';

export default {
  components: {
    DialogFilterForm,
    DialogExportField,
    SalaryTotalColumns
  },
  mixins: [searchList],
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterSearchId: 'sarary-filter-id',
      filterExportId: 'sarary-export-id',
      tableData: [],
      totalData: [],

      filterSearchForm: {},
      filterOptions: [],

      exportOptions: [],
      optionsArr: [],

      operationkeys: [
        'optionId',
        'employeeStatus',
        'establishmentTypes',
        'nationalityes',
        'employeeCategorys',
        'politicalStatuses',
        'marriageStatuses',
        'positionNames',
        'orgAttributestypes',
        'plgws',
        'gwdjs',
        'operationTypes',
        'personalIdentitys',
        'jobtitleCategory',
        'jobtitleName',
        'educationTypes'
      ],

      columns: [],
      personInformation: [],
      salaryItem: []
    };
  },
  methods: {
    colspanMethod({ _rowIndex, _columnIndex }) {
      if (_rowIndex === this.tableData.length - 1) {
        if (_columnIndex === 0) {
          return { rowspan: 0, colspan: 0 };
        } else if (_columnIndex === 1) {
          return { rowspan: 1, colspan: 6 };
        } else if (
          _columnIndex === 2 ||
          _columnIndex === 3 ||
          _columnIndex === 4 ||
          _columnIndex === 5
        ) {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },
    handleClickSearchTabs(item) {
      this.$refs.table.pageNo = 1;
      this.searchForm.optionId = item.value;
      this.resetData.optionId = item.value;
      this.handleRefreshTable();
    },

    handleChangeTree(e) {
      const { id, name } = e;
      this.searchForm.code = id;
      this.searchForm.orgName = name;
    },

    handleTreeBeforeChange(e, treeObj, fn) {
      return true;
    },

    refresh() {
      // this.handleGetFilterOptions();
      // this.handleGetExportBtn();
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      // this.filterSearchForm.filterName = '';
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      await this.handleGetProgrammeList();
      await this.handleSalaryAccountDetailsTotal();
      this.handleGetDictItemList('establishment_type', 'establishmentTypes');
      this.handleGetDictItemList('nationality_name', 'nationalityes');
      this.handleGetDictItemList('employee_category', 'employeeCategorys');
      this.handleGetDictItemList('political_status', 'politicalStatuses');
      this.handleGetDictItemList('marriage_status', 'marriageStatuses');
      this.handleGetDictItemList('ORG_ATTRIBUTES', 'orgAttributestypes');
      this.handleGetDictItemList('post_category', 'plgws');
      this.handleGetDictItemList('operation_type', 'operationTypes');
      this.handleGetDictItemList('personal_identity', 'personalIdentitys');
      this.handleGetDictItemList('education_type', 'educationTypes');

      this.handleGetPositionList();
      this.handleGetPostGetPageList();
      this.handleGetCategoryPageList();
      this.handleGetCategoryPageList2();
      this.handleGetCheckBoxList();

      this.handleGetTableHead();
      this.handleGetTableData();
    },

    async handleGetProgrammeList() {
      const res = await this.ajax.newSalaryOptionAllList();
      if (!res.success) {
        this.$message.error(res.message || '获取方案失败!');
        return;
      }
      let data = deepClone(res.object || []);
      this.optionsArr = data.map(m => {
        return {
          label: m.optionName,
          value: m.id
        };
      });

      if (this.optionsArr && this.optionsArr.length) {
        if (!this.searchForm.optionId) {
          this.$set(this.searchForm, 'optionId', this.optionsArr[0].value);
        }
      } else {
      }
    },

    // 获取导出项
    async handleGetCheckBoxList() {
      let optionId = this.searchForm.optionId;
      const res = await this.ajax.selectMonthlySalaryGetExportColumns({
        optionId
      });
      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }
      let rsxx = res.object?.rsxx || [];
      let gzx = res.object?.gzx || [];

      this.personInformation = Object.keys(rsxx).map(key => {
        return { label: rsxx[key], id: key };
      });
      this.salaryItem = Object.keys(gzx).map(key => {
        return { label: gzx[key], id: key };
      });
    },

    handleSalaryAccountDetailsTotal() {
      let param = {
        optionId: this.searchForm.optionId,
        startDate: this.searchForm.date[0],
        endDate: this.searchForm.date[1]
      };

      this.ajax.sendSalarySummaryData(param).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取汇总信息失败');
          return;
        }
        this.totalData = res.object;
      });
    },

    handleOpenAddExport() {
      this.$refs.DialogExportField.open({
        type: 'add',
        data: {}
      });
    },

    handleSelectExportItem(data) {
      this.$refs.DialogExportField.open({
        type: 'edit',
        data
      });
    },

    // 提交过滤器
    handleSubmitFilter() {
      this.$refs.DialogFilterForm.open();
    },

    // 使用过滤器
    handleUseFilterSearch(val) {
      let find = this.filterOptions.find(f => f.id == val);

      if (find.id) {
        this.$set(this, 'searchForm', {});

        let conditionMap = JSON.parse(find.conditionMap);

        this.operationkeys.forEach(f => {
          if (typeof conditionMap[f] == 'string' && conditionMap[f]) {
            conditionMap[f] = conditionMap[f].split(',');
          }
        });
        this.$set(this, 'searchForm', conditionMap);
        this.handleRefreshTable();
      } else {
        this.$set(this, 'searchForm', {});
        this.handleRefreshTable();
      }
    },

    // 删除过滤器
    async handleDeleteFilter() {
      let val = this.filterSearchForm.filterName;

      if (!val) {
        this.$message.warning('请先选择一个筛选器!');
      } else {
        try {
          await this.$confirm('是否删除该条筛选器?', '提示', {
            type: 'warning'
          });
          const res = await this.ajax.personalFilterDeleteById(val);
          if (res.success) {
            this.$set(this, 'searchForm', {});
            this.$set(this.filterSearchForm, 'filterName', '');

            this.$message.success('删除成功!');
            this.refresh();
          } else {
            this.$message.error(res.message || '删除失败!');
          }
        } catch (e) {
          console.error(e);
        }
      }
    },

    async handleDeleteExportItem(data) {
      try {
        await this.$confirm('是否删除这条数据?', '提示', {
          type: 'warning'
        });

        const res = await this.ajax.personalFilterDeleteById(data.id);
        if (res.success) {
          this.$message.success('删除成功!');
          this.refresh();
        } else {
          this.$message.error(res.message || '删除失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleExport(exportJson) {
      let data = deepClone(this.handleGetSearchFromData());
      data.exportJson = exportJson;

      const res = await this.ajax.exportSubmitData(data);
      if (res.success && res.statusCode === 200) {
        let aDom = document.createElement('a');
        aDom.href = `/ts-hrms/api/selectMonthlySalary/export`;
        aDom.click();
      } else {
        this.$message.error(res.message || '操作失败!');
      }
    },

    async handleGetExportBtn() {
      let data = {
        menuId: this.filterExportId
      };
      const res = await this.ajax.personalFilterGetList(data);

      if (!res.success) {
        this.$message.error(res.message || '获取导出集合失败');
        return;
      }

      this.exportOptions = res.object;
    },

    async handleGetFilterOptions() {
      let data = {
        menuId: this.filterSearchId
      };
      const res = await this.ajax.personalFilterGetList(data);

      if (!res.success) {
        this.$message.error(res.message || '获取过滤器集合失败');
        return;
      }

      this.filterOptions = res.object;
    },

    async handleGetTableHead() {
      this.columns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        }
      ];
      let res = await this.ajax.selectMonthlySalarySalaryCountTitle(
        this.searchForm.optionId
      );
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        this.columns.push(...res.object);
      }
    },

    async handleGetTableData() {
      let resultForm = deepClone(this.handleGetSearchFromData());
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          pageNo,
          pageSize,
          ...resultForm
        };

      let res = await this.ajax.selectMonthlySalarySalaryCountData(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.tableData = deepClone(rows);
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.export-drop-btn {
  ::v-deep {
    .el-dropdown-menu__item {
      padding: 4px 8px !important;
      .dropdowm-text {
        white-space: nowrap;
        padding: 4px 16px;
      }
      &:hover {
        opacity: 0.8 !important;

        color: rgb(255, 255, 255) !important;
        background-color: $primary-blue !important;
      }
    }
  }
}
.table-month-salary-summary {
  height: calc(100% - 44px);
  display: flex;
  flex-direction: column;
  .filterate {
    height: 45px;
  }
  .trasen-container {
    padding-top: 0px !important;
    position: relative;
    flex: 1;

    .search-type-tabs {
      display: flex;
      margin-bottom: 8px;
      .search-item {
        cursor: pointer;
        margin-right: 8px;
        border-radius: 4px;
        border: 2px solid #e1e1e1;
        padding: 8px 12px;
        font-weight: 800;
        line-height: 32px;
        height: 32px;
        &.active {
          color: $primary-blue;
          border: 2px solid $primary-blue;
        }
      }
    }

    .footer_total {
      position: relative;
      left: 0;
      bottom: 0;
      z-index: 9999;
    }

    .export-btn {
      ::v-deep {
        .el-button {
          color: rgb(255, 255, 255);
          background-color: $primary-blue !important;
          border-color: $primary-blue !important;
          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
    ::v-deep {
      .more-text-btn {
        width: 130px;
        span {
          text-overflow: unset !important;
        }
      }
      .form-table {
        flex: 1;
        overflow: hidden;
        transform: scale(1);
      }
    }
  }
}
</style>
