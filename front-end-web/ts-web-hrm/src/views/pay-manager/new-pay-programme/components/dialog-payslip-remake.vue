<template>
  <ts-dialog
    custom-class="dialog-payslip-remake"
    title="备注"
    :visible.sync="visible"
    :append-to-body="true"
    @close="remakeClose"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item label="备注" prop="salaryRemark" :rules="rules.required">
          <ts-input
            v-model="form.salaryRemark"
            type="textarea"
            class="textarea"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="handleRemakeSubmit">
        提 交
      </ts-button>
      <ts-button @click="remakeClose">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  data() {
    return {
      rules: {
        required: { required: true, message: '必填' }
      },
      form: {},

      remakeRow: {},
      visible: false
    };
  },
  methods: {
    open({ data }) {
      this.$set(this.form, 'salaryRemark', data.salaryRemark || '');
      this.remakeRow = data;

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },

    async handleRemakeSubmit() {
      try {
        await this.$refs.form.validate();
        let salaryRemark = this.form.salaryRemark;
        this.remakeRow.salaryRemark = salaryRemark;

        this.$message.success('备注成功!');
        this.remakeClose();
      } catch (error) {
        console.error(error);
      }
    },

    remakeClose() {
      this.visible = false;
    }
  }
};
</script>
