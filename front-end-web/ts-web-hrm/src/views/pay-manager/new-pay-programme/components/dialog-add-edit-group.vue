<template>
  <ts-dialog
    class="dialog-add-edit-group"
    :title="title"
    width="600px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item label="分组名称" prop="itemGroup" :rules="rules.required">
          <ts-input v-model="form.itemGroup" placeholder="请输入" />
        </ts-form-item>

        <ts-form-item label="排序" prop="seqNo" :rules="rules.required">
          <ts-input
            v-model="form.seqNo"
            placeholder="请输入"
            @input="value => (form.seqNo = (value.match(/\d+/g) || [''])[0])"
            :maxLength="2"
          />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    }
  },
  data() {
    return {
      visible: false,

      title: '',
      type: '',

      form: {},
      optionId: undefined,
      rules: {
        required: { required: true, message: '必填' }
      },
      submitLoading: false
    };
  },
  methods: {
    open(data) {
      this.title = data.title;
      this.type = data.type;
      this.optionId = data.optionId;
      this.form = data.data;

      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
      this.visible = true;
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        data.optionId = this.optionId;

        let API = null;
        switch (this.type) {
          case 'add':
            API = this.ajax.salaryItemGroupSave;
            break;
          case 'edit':
            API = this.ajax.salaryItemGroupUpdate;
            break;
        }

        this.submitLoading = true;
        const res = await API(data);
        this.submitLoading = false;
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.close();
          this.$emit('addSuccess');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped></style>
