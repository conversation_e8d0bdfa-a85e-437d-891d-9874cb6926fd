<template>
  <ts-dialog
    class="dialog-change-salary-group"
    title="选择分组"
    width="600px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item label="分组" prop="groupId" :rules="rules.required">
          <ts-select
            style="width: 100%"
            v-model="form.groupId"
            clearable
            placeholder="请选择"
          >
            <ts-option
              v-for="item of salaryProgrammeGroup"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  data() {
    return {
      visible: false,

      salaryProgrammeGroup: [],

      form: {},
      details: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      submitLoading: false
    };
  },
  methods: {
    async open({ data, optionId }) {
      this.form = {};
      this.details = deepClone(data);

      await this.handleGetSalaryItemGroupListByOptionIdSel(optionId);
      this.visible = true;

      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },

    async handleGetSalaryItemGroupListByOptionIdSel(optionId) {
      const res = await this.ajax.salaryItemGroupListByOptionIdSel({
        optionId
      });
      if (!res.success) {
        this.$message.error(res.message || '获取方案失败!');
        return;
      }

      let arr = deepClone(res.object || []);
      this.salaryProgrammeGroup = arr.map(m => {
        return {
          label: m.itemGroup,
          value: m.id
        };
      });
    },

    async submit() {
      try {
        await this.$refs.form.validate();

        let data = Object.assign(this.details, this.form);

        this.submitLoading = true;
        const res = await this.ajax.salaryItemUpdate(data);
        this.submitLoading = false;

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.close();
          this.$emit('addSuccess');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped></style>
