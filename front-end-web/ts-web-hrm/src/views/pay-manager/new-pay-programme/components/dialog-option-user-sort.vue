<template>
  <ts-dialog
    custom-class="dialog-operate-logger"
    :title="title"
    width="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="tabs-content">
        <ts-search-bar
          v-model="searchForm"
          :formList="searchList"
          :elementCol="14"
          @search="search"
        >
        </ts-search-bar>
        <el-table
          ref="table"
          class="form-table"
          :data="dataSource"
          style="width: 100%"
          height="87%"
          border
          stripe
          :show-overflow-tooltip="true"
        >
          <el-table-column
            :show-overflow-tooltip="true"
            width="36"
            align="center"
            type="index"
            prop="index"
          ></el-table-column>
          <el-table-column
            label="工号"
            :show-overflow-tooltip="true"
            align="center"
            prop="employeeNo"
          ></el-table-column>
          <el-table-column
            label="姓名"
            :show-overflow-tooltip="true"
            align="center"
            prop="employeeName"
          >
          </el-table-column>
          <el-table-column
            label="排序号"
            :show-overflow-tooltip="true"
            align="center"
            prop="sortNum"
          >
            <template slot-scope="scope">
              <ts-input
                v-if="scope.row.isEdit == '1'"
                class="right-aligned-input"
                v-model="scope.row.sortNum"
                type="number"
              />
              <div v-else>
                {{ scope.row.sortNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <ts-button
                v-if="scope.row.isEdit == ''"
                @click="handleEdit(scope.$index, scope.row)"
                >编辑</ts-button
              >
              <ts-button
                type="primary"
                v-if="scope.row.isEdit == '1'"
                @click="handleSave(scope.$index, scope.row)"
                >保存</ts-button
              >
              <ts-button
                v-if="scope.row.isEdit == '1'"
                @click="handleCancel(scope.$index, scope.row)"
                >取消</ts-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {},
  data() {
    return {
      visible: false,
      title: '人员排序',
      oldData: [], //原列表数据
      dataSource: [], //列表数据
      searchForm: {},
      searchList: [
        {
          label: '姓名/工号',
          value: 'employeeNo',
          element: 'TsInput',
          elementProp: {
            placeholder: '请输入姓名/工号',
            clearable: true
          }
        }
      ],
      oldRow: [],
      operIndex: -1
    };
  },
  created() {},
  methods: {
    open({ data }) {
      this.oldRow = [];
      this.operIndex = -1;
      this.title = '方案[' + data.optionName + ']人员排序';
      let empList = data.empList;
      let rows = empList.map((item, i) => {
        let index = i + 1;
        return {
          isEdit: '',
          index,
          ...item
        };
      });
      this.oldData = deepClone(rows);
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.search();
      });
    },
    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    //搜索
    async search() {
      await this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let searchName = this.searchForm.employeeNo;
      let data = deepClone(this.oldData) || [];
      if (searchName) {
        let row = data.filter(
          item =>
            item.employeeNo.includes(searchName) ||
            item.employeeName.includes(searchName)
        );
        this.dataSource = deepClone(row);
      } else {
        this.dataSource = deepClone(data);
      }
      this.dataSource.sort((a, b) => {
        return a.sortNum - b.sortNum;
      });
    },
    //关闭
    close() {
      this.form = null;
      this.fileList = [];
      this.type = undefined;
      this.visible = false;
    },
    async handleEdit(index, row) {
      if (this.operIndex > -1 && index != this.operIndex) {
        await this.$confirm(
          `人员${this.oldRow.employeeName}的排序尚未保存，是否保存?`,
          '提示',
          {
            type: 'warning'
          }
        )
          .then(() => {
            this.handleSave(this.operIndex);
          })
          .catch(() => {
            this.dataSource[this.operIndex].sortNum = this.oldRow.sortNum;
            this.dataSource[this.operIndex].isEdit = '';
          });
      }
      row.isEdit = '1';
      this.oldRow = deepClone(row);
      this.operIndex = index;
    },
    //保存
    async handleSave(index) {
      this.dataSource[index].isEdit = '';
      let data = this.dataSource[index];
      const res = await this.ajax.newSalaryOptionEmpSort({
        id: data.id,
        sortNum: data.sortNum
      });
      if (!res.success) {
        this.$message.error(res.message || '操作失败!');
        return false;
      }
      //更新数据
      for (let i = 0; i < this.oldData.length; i++) {
        if (this.oldData[i].id == data.id) {
          this.oldData[i].sortNum = data.sortNum;
          break;
        }
      }
      this.handleRefreshTable();
      this.$message.success('操作成功!');
    },
    //取消
    handleCancel(index, row) {
      row.isEdit = '';
      this.dataSource[index].sortNum = this.oldRow.sortNum;
      this.oldRow = {};
      this.operIndex = -1;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-operate-logger {
    height: 70%;
    .ts-button {
      &.is-disabled {
        color: rgb(204, 204, 204) !important;
        border-color: rgb(231, 235, 240) !important;
        background-color: rgb(250, 250, 250) !important;
        &:hover {
          cursor: not-allowed;
        }
      }
    }
    .el-dialog__header {
      height: 42px !important;
      padding: 10px 20px !important;
    }
    .title-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        transform: translateY(14px);
        > p {
          font-weight: 800;
          margin: 0;
        }
      }
    }

    .el-dialog__footer {
      width: calc(100% - 45px) !important;
      //min-width: 1090px !important;

      .dialog-footer {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }

    .el-dialog__body {
      //min-width: 1090px !important;
      width: calc(100% - 45px) !important;
      height: calc(100% - 132px) !important;
      padding: 8px !important;
      overflow: auto;
      .content {
        height: 90%;
        position: relative;
        .tabs-content {
          position: absolute;
          left: 0;
          top: 0px;
          width: 100%;
          height: calc(100% - 0px);
          .tabs-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            .form-table {
              flex: 1;
              overflow: hidden;
              transform: scale(1);
              .vxe-footer--column {
                color: rgb(255, 121, 0);
              }

              .el-table__body-wrapper {
                margin-top: 31px !important;
              }

              .details-span {
                color: $primary-blue;
                cursor: pointer;
              }

              .operation-span {
                color: $primary-blue;
                margin-right: 8px;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
