<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTab">
      <ts-tab-pane label="方案异动办理" name="1"></ts-tab-pane>
      <ts-tab-pane label="薪资异动办理" name="2"></ts-tab-pane>
      <ts-tab-pane label="薪级等级调整" name="3"></ts-tab-pane>
      <ts-tab-pane label="提醒设置" name="4"></ts-tab-pane>
    </ts-tabs>

    <tab-newsalary-options-changes
      ref="TabNewsalaryOptionsChanges"
      v-if="activeTab == '1'"
    />
    <tab-newsalary-item-changes
      ref="TabNewsalaryItemChanges"
      v-if="activeTab == '2'"
    />
    <tab-newsalary-level-changes
      ref="TabNewsalaryLevelChanges"
      v-if="activeTab == '3'"
    />
    <tab-newsalary-remind-setting
      ref="TabNewsalaryRemindSetting"
      v-if="activeTab == '4'"
    />
  </div>
</template>

<script>
import TabNewsalaryOptionsChanges from './components/tab-newsalary-options-changes.vue';
import TabNewsalaryItemChanges from './components/tab-newsalary-item-changes.vue';
import TabNewsalaryLevelChanges from './components/tab-newsalary-level-changes.vue';
import TabNewsalaryRemindSetting from './components/tab-newsalary-remind-setting.vue';
export default {
  components: {
    TabNewsalaryOptionsChanges,
    TabNewsalaryItemChanges,
    TabNewsalaryLevelChanges,
    TabNewsalaryRemindSetting
  },
  data() {
    return {
      activeTab: '1',
      computeDate: ''
    };
  },
  created() {
    this.activeTab = this.$route.query.activeTab || '1';
  },
  watch: {
    activeTab() {
      this.refresh();
    }
  },
  methods: {
    refresh() {
      let dic = {
        1: 'TabNewsalaryOptionsChanges',
        2: 'TabNewsalaryItemChanges',
        3: 'TabNewsalaryLevelChanges',
        4: 'TabNewsalaryRemindSetting'
      };

      this.$nextTick(() => {
        if (
          typeof this.$route.query.date != 'undefined' &&
          this.activeTab != 3
        ) {
          this.$refs[
            dic[this.activeTab]
          ].searchForm.computeDate = this.$route.query.date;
        }
        this.$refs[dic[this.activeTab]].handleRefreshTable();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.trasen-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
