<template>
  <div class="summary-and-report-box">
    <div class="left-tree-content">
      <base-search-tree
        class="attendance-tree"
        ref="searchTree"
        placeholder="输入科室名进行搜索"
        :apiFunction="apiFunction"
        :params="treeParams"
        @tree="getTreeSuccess"
        @beforeClick="clickItemTree"
      />
    </div>
    <div class="right-table-content">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :actions="actions"
        @search="search"
        :resetData="handleReset"
      >
        <template slot="employeeStatuses">
          <ts-select
            style="width: 100%"
            v-model="searchForm.employeeStatuses"
            clearable
            multiple
            placeholder="请选择"
          >
            <ts-option
              v-for="item of employeeStatusData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>
        <template slot="establishmentTypes">
          <ts-select
            style="width: 100%"
            v-model="searchForm.establishmentTypes"
            clearable
            placeholder="请选择"
          >
            <ts-option
              v-for="item of establishmentTypeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </template>

        <template slot="right">
          <ts-button @click="handleExport()">
            导出
          </ts-button>
          <ts-button type="primary" @click="handleAdjust(1)">
            批量调整
          </ts-button>
          <ts-button type="danger" @click="handleAdjust(0)">
            批量不调整
          </ts-button>
        </template>
      </ts-search-bar>
      <div class="process" v-if="isShowProgress">
        <span>进度：</span>
        <progress :value="progress" max="100" background="blue"></progress>
        <span style="margin-left: 5px;">{{ progress }}%</span>
      </div>

      <TsVxeTemplateTable
        id="table_newsalary_level_changes_"
        class="form-table"
        ref="table"
        :columns="columns"
        :checkboxConfig="{
          checkMethod: showCheckboxkMethod
        }"
        @selection-change="handleSelectChange"
        @refresh="handleRefreshTable"
      />
    </div>
  </div>
</template>

<script>
import { getOrganizationSelectZTreeList } from '@/api/ajax/personManage/empTemporary.js';
import table from './tab-newsalary-level-changes.js';
export default {
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  mixins: [table],
  components: {},
  data() {
    return {
      org: {
        orgId: '',
        orgName: ''
      },
      isShowProgress: false,
      progress: 0, //执行进度
      eventSource: null, //sse客户端链接
      treeParams: {},
      selectList: [],
      apiFunction: getOrganizationSelectZTreeList,
      establishmentTypeData: [],
      employeeStatusData: [],
      salaryProfileOptions: [],
      dialogOrganizationTreeData: []
    };
  },
  created() {
    this.getEstablishmentTypeData();
    this.getEmployeeStatusData();
    this.handleGetNewSalaryOptionAllList();
  },
  beforeDestroy() {
    this.eventSource.close();
  },
  methods: {
    //组织树加载成功
    async getTreeSuccess(data) {
      this.activeId = data[0].id;
      this.dialogOrganizationTreeData = data;
      this.org.orgId = data[0].id;
      this.org.orgName = data[0].name;
      await this.handleRefreshTable();
    },
    //组织树点击
    async clickItemTree(node) {
      this.$refs.table.pageNo = 1;
      this.org.orgId = node.id;
      this.org.orgName = node.name;
      await this.handleRefreshTable();
    },
    //检索
    search() {
      //隐藏进度条
      this.isShowProgress = false;

      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let employeeStatuses = null;
      if (this.searchForm.employeeStatuses) {
        employeeStatuses = this.searchForm.employeeStatuses.join(',');
      }
      let formData = {
        ...this.searchForm,
        ...this.org,
        employeeStatuses,
        pageNo,
        pageSize
      };
      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        }
      });
      let res = await this.ajax.getNewsalaryLevelRemindRecordList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    //重置
    handleReset() {
      if (this.dialogOrganizationTreeData) {
        this.org.orgId = this.dialogOrganizationTreeData[0].id;
        this.org.orgName = this.dialogOrganizationTreeData[0].name;
      }
      return {
        adjustDate: this.$moment().format('YYYY-MM')
      };
    },
    showCheckboxkMethod({ row }) {
      return row.handleStatus != '1';
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    //导出
    handleExport() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let employeeStatuses = null;
      if (this.searchForm.employeeStatuses) {
        employeeStatuses = this.searchForm.employeeStatuses.join(',');
      }
      let formData = {
        ...this.searchForm,
        ...this.org,
        employeeStatuses,
        pageNo,
        pageSize
      };

      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.id = 'down-file-iframe';

      const form = document.createElement('form');
      form.method = 'get';
      form.target = iframe.id;
      form.action = '/ts-hrms/api/hrmsNewsalaryLevelRemindRecord/downLoad';

      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        } else {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = formData[key];
          form.appendChild(input);
        }
      });
      delete formData.createDateList;

      iframe.appendChild(form);
      document.body.appendChild(iframe);

      form.submit();
      document.body.removeChild(iframe);

      this.exportLoading = true;
      setTimeout(() => {
        this.exportLoading = false;
      }, 1500);
    },
    //确认调整
    // async handleAdjust() {
    //   if (this.selectList.length == 0) {
    //     this.$message.warning('请选择要调整的数据');
    //     return;
    //   }

    //   this.$refs.DialogLevelAdjust.open({
    //     data: {
    //       list: this.selectList
    //     }
    //   });
    // },
    //一键调整
    async handleAdjust(type) {
      if (this.selectList.length == 0) {
        this.$message.warning('请选择要调整的数据');
        return;
      }
      let message = '调整后的数据将同步至薪酬档案并立刻生效，是否一键确认？';
      if (type == '0') {
        message =
          '该操作仅影响当前记录的操作状态，不同步薪酬档案数据的调整，是否一键确认？';
      }
      try {
        await this.$confirm(message, '提示', {
          type: 'warning'
        });
        let employeeNames = [];
        this.selectList.forEach(function(item) {
          if (!item.optionId) {
            employeeNames.push(item.employeeName);
          }
        });
        if (employeeNames && employeeNames.length > 0 && type == '1') {
          this.$message.error(
            '员工[' +
              JSON.stringify(employeeNames) +
              ']尚未加入薪酬方案，不支持批量调整!'
          );
          return;
        }
        let list = this.selectList.map(item => item.id);
        this.isShowProgress = true;
        this.progress = 0;
        this.eventSource = new EventSource(
          '/ts-hrms/api/hrmsNewsalaryLevelRemindRecord/batchAdjust?isAdjust=' +
            type +
            '&ids=' +
            list.join(',')
        );
        this.eventSource.onmessage = e => {
          if (e.data.includes('message')) {
            this.$message.warning(
              '批量操作失败，' + e.data.split('message:')[1]
            );
            this.eventSource.close();
          } else {
            this.progress = parseInt(e.data);
            if (this.progress >= 100) {
              this.eventSource.close();
              this.$message.success('操作成功!');
              this.search();
            }
          }
        };
        this.eventSource.onerror = e => {
          this.$message.warning('批量操作失败，请稍后再试！');
          this.eventSource.close();
        };
      } catch (error) {
        if (
          error.code === 'ECONNABORTED' &&
          error.message.includes('timeout')
        ) {
          this.$message.warning('请求超时, 请联系管理员!');
          this.computedLoading = false;
        }
      }
    },
    // async handleAdjust(type) {
    //   if (this.selectList.length == 0) {
    //     this.$message.warning('请选择要调整的数据');
    //     return;
    //   }
    //   let message = '调整后的数据将同步至薪酬档案并立刻生效，是否一键确认？';
    //   if (type == '0') {
    //     message =
    //       '该操作仅影响当前记录的操作状态，不同步薪酬档案数据的调整，是否一键确认？';
    //   }
    //   try {
    //     await this.$confirm(message, '提示', {
    //       type: 'warning'
    //     });
    //     let employeeNames = [];
    //     this.selectList.forEach(function(item) {
    //       if (!item.optionId) {
    //         employeeNames.push(item.employeeName);
    //       }
    //     });
    //     if (employeeNames && employeeNames.length > 0 && type == '1') {
    //       this.$message.error(
    //         '员工[' +
    //           JSON.stringify(employeeNames) +
    //           ']尚未加入薪酬方案，不支持批量调整!'
    //       );
    //       return;
    //     }
    //     let list = this.selectList.map(item => ({
    //       ...item,
    //       isAdjust: type
    //     }));
    //     const res = await this.ajax.handleBatchAdjustLevel(list);
    //     if (res.success && res.statusCode === 200) {
    //       this.$message.success('操作成功!');
    //       this.handleRefreshTable();
    //     } else {
    //       this.$message.error(res.message || '操作失败!');
    //     }
    //   } catch (e) {
    //     console.error(e);
    //   }
    // },

    /**@desc 获取编制类型 */
    getEstablishmentTypeData() {
      this.ajax.getDataByDataLibrary('establishment_type').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '编制类型获取失败');
          return;
        }
        this.establishmentTypeData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    /**@desc 获取员工状态 */
    getEmployeeStatusData() {
      this.ajax.getDataByDataLibrary('employee_status').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '员工状态获取失败');
          return;
        }
        this.employeeStatusData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    // 获取薪酬方案
    async handleGetNewSalaryOptionAllList() {
      let res = await this.ajax.newSalaryOptionAllList();
      if (res.success == false) {
        this.$message.error(res.message || '薪酬组数据获取失败');
        return;
      }
      this.salaryProfileOptions = (res.object || []).map(m => {
        return {
          label: m.optionName,
          value: m.id,
          element: 'ts-option'
        };
      });

      this.searchList.find(
        f => f.value == 'optionId'
      ).childNodeList = this.salaryProfileOptions;
    }
  }
};
</script>

<style lang="scss" scoped>
.summary-and-report-box {
  height: calc(100% - 8px);
  margin-bottom: 8px;
  display: flex;
  .process {
    position: absolute;
    right: 250px;
    top: 15px;
  }
  /* 重置默认样式 */
  progress {
    -webkit-appearance: none; /* For Safari and Chrome */
    -moz-appearance: none; /* For Firefox */
    appearance: none; /* Standard property */
    border: none;
    border-radius: 5px;
    background-color: #f3f3f3;
  }

  /* 设置进度条填充颜色 */
  progress::-webkit-progress-bar {
    background-color: #f3f3f3;
  }

  progress::-webkit-progress-value {
    background-color: #5260ff;
  }

  /* For Firefox */
  progress::-moz-progress-bar {
    background-color: #5260ff;
  }
  & > div {
    height: 100%;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
    border: 1px solid #e4e4e4;
  }

  .left-tree-content {
    width: 220px;
    border-radius: 8px;
    background: #fff;
    height: 100%;
    margin-right: 8px;
    padding: 8px;
    /deep/ .attendance-tree {
      .button.attendance-icon_ico_docu,
      .button.attendance-icon_ico_open,
      .button.attendance-icon_ico_close {
        position: relative;
        width: 0;
        &::before {
          font-family: oaicon;
          content: '\e8c3';
          color: $success-color;
          width: 16px;
          height: 16px;
          position: absolute;
          left: -16px;
        }
      }
      .not-leaf-node {
        > .button.attendance-icon_ico_docu,
        > .button.attendance-icon_ico_open,
        > .button.attendance-icon_ico_close {
          &::before {
            position: static;
          }
        }
      }
    }
  }
  .right-table-content {
    border-radius: 8px;
    background: #fff;
    height: 100%;
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }
  .form-table {
    overflow: hidden;
  }
  .action-span {
    color: $primary-blue;
    cursor: pointer;
    user-select: none;

    & + .action-span {
      margin-left: $primary-spacing;
    }

    &:hover {
      opacity: 0.8;
    }

    &:focus,
    &:active {
      color: $primary-blue-active;
    }
  }
}
</style>
