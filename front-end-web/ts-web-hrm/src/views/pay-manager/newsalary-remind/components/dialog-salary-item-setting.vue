<template>
  <div class="content">
    <div class="title">
      薪酬薪资设置
    </div>
    <ts-form
      ref="ruleForm"
      :model="form"
      labelWidth="130px"
      style="height: 100%;"
    >
      <div class="table">
        <el-table
          ref="table"
          class="form-table"
          :data="itemData"
          style="width: 100%"
          height="100%"
          border
          stripe
          :show-overflow-tooltip="true"
        >
          <el-table-column
            label="序号"
            width="60"
            align="center"
            type="index"
            prop="index"
          ></el-table-column>
          <el-table-column label="薪酬项目" align="center" prop="itemId">
            <template slot-scope="scope">
              <base-select
                v-model="scope.row.itemId"
                :inputText.sync="scope.row.itemName"
                :loadMethod="handleGetOverallSituationSalaryItem"
                label="itemName"
                searchInputName="itemName"
                value="id"
                clearable
                class="salary-item-select"
              ></base-select>
            </template>
          </el-table-column>
          <el-table-column label="是否补扣" align="center" prop="isBackPayment">
            <template slot-scope="scope">
              <vxe-switch
                v-model="scope.row.isBackPayment"
                close-value="0"
                open-value="1"
                open-label="是"
                close-label="否"
              ></vxe-switch>
            </template>
          </el-table-column>
          <el-table-column
            label="补缴开始月份"
            align="center"
            prop="backPaymentDate"
          >
            <template slot-scope="scope">
              <!-- <ts-month-picker
                style="padding-right: 8px;"
                v-model="scope.row.backPaymentDate"
                valueFormat="YYYY-MM"
                placeholder="请选择"
              /> -->
              <el-date-picker
                type="month"
                style="width:90%;padding-right: 8px;"
                v-model="scope.row.backPaymentDate"
                format="yyyy-MM"
                value-format="yyyy-MM"
                placeholder="请选择"
              >
              </el-date-picker>
              <!-- <ts-select
                style="width: 100%;padding-right: 8px;"
                v-model="scope.row.backPaymentDate"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="idx in 12"
                  :key="idx"
                  :label="idx + '月'"
                  :value="idx"
                ></ts-option>
              </ts-select> -->
            </template>
          </el-table-column>
          <el-table-column label="是否启用" align="center" prop="isEnabled">
            <template slot-scope="scope">
              <vxe-switch
                v-model="scope.row.isEnabled"
                close-value="0"
                open-value="1"
                open-label="启用"
                close-label="关闭"
              ></vxe-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <span class="operation-span" @click="delRow(scope.$index)">
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="padding-top: 10px;">
        <ts-button type="primary" @click="addRow">新增行</ts-button>
      </div>
      <hr
        style="padding-top: 5px;border: 0; border-top: 1px dashed blue; margin: 20px 0;"
      />
      <ts-form-item
        label="通知方式"
        prop="noticeTypeArr"
        :rules="rules.required"
      >
        <ts-checkbox-group
          v-model="form.noticeTypeArr"
          style="margin-top: 10px;"
        >
          <ts-checkbox label="1">站内通知</ts-checkbox>
          <!-- <ts-checkbox label="2">微信通知</ts-checkbox>
          <ts-checkbox label="3">短信通知</ts-checkbox> -->
        </ts-checkbox-group>
      </ts-form-item>

      <ts-form-item
        label="通知对象"
        prop="noticeUserName"
        :rules="rules.required"
        style="position: relative;"
      >
        <ts-input
          v-model="form.noticeUserName"
          rows="4"
          resize="none"
          type="textarea"
          disabled
          class="user-select-input"
          style="width: 50%;"
        ></ts-input>
        <img
          class="person-icon"
          src="@/assets/img/defUserPhoto.png"
          @click="handleOpenUserSelect('noticeUser')"
        />
      </ts-form-item>
    </ts-form>

    <div class="footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="reset">重 置</ts-button>
    </div>

    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk" />
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  // components: {},
  // mixins: [postSettingColumns],
  data() {
    return {
      form: {
        noticeTypeArr: []
      },
      index: 0,
      itemData: [],
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async handleGetOverallSituationSalaryItem(data) {
      let res = await this.ajax.salaryItemLibraryGetEnableDataList({
        ...data,
        pageSize: 15
      });

      if (res.success == false) {
        this.$message.error(res.message || '获取薪酬项目失败!');
        return false;
      }
      return res.rows;
    },
    //选择通知人员
    handleOpenUserSelect(key, isRadio = false) {
      let names = this.form[key + 'Name']?.split(',') || [],
        ids = this.form[key]?.split(',') || [],
        empList = [];
      names.map((name, index) => {
        empList.push({
          empCode: ids[index],
          empName: name
        });
      });
      let emp = [key + 'Name', key];
      this.$refs.TsHomsSelectPerson.open('noticeUser', {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: false,
        echoData: {
          dept: '',
          group: '',
          noticeUser: ids
        },
        submitKeys: {
          dept: ['', ''],
          group: ['', ''],
          emp
        }
      });
    },
    handleSelectUserOk(result, key) {
      switch (key) {
        case 'noticeUser':
          const { allNames: noticeUserName, noticeUser } = result[key];
          let ids = noticeUser.split(',');
          let names = noticeUserName.split(',');
          let list = ids.map((e, index) => {
            return {
              empName: names[index],
              empCode: e
            };
          });
          this.userList = [];
          list.map(item => {
            let obj = null;
            obj = {
              noticeUserName: item.empName,
              noticeUser: item.empCode
            };
            this.userList.push(obj);
          });
          this.$set(
            this.form,
            key,
            this.userList
              .map(e => {
                return e.noticeUser;
              })
              .join(',')
          );
          this.$set(
            this.form,
            key + 'Name',
            this.userList
              .map(e => {
                return e.noticeUserName;
              })
              .join(',')
          );
          break;
      }
    },
    //查询薪酬薪资列表数据
    async handleRefreshTable() {
      let res = await this.ajax.getNewsalarySettingByType({ remindType: 2 });
      if (res.success == false) {
        this.$message.error(res.message || '方案提醒设置数据获取失败');
        return;
      }
      let data = res.object || {};
      if (data.noticeType) {
        data.noticeTypeArr = data.noticeType.split(',');
      }
      if (!data.noticeTypeArr) {
        data.noticeTypeArr = [];
      }
      if (data.itemRemindSettingList) {
        this.itemData = data.itemRemindSettingList.map((item, i) => {
          let index = i + 1;
          this.index = index;
          // if (item.backPaymentDate) {
          //   item.backPaymentDate = Number(item.backPaymentDate);
          // }
          return {
            index,
            ...item
          };
        });
      }
      this.form = data;
    },
    //添加行
    addRow() {
      this.itemData.push({ index: ++this.index });
    },
    //删除行
    delRow(index) {
      this.itemData.splice(index, 1);
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        if (this.form.noticeUser && this.form.noticeUser.length > 500) {
          this.$message.error(
            '通知对象不能找过500个字符,当前为' +
              this.form.noticeUser.length +
              '个字符!'
          );
          return;
        }
        this.form.remindType = 2;
        if (this.form.noticeTypeArr) {
          this.form.noticeType = this.form.noticeTypeArr.join(',');
        }
        this.form.itemRemindSettingList = this.itemData;
        const data = deepClone(this.form);
        let Api = this.ajax.handleAddRemindSettingData;
        if (this.form.id) {
          Api = this.ajax.handleEditRemindSettingData;
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
          // this.$emit('refresh');
          // this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    reset() {
      this.form = {};
      this.handleRefreshTable();
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  // display: flex;
  // padding: 10px;
  background: #fff;
  height: 50%;
  .table {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .form-table {
      flex: 1;
      transform: scale(1);
      /deep/ .el-table__header-wrapper tr th {
        background: #d2def0;
        min-width: 0px;
        box-sizing: border-box;
        text-overflow: ellipsis;
        vertical-align: middle;
        position: relative;
        line-height: 30px;
        padding: 0;
      }
      /deep/ .el-table__row td {
        padding: 0;
        line-height: 30px;
        height: 30px;
      }
      .details-span {
        color: $primary-blue;
        cursor: pointer;
      }
    }
  }
}
.title {
  height: 30px;
  background-color: rgb(210, 222, 240);
  align-items: center;
  padding-top: 6px;
  padding-left: 8px;
  margin: -8px;
  margin-bottom: 8px;
}
.person-icon {
  position: absolute;
  right: 51%;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 999;
}
.operation-span {
  color: red;
  cursor: pointer;
}
.footer {
  padding: 20px;
  position: absolute;
  bottom: 20px;
  right: 20px;
}
// 设置滚动条的宽度
/deep/ ul::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
// 设置滚动条的背景色和圆角
/deep/ ul::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: rgba(153, 153, 153, 0.4);
  &:hover {
    background: rgba(153, 153, 153, 0.8);
  }
}
/deep/ ul::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  background: #fff;
}
// 设置滚动条的宽度
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 设置滚动条的背景色和圆角
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: rgba(153, 153, 153, 0.4);
  &:hover {
    background: rgba(153, 153, 153, 0.8);
  }
}
/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  background: #fff;
}
</style>
