<template>
  <div class="summary-and-report-box">
    <div class="container">
      <div class="left-tree-content">
        <div class="search-container">
          <div class="left-title">
            提醒设置
          </div>
        </div>
        <el-scrollbar style="flex-grow: 1; width: 100%;height: 100%;">
          <el-tree
            ref="remindTree"
            :data="remindTreeList.data"
            node-key="id"
            :default-expand-all="true"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="
              (data, checked, indeterminate) => {
                nodeClick(data, checked, indeterminate, 'remindTree');
              }
            "
            class="remind-tree"
          >
          </el-tree>
        </el-scrollbar>
      </div>
      <div class="right-table-content">
        <dialog-options-setting
          ref="dialogOptionsSetting"
          v-if="this.activeType == 1"
        />
        <dialog-salary-item-setting
          ref="dialogSalaryItemSetting"
          v-if="this.activeType == 2"
        />
        <dialog-salary-level-setting
          ref="dialogSalaryLevelSetting"
          v-if="this.activeType == 3"
        />
      </div>
    </div>
  </div>
</template>

<script>
import dialogOptionsSetting from './dialog-options-setting.vue';
import dialogSalaryItemSetting from './dialog-salary-item-setting.vue';
import dialogSalaryLevelSetting from './dialog-salary-level-setting.vue';

const folderIcon = require('@/assets/img/icon_folder.png');
const fileIcon = require('@/assets/img/icon_file.svg');

export default {
  components: {
    dialogOptionsSetting,
    dialogSalaryItemSetting,
    dialogSalaryLevelSetting
  },
  // mixins: [],
  data() {
    return {
      folderIcon,
      fileIcon,
      salaryCategoryList: [],
      selectList: [],
      searchForm: {},
      remindTreeList: {
        data: [
          { id: '1', parentId: '-1', name: '薪酬方案提醒' },
          { id: '2', parentId: '-1', name: '薪酬项目提醒' },
          { id: '3', parentId: '-1', name: '薪级定期调整' }
        ],
        defaultExpandedKeys: []
      },
      activeType: 1
    };
  },
  methods: {
    async handleRefreshTable() {
      let dic = {
        1: 'dialogOptionsSetting',
        2: 'dialogSalaryItemSetting',
        3: 'dialogSalaryLevelSetting'
      };

      this.$nextTick(() => {
        this.$refs[dic[this.activeType]].handleRefreshTable();
      });
    },

    // 点击提醒类型事件
    nodeClick(data, checked, indeterminate, refName) {
      if (this.searchForm.remindType !== data.id) {
        this.activeType = data.id;
        this.searchForm.remindType = data.id;
        this.handleRefreshTable();
      }
    },
    renderContent(h, { node, data, store }) {
      return (
        <div class="custom-tree-node">
          <img
            class="icon-file"
            src={data.children ? this.folderIcon : this.fileIcon}
          />
          <span>{data.name}</span>
        </div>
      );
    }
  }
};
</script>

<style lang="scss" scoped>
$main-bg: #5260ff;
$main-bg-8: #5260ff14;
.container {
  display: flex;
  width: 100%;
  justify-content: space-between;

  & > div {
    height: 100%;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
    border: 1px solid #e4e4e4;
  }

  .left-title {
    height: 30px;
    background-color: rgb(210, 222, 240);
    align-items: center;
    padding-top: 6px;
    padding-left: 8px;
    margin: -8px;
    margin-bottom: 8px;
  }

  .tree-container,
  .table-container,
  .sel-container {
    box-sizing: border-box;
    webkit-box-sizing: border-box;
    moz-box-sizing: border-box;
  }

  .tree-container {
    width: 200px;
    display: flex;
    flex-direction: column;

    .search-container {
      padding: 8px;
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;

    /deep/ .el-loading-spinner {
      margin-top: 0;
    }

    .search-container {
      padding: 8px;
      display: flex;
      .right-picker {
        margin-left: 8px;
      }
    }

    .el-pagination {
      display: flex;
      justify-content: flex-end;
    }

    .text-style {
      font-size: 12px;
      color: #333333;
    }
  }

  .sel-container {
    width: 252px;
    display: flex;
    flex-direction: column;

    .staff-container {
      width: 100%;
      height: 290px;
    }

    .dept-container {
      height: calc(100% - 290px);
    }

    .title {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
      background: #fafafa;
      font-size: 12px;
      font-weight: bold;
      color: #333333;

      & > a {
        cursor: pointer;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        font-weight: 400;
        color: #333333;
      }
    }

    .content {
      height: calc(100% - 30px);
      width: 100%;
      flex-grow: 1;
      padding: 8px 7px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(51, 51, 51, 0.7);
      line-height: 17px;
      overflow-y: auto;
      box-sizing: border-box;
      webkit-box-sizing: border-box;
      moz-box-sizing: border-box;
    }
  }

  /deep/ ::-webkit-scrollbar {
    width: 6px;
    height: 8px;
  }

  /deep/ ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background: rgba(153, 153, 153, 0.8);
    }
  }

  /deep/ ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
}

.summary-and-report-box {
  height: calc(100% - 8px);
  margin-bottom: 8px;
  display: flex;

  .left-tree-content {
    width: 220px;
    border-radius: 8px;
    background: #fff;
    height: 100%;
    margin-right: 8px;
    padding: 8px;
    /deep/ .attendance-tree {
      .button.attendance-icon_ico_docu,
      .button.attendance-icon_ico_open,
      .button.attendance-icon_ico_close {
        position: relative;
        width: 0;
        &::before {
          font-family: oaicon;
          content: '\e8c3';
          color: $success-color;
          width: 16px;
          height: 16px;
          position: absolute;
          left: -16px;
        }
      }
      .not-leaf-node {
        > .button.attendance-icon_ico_docu,
        > .button.attendance-icon_ico_open,
        > .button.attendance-icon_ico_close {
          &::before {
            position: static;
          }
        }
      }
    }
  }
  .right-table-content {
    border-radius: 8px;
    background: #fff;
    height: 100%;
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }
  .form-table {
    overflow: hidden;
    height: 90%;
  }
  .action-span {
    color: $primary-blue;
    cursor: pointer;
    user-select: none;

    & + .action-span {
      margin-left: $primary-spacing;
    }

    &:hover {
      opacity: 0.8;
    }

    &:focus,
    &:active {
      color: $primary-blue-active;
    }
  }
}
/deep/ {
  .remind-tree {
    .el-tree-node__content:hover .custom-tree-node span {
      color: $main-bg;
    }

    .icon-file {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    .is-current {
      & > .el-tree-node__content {
        background-color: $main-bg-8 !important;
      }
    }
  }
}
</style>
