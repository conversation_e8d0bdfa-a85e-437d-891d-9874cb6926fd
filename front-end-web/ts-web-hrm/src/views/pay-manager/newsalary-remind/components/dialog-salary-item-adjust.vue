<template>
  <el-drawer
    custom-class="dialog-salary-item-adjust"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancelModel"
    size="35%"
  >
    <template slot="title">
      <span class="dialog-title">
        {{ '薪资变动提醒-处理' }}
      </span>
    </template>
    <div class="content">
      <div class="detail">
        <ts-form ref="form" :model="editData">
          <div
            style="width: 100%;background-color:#dcdcdc;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">提醒信息</span>
          </div>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="算薪周期"
                prop="computeDate"
                :rules="requiredRow"
              >
                <ts-month-picker
                  style="width: 100%"
                  v-model="editData.computeDate"
                  valueFormat="YYYY-MM"
                  placeholder="算薪周期"
                  disabled
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="提醒类型" prop="remindType">
                <ts-input
                  maxlength="50"
                  v-model="editData.remindType"
                  placeholder="提醒类型"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="薪酬项目" prop="itemName">
                <ts-input
                  maxlength="50"
                  v-model="editData.itemName"
                  placeholder="薪酬项目"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="工号" prop="employeeNo">
                <ts-input
                  maxlength="50"
                  v-model="editData.employeeNo"
                  placeholder="工号"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="姓名" prop="employeeName">
                <ts-input
                  maxlength="50"
                  v-model="editData.employeeName"
                  placeholder="姓名"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="手机号码" prop="phoneNumber">
                <ts-input
                  maxlength="50"
                  v-model="editData.phoneNumber"
                  placeholder="手机号码"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="性别" prop="gender">
                <ts-input
                  maxlength="50"
                  v-model="editData.gender"
                  placeholder="性别"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="所属组织" prop="orgName">
                <ts-input
                  maxlength="50"
                  v-model="editData.orgName"
                  placeholder="所属组织"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="调整前" prop="beforeSalary">
                <ts-input
                  maxlength="50"
                  v-model="editData.beforeSalary"
                  placeholder="调整前"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="调整后" prop="afterSalary">
                <ts-input
                  maxlength="50"
                  v-model="editData.afterSalary"
                  placeholder="调整后"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="差额" prop="differenceAmount">
                <ts-input
                  maxlength="50"
                  v-model="editData.differenceAmount"
                  placeholder="差额"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="补缴月份" prop="backPaymentMonth">
                <ts-input
                  maxlength="50"
                  v-model="editData.backPaymentMonth"
                  placeholder="补缴月份"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
          </ts-row>
          <div
            style="width: 100%;background-color:#dcdcdc;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">薪酬调整-补扣信息</span>
          </div>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item label="金额合计" prop="totalAmount">
                <ts-input
                  maxlength="50"
                  v-model="editData.totalAmount"
                  placeholder="金额合计"
                  disabled
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="项目类型"
                prop="tmpItem"
                :rules="requiredRow"
              >
                <ts-select
                  style="width: 100%"
                  v-model="editData.tmpItem"
                  clearable
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <ts-option
                    v-for="item of adjusItemsData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-form-item label="备注" prop="remark" :rules="requiredRow">
              <ts-input
                v-model="editData.remark"
                type="textarea"
                class="textarea"
                placeholder="请输入备注信息"
                show-word-limit
                :disabled="isDisabled"
              ></ts-input>
            </ts-form-item>
          </ts-row>
        </ts-form>
        <template>
          <div style="position: fixed;bottom: 20px;right: 20px;z-index: 99999;">
            <ts-button type="primary" @click="handleConfirm" v-if="!isDisabled"
              >确定</ts-button
            >
            <ts-button @click="handleCancelModel">取消</ts-button>
          </div>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      editType: 'add',
      visible: false,
      loading: false,
      isDisabled: false,

      adjusItemsData: [],
      editData: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  methods: {
    open({ editType = 'add', editData = {} }) {
      this.isDisabled = false;
      if (editType == 'add') {
        this.title = '新增';
      } else {
        this.title = '查看';
        this.isDisabled = true;
      }
      this.editType = editType;
      editData.totalAmount = editData.differenceAmount;
      if (editData.backPaymentMonth) {
        editData.totalAmount = (
          editData.differenceAmount * editData.backPaymentMonth
        ).toFixed(2);
      }
      this.editData = editData;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.getAdjusItemsData();
      });
    },
    /**@desc 获取项目类型 */
    getAdjusItemsData() {
      this.ajax.getDataByDataLibrary('tmp_adjust_item').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '项目类型获取失败');
          return;
        }
        this.adjusItemsData = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    handleCancelModel() {
      this.visible = false;
      this.keyList = [];
    },
    async handleConfirm() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate || this.loading) {
        return;
      }
      this.loading = true;
      let paramsData = deepClone(this.editData);

      this.ajax.handleNewsalaryItemRemindProcessing(paramsData).then(res => {
        this.loading = false;
        if (!res.success) {
          this.$message.error(res.message || '处理失败');
          return;
        }
        //更新薪资异动状态

        this.$message.success('操作成功');
        this.$emit('success');
        this.handleCancelModel();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.month-picker {
  width: 100%;
}
::v-deep {
  .dialog-salary-item-adjust {
    margin-top: 74px;
    height: 100%;
    overflow: hidden;
    .el-drawer__header {
      .dialog-title {
        // color: #4148ff;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
      }
      .el-drawer__close-btn {
        position: relative;
        top: -9px;
      }
    }
    .el-drawer__body {
      padding: 0 10px;
      overflow: hidden;
      .content {
        .detail {
          .el-form .el-form-item {
            margin: 0;
          }
        }
        .ts-button {
          &.is-disabled {
            color: rgb(204, 204, 204) !important;
            border-color: rgb(231, 235, 240) !important;
            background-color: rgb(250, 250, 250) !important;
            &:hover {
              cursor: not-allowed;
            }
          }
        }
        .fontWeight {
          font-size: 16px;
          font-weight: 600;
        }
        .top {
          width: 4px;
          background-color: #4148ff;
          height: 21px;
          top: -3px;
        }
        .form_item {
          line-height: 36px;
        }
        .record-list {
          .item-list {
            max-height: calc(100vh - 350px);
            overflow-y: auto;
          }
          // 设置滚动条的宽度
          .item-list::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          // 设置滚动条的背景色和圆角
          u.item-listl::-webkit-scrollbar-thumb {
            border-radius: 8px;
            background: rgba(153, 153, 153, 0.4);
            &:hover {
              background: rgba(153, 153, 153, 0.8);
            }
          }
          u.item-listl::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            background: #fff;
          }
          .record-item {
            margin-top: 8px;
            padding: 0 8px;
            .item-title {
              display: flex;
              justify-content: space-between;
              font-weight: 600;
              margin-bottom: 8px;
            }
            .form-table {
              flex: 1;
              transform: scale(1);
              height: auto;
              .el-table__header-wrapper tr th {
                background: #d2def0;
                min-width: 0px;
                box-sizing: border-box;
                text-overflow: ellipsis;
                vertical-align: middle;
                position: relative;
                line-height: 30px;
                padding: 0;
              }
              .el-table__row td {
                padding: 0;
                line-height: 30px;
                height: 30px;
              }
              .details-span {
                color: $primary-blue;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
