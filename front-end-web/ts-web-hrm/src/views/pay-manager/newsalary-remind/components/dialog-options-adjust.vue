<template>
  <ts-dialog
    custom-class="dialog-adjust-profile"
    title="调整薪酬方案"
    width="650px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item label="调整前薪酬方案" prop="beforeOptionId">
          <ts-select
            style="width: 100%;"
            v-model="form.beforeOptionId"
            clearable
            placeholder="请选择"
            :disabled="true"
          >
            <ts-option
              v-for="item of salaryProfileOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </ts-form-item>
        <ts-form-item label="调整后薪酬方案" prop="afterOptionId">
          <ts-select
            style="width: 100%;"
            v-model="form.afterOptionId"
            clearable
            placeholder="请选择"
            :rules="rules.required"
            :disabled="isdisabled"
          >
            <ts-option
              v-for="item of salaryProfileOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></ts-option>
          </ts-select>
        </ts-form-item>

        <ts-form-item
          label="生效时间"
          prop="effectiveDate"
          :rules="rules.required"
        >
          <ts-date-picker
            style="width: 100%"
            v-model="form.effectiveDate"
            valueFormat="YYYY-MM-DD"
            placeholder="请选择"
            :disabled="isdisabled"
          />
        </ts-form-item>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.handleDesc"
            placeholder="请输入"
            type="textarea"
            class="textarea"
            maxlength="500"
            show-word-limit
            :disabled="isdisabled"
          />
        </ts-form-item>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button
        type="primary"
        :loading="submitLoading"
        @click="submit"
        v-if="!isdisabled"
      >
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  props: {},
  data() {
    return {
      visible: false,
      submitLoading: false,
      salaryProfileOptions: [],
      isdisabled: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open({ data, isdisabled }) {
      this.isdisabled = isdisabled;
      let echoData = deepClone(data);
      this.$set(this, 'form', echoData);
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.handleGetNewSalaryOptionAllList();
      });
      this.visible = true;
    },
    // 获取薪酬方案
    async handleGetNewSalaryOptionAllList() {
      let res = await this.ajax.newSalaryOptionAllList();
      if (res.success == false) {
        this.$message.error(res.message || '薪酬组数据获取失败');
        return;
      }
      this.salaryProfileOptions = (res.object || []).map(m => {
        return {
          label: m.optionName,
          value: m.id,
          element: 'ts-option'
        };
      });
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let data = deepClone(this.form);

        this.submitLoading = true;
        const res = await this.ajax.handleBatchAdjustOptions(data);
        this.submitLoading = false;

        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return false;
        }
        this.$message.success('操作成功!');
        this.$emit('refresh');
        this.close();
      } catch (error) {
        console.log(error, 'error');
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.$set(this, 'form', {});
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-adjust-profile {
    .el-dialog__body {
      height: 280px !important;
      .content {
        height: 100% !important;

        .textarea {
          .el-textarea__inner {
            min-height: 110px !important;
            max-height: 200px !important;
          }
        }
      }
    }
  }
}
</style>
