export default {
  data() {
    return {
      searchForm: { date: [], employeeName: '', approvalStatus: '' },
      resetData: {
        date: [],
        employeeName: '',
        approvalStatus: ''
      },
      actions: [],
      searchList: [
        {
          label: '生效日期',
          value: 'date'
        },
        {
          label: '姓名',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入'
          }
        },
        {
          label: '状态',
          value: 'approvalStatus',
          element: 'ts-select',
          childNodeList: [
            {
              element: 'ts-option',
              label: '全部',
              value: ''
            },
            {
              element: 'ts-option',
              label: '未审批',
              value: '1'
            },
            {
              element: 'ts-option',
              label: '已退回',
              value: '3'
            },
            {
              element: 'ts-option',
              label: '已审批',
              value: '4'
            }
          ]
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 40
        },
        {
          label: '生效日期',
          prop: 'effectiveDate',
          align: 'center',
          width: 95
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          width: 100
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center',
          minWidth: 120,
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetail(row) }
              },
              row.employeeName
            );
          }
        },
        {
          label: '部门',
          prop: 'employeeOrgName',
          align: 'center',
          width: 100
        },
        {
          label: '任职时间',
          prop: 'jobDeionTypeTime',
          align: 'center',
          width: 80
        },
        {
          label: '晋升前岗位类别',
          prop: 'oldPlgwText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升后岗位类别',
          prop: 'newPlgwText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升前岗位等级',
          prop: 'oldGwdjText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升后岗位等级',
          prop: 'newGwdjText',
          align: 'center',
          width: 120
        },
        {
          label: '来源',
          prop: 'sourceTypeText',
          align: 'center',
          width: 80
        },
        {
          label: '晋升前薪级类别',
          prop: 'oldSalaryLevelTypeText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升后薪级类别',
          prop: 'newSalaryLevelTypeText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升前薪级等级',
          prop: 'oldSalaryLevelIdText',
          align: 'center',
          width: 120
        },
        {
          label: '晋升后薪级等级',
          prop: 'newSalaryLevelIdText',
          align: 'center',
          width: 120
        },
        {
          label: '创建时间',
          prop: 'createDate',
          align: 'center',
          width: 120
        },
        {
          label: '操作人',
          prop: 'updateUserName',
          align: 'center',
          width: 100
        },
        {
          label: '状态',
          prop: 'approvalStatusText',
          align: 'center',
          fixed: 'right',
          width: 100
        },
        {
          label: '操作',
          prop: 'actions',
          width: 120,
          fixed: 'right',
          align: 'center',
          headerSlots: 'action',
          render: (h, { row }) => {
            let actionList = [];
            if (row.approvalStatus != 4) {
              actionList = [
                {
                  label: '编辑',
                  event: this.handleEdit
                },
                {
                  label: '删除',
                  className: 'actionDel',
                  event: this.handleDelete
                }
              ];
            } else {
              actionList.push({
                label: '查看',
                event: this.handleDetail
              });
            }

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  }
};
