<template>
  <div class="componet-select-dept flex-column">
    <div class="input-container flex-space">
      <ts-input
        placeholder="输入关键字进行过滤"
        @input="searchInput"
        class="search-input"
        v-model="searchVal"
      >
        <template slot="append">
          <i
            @click="handleClickInputCircle"
            class="el-icon-circle-close input-circle"
          />
        </template>
      </ts-input>
    </div>
    <el-scrollbar
      class="flex-column tree-scrollbar"
      style="flex: 1;"
      wrap-style="overflow-x: hidden;"
    >
      <ts-ztree
        ref="tree"
        :data="treeData"
        emptyText="暂无数据"
        showCheckbox
        :chkboxType="{
          Y: 's',
          N: 's'
        }"
        :defaultExpandAll="defaultExpandAll"
        :fontCss="fontCss"
        @before-click="handleRightBeforeClick"
        @before-check="handleBeforeCheck"
        @onCreated="handleOnCreated"
      >
      </ts-ztree>
    </el-scrollbar>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  model: {
    prop: 'selectOrgIds',
    event: 'change'
  },
  props: {
    selectOrgIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchVal: '',
      searchInput: null,
      defaultExpandAll: false,
      fontCss: function(treeId, treeNode) {
        return treeNode.highlight
          ? {
              color: '#fff',
              fontWeight: 'bold',
              background: 'rgba(82, 96, 255, 0.35)'
            }
          : {
              color: 'inherit',
              fontWeight: 'normal',
              background: 'inherit'
            };
      },

      treeData: [],
      treeObj: null
    };
  },
  computed: {},
  methods: {
    async init(treeData) {
      this.searchVal = '';
      this.lastIds = undefined;
      this.searchInput = this.debounce(this.input, 300);
      this.treeData = deepClone(treeData);
    },

    handleOnCreated(treeObj) {
      this.treeObj = treeObj;
    },

    handleRightBeforeClick(treeNode, treeObj, fn) {
      let { checked } = treeNode.getCheckStatus();
      this.treeObj.checkNode(treeNode, !checked, true);
      this.$nextTick(() => {
        this.handleGetSelectValue();
      });
      fn(false);
    },

    handleBeforeCheck(treeNode, treeObj, fn) {
      this.$nextTick(() => {
        this.handleGetSelectValue();
      });
    },

    handleClickInputCircle() {
      this.searchVal = '';
      this.input();
    },

    handleOpenFirstTreeChildren() {
      this.treeObj.expandAll(false); // 收起所有节点
      const nodes = this.treeObj.getNodes();
      if (nodes.length > 0) {
        this.treeObj.expandNode(nodes[0], true, false, false);
      }
    },

    handleGetSelectValue() {
      let checkeds = this.treeObj.getCheckedNodes();
      let val = checkeds.map(m => m.id).join(',');
      this.$emit('change', val);
    },

    input() {
      const searchKey = this.searchVal.trim();

      let arrNodes = this.treeObj.transformToArray(this.treeObj.getNodes());
      arrNodes.forEach(node => {
        node.highlight = false;
        this.treeObj.updateNode(node);
      });

      if (!searchKey) {
        this.handleOpenFirstTreeChildren();
        return;
      }
      // 搜索并高亮匹配的节点
      const nodes = this.treeObj.getNodesByParamFuzzy('name', searchKey);
      if (nodes.length > 0) {
        nodes.forEach(node => {
          node.highlight = true;
          this.treeObj.expandNode(node.getParentNode(), true, false, false); // 展开父节点
          this.treeObj.expandNode(node, true, false, false); // 展开匹配节点
          this.treeObj.updateNode(node);
        });
      } else {
        this.handleOpenFirstTreeChildren();
      }
    },

    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.componet-select-dept {
  height: 100%;

  .search-input {
    width: 145px;
    min-width: 145px;
    input {
      width: 145px;
      min-width: 145px;
    }
  }
  ::v-deep {
    .ts-ztree {
      .ztree {
        .node_name {
          color: inherit;
          background-color: inherit;
        }
      }
    }
    .input-container {
      margin-bottom: 8px;
      .el-input-group__append {
        padding: 0px !important;

        .input-circle {
          cursor: pointer;
          padding: 0px 8px !important;
        }
      }
    }
  }
}
</style>
