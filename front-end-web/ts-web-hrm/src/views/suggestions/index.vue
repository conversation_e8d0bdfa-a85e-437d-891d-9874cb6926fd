<template>
  <div class="recruitment-plan-box">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{
        planStatus: '0'
      }"
      v-if="nkyyShow"
    >
      <template v-slot:signUpMonth>
        <ts-date-picker
          v-model="searchForm.signUpMonth"
          mode="month"
          format="YYYY-MM"
          :open="searchMonthIsOpen"
          @openChange="val => (searchMonthIsOpen = val)"
          @panelChange="handlePanelChange"
        ></ts-date-picker>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :columns="columns"
      @refresh="handleRefreshTable"
      v-if="nkyyShow"
    />

    <dialog-edit-form
      v-model="dialogEditForm"
      :eachData="eachData"
      @refresh="handleRefreshTable"
    />
    <div v-if="!nkyyShow" class="checkpass">
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="60px">
          <ts-form-item label="密码1" prop="pwdOne" :rules="rules.required">
            <ts-input v-model="form.pwdOne" type="password" />
          </ts-form-item>
          <ts-form-item prop="pwdTwo" :rules="rules.required" label="密码2">
            <ts-input v-model="form.pwdTwo" type="password" />
          </ts-form-item>
        </ts-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" :loading="submitLoading" @click="submit"
          >提 交</ts-button
        >
      </span>
    </div>
    <dialog-details v-model="dialogDetails" :eachData="eachData" />
  </div>
</template>

<script>
import table from './mixins/table';
import { deepClone } from '@/unit/commonHandle.js';
import DialogEditForm from './components/dialog-edit-form.vue';
import DialogDetails from './components/dialog-details.vue';
export default {
  mixins: [table],
  components: {
    DialogEditForm,
    DialogDetails
  },
  data() {
    return {
      eachData: {},
      dialogEditForm: false,
      dialogDetails: false,
      nkyyShow: false,
      orgCode: '',
      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  async created() {
    let res = await this.ajax.getGlobalSetting();
    this.orgCode = res.object.orgCode || '';
    if (this.orgCode != 'hnnkyy') {
      this.nkyyShow = true;
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    }
  },
  methods: {
    checkPass(boolean) {
      this.nkyyShow = boolean;
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    handleDetails(row) {
      this.eachData = row;
      this.dialogDetails = true;
    },
    // 编辑
    handleEdit(e) {
      this.eachData = e;
      this.dialogEditForm = true;
    },
    // 删除
    async handleDel(row) {
      try {
        await this.$confirm('确定删除该数据吗？', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.suggestionBoxDel(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message);
        }
      } catch (e) {
        console.error(e);
      }
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        data.pwdId = 1;
        this.submitLoading = true;
        const res = await this.ajax.verificationPwd(data);

        if (res.success) {
          this.submitLoading = false;
          this.$message.success('认证成功!');
          this.checkPass(true);
        } else {
          this.submitLoading = false;
          this.$message.error(res.message || '认证成功!');
          this.form = {};
          await this.$refs.form.clearValidate();
        }
      } catch (error) {
        console.error(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.recruitment-plan-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
.checkpass {
  position: absolute;
  top: 40%;
  right: 40%;
  width: 300px;
  border: 1px solid #aaa;
  padding: 10px;
  border-radius: 5px;
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
}
</style>
