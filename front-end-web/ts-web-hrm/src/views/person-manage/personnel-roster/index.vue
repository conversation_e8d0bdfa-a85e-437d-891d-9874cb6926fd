<template>
  <div class="personnel-roster">
    <div class="filter-box">
      <div class="pubBtnBox">
        <div
          class="btnItem"
          :class="activeTab == item.employeeStatus ? 'active' : ''"
          v-for="(item, index) in topList"
          :key="index"
          @click="topSearch(item)"
        >
          {{ item.employeeType }}({{ item.employeeCount }})
        </div>
      </div>
      <div class="filterBox">
        <span style="line-height: 30px;">当前筛选器&nbsp;</span>
        <base-select
          v-model="filterId"
          :inputText.sync="filterIdName"
          :loadMethod="handleGetFilterList"
          label="filterName"
          value="id"
          searchInputName="filterName"
          :clearable="false"
          placeholder="请选择"
          style="width: 140px;margin-right: 10px;"
          @select="handleOk"
        />
        <ts-button type="primary" @click="handleAddFilter">保存</ts-button>
        <ts-button @click="handleDelFilter" class="shallowButton"
          >删除</ts-button
        >
      </div>
    </div>
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="reset"
    >
      <template #right>
        <ts-button @click="handleExport" class="shallowButton">导出</ts-button>
        <ts-button @click="handleExportAvatar" class="shallowButton"
          >导出照片</ts-button
        >
      </template>
      <template slot="birthday">
        <div style="display: flex; line-height: 30px;">
          <el-date-picker
            style="flex: 1;"
            class="date-picker"
            v-model="searchForm.birthdayStartTime"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="开始日期"
          >
          </el-date-picker>
          &nbsp;~&nbsp;
          <el-date-picker
            style="flex: 1;"
            class="date-picker"
            v-model="searchForm.birthdayEndTime"
            value-format="yyyy-MM-dd"
            placeholder="结束日期"
            :picker-options="pickerOptions1"
          >
          </el-date-picker>
        </div>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_personnel_roster"
      class="form-table"
      ref="table"
      :columns="columns"
      :defaultSort="{
        sidx: 't1.create_date',
        sord: 'desc'
      }"
      @selection-change="handleSelectChange"
      @refresh="handleRefreshTable"
    />
    <dialog-add-filter ref="dialogAddFilter" @refresh="addFilterBack" />
    <dialog-my-archives ref="DialogMyArchives" @refresh="handleRefreshTable" />
  </div>
</template>
<script>
import table from './minixs/table';
import moment from 'moment';
import dialogAddFilter from './components/dialog-add-filter.vue';
import DialogMyArchives from '@/views/archives-manage/archive-manage/components/dialog-my-archives.vue';
export default {
  components: { dialogAddFilter, DialogMyArchives },
  mixins: [table],
  data() {
    return {
      loading: false,
      selectList: [],
      activeTab: null,
      filterId: '',
      filterIdName: '',
      archivesType: null, //档案类型，默认人事档案
      pickerOptions: {
        disabledDate: this.disabledDate
      },
      pickerOptions1: {
        disabledDate: this.disabledDate1
      }
    };
  },
  created() {
    let type = this.$route.params.type;
    if (type) {
      this.archivesType = type;
    } else {
      this.archivesType = null;
    }
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (to.path.indexOf('person-manage/personnel-roster') > -1) {
          let type = this.$route.params.type;
          if (type) {
            this.archivesType = type;
          } else {
            this.archivesType = null;
          }
          this.$nextTick(() => {
            this.refresh();
          });
        }
      }
    }
  },
  methods: {
    async refresh() {
      await this.getEmployeeListStatus();
      if (!this.activeTab) {
        this.activeTab = this.topList[0].employeeStatus;
      }
      this.formatSearchList();
      this.$nextTick(() => {
        this.search();
      });
    },
    async handleGetFilterList(data) {
      let menuId = this.$getCookiesInfo('lastMenuIdqx');
      let res = await this.ajax.personalFilterList({
        menuId,
        ...data
      });
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.object;
    },
    disabledDate(time) {
      if (this.searchForm.birthdayEndTime) {
        if (moment(time).isAfter(moment(this.searchForm.birthdayEndTime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    disabledDate1(time) {
      if (this.searchForm.birthdayStartTime) {
        if (moment(time).isBefore(moment(this.searchForm.birthdayStartTime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    topSearch(item) {
      this.activeTab = item.employeeStatus;
      this.search();
    },
    handleOk(data) {
      let queryData = JSON.parse(data.conditionMap);
      let mulitiList = this.searchList
        .filter(e => e.elementProp && e.elementProp.multiple)
        .map(e => {
          return e.value;
        });
      for (let key in queryData) {
        if (mulitiList.indexOf(key) > -1) {
          queryData[key] = queryData[key].split(',');
        }
      }
      this.searchForm = { ...queryData };
      this.activeTab = this.searchForm.queryStatus;
      this.search();
    },
    addFilterBack(data) {
      this.filterIdName = data.filterIdName;
      this.filterId = data.filterId;
    },
    search() {
      if (this.activeTab == '8' || this.activeTab == '9999') {
        this.columns.forEach(col => {
          if (col.prop == 'retireDateExport') {
            col.visible = true;
          }
        });
      } else {
        this.columns.forEach(col => {
          if (col.prop == 'retireDateExport') {
            col.visible = false;
          }
        });
      }
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    reset() {
      if (!this.activeTab) {
        this.activeTab = this.topList[0].employeeStatus;
      }
      this.filterId = '';
      this.filterIdName = '';
      return {};
    },
    async handleAddFilter() {
      try {
        await this.$confirm(
          `【<span style="color: red">保存</span>】筛选器？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'shallowButton'
          }
        );
        let { searchForm } = this.getQueryParam();
        delete searchForm.pageNo;
        delete searchForm.pageSize;
        let data = {
          personalFilterId: this.filterId || '',
          filterName: this.filterIdName || '',
          menuId: this.$getCookiesInfo('lastMenuIdqx'),
          conditionMap: JSON.stringify(searchForm)
        };
        this.$refs.dialogAddFilter.open({ title: '保存筛选器', data });
      } catch (e) {
        console.error(e);
      }
    },
    async handleDelFilter() {
      if (this.filterId == '') {
        this.$newMessage('warning', '请选择需要【删除】的筛选器!');
        return;
      }
      try {
        await this.$confirm(
          `【<span style="color: red">删除</span>】筛选器？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'shallowButton'
          }
        );
        this.ajax.personalFilterDelete(this.filterId).then(res => {
          if (!res.success) {
            this.$newMessage('error', '【删除】失败!');
            return;
          }
          this.$newMessage('success', '【删除】成功!');
          this.searchForm = {};
          this.filterId = '';
          this.filterIdName = '';
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          status: this.activeTab,
          selectRetire: 1,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      searchForm = this.formatSearchForm(searchForm);
      searchForm.archivesType = this.archivesType;
      searchForm.queryStatus = this.activeTab;
      return { searchForm, pageSize, pageNo };
    },
    handleDetail({ employeeId }) {
      this.$refs.DialogMyArchives.open({
        type: 'details',
        title: '人才画像',
        archivesType: 'all',
        employeeId
      });
    },
    handleExport() {
      let aDom = document.createElement('a');
      let { searchForm } = this.getQueryParam();
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/employee/exportRoster?${conditionList.join('&')}`;
      aDom.click();
    },
    handleExportAvatar() {
      let aDom = document.createElement('a');
      let { searchForm } = this.getQueryParam();
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/employee/exportPhoto?${conditionList.join('&')}`;
      aDom.click();
    },
    async handleRefreshTable() {
      this.loading = true;
      let { searchForm, pageSize, pageNo } = this.getQueryParam();
      this.ajax.getEmployeeRosterList(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$message.error(res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.personnel-roster {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  .filter-box {
    display: flex;
    flex-wrap: wrap;
    height: 30px;
    margin-bottom: 5px;
    .pubBtnBox {
      border: 1px solid #5260ff;
      border-radius: 4px;
      display: flex;
      .btnItem {
        width: auto;
        color: #5260ff;
        line-height: 30px;
        height: 30px;
        padding: 0 2px;
        cursor: pointer;
        &:not(:last-child) {
          border-right: 1px solid #5260ff;
        }
        &.active {
          background: #5260ff;
          color: #fff;
        }
      }
    }
    .filterBox {
      display: flex;
      margin-left: 10px;
    }
  }
  /deep/ .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    .primary-span {
      color: #295cf9;
      cursor: pointer;
      text-align: left;
    }
  }
}
</style>
