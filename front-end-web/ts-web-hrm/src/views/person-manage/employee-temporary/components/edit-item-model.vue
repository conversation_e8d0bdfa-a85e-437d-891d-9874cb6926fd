<template>
  <el-drawer
    custom-class="dialog-setting-detail"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancelModel"
    size="35%"
  >
    <template slot="title">
      <span class="dialog-title">
        {{ title + '临时员工' }}
      </span>
    </template>
    <div class="content">
      <div class="detail">
        <ts-form ref="form" :model="editData">
          <div
            style="width: 100%;background-color:#efefef;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">基本信息</span>
          </div>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="姓名"
                prop="employeeName"
                :rules="requiredRow"
              >
                <ts-input
                  maxlength="50"
                  v-model="editData.employeeName"
                  placeholder="请输入姓名"
                  :disabled="isDisabled"
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="工号" prop="employeeNo" :rules="requiredRow">
                <ts-input
                  v-model="editData.employeeNo"
                  placeholder="请输入工号"
                  maxlength="50"
                  :disabled="editData.id != null"
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="所属组织" prop="orgId">
                <ts-ztree-select
                  ref="treeSelect"
                  defaultExpandAll
                  :inpText.sync="editData.orgName"
                  :inpVal.sync="editData.orgId"
                  :data="treeData"
                  @change="handleChangeTree"
                  :disabled="isDisabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="身份证号"
                prop="identityNumber"
                :rules="requiredRow"
              >
                <ts-input
                  v-model="editData.identityNumber"
                  placeholder="请输入身份证号"
                  maxlength="30"
                  :disabled="isDisabled"
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="性别" prop="gender">
                <ts-select
                  style="width: 100%"
                  v-model="editData.gender"
                  clearable
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <ts-option
                    v-for="item of editData.genderData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="联系电话" prop="phoneNumber">
                <ts-input
                  v-model.number="editData.phoneNumber"
                  placeholder="请输入联系电话"
                  maxlength="11"
                  :disabled="isDisabled"
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="编制类型" prop="tmpEstablishment">
                <ts-select
                  style="width: 100%"
                  v-model="editData.tmpEstablishment"
                  clearable
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <ts-option
                    v-for="item of editData.establishmentTypeData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="学历" prop="tmpEducation">
                <ts-select
                  style="width: 100%"
                  v-model="editData.tmpEducation"
                  clearable
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <ts-option
                    v-for="item of editData.educationTypeData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="岗位名称" prop="tmpPosition">
                <ts-select
                  style="width: 100%"
                  v-model="editData.tmpPosition"
                  clearable
                  placeholder="请选择"
                  :disabled="isDisabled"
                >
                  <ts-option
                    v-for="item of editData.personalIdentityData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></ts-option>
                </ts-select>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="岗位工资"
                prop="tmpPositionSalary"
                :rules="requiredRow"
              >
                <ts-input
                  v-model.number="editData.tmpPositionSalary"
                  placeholder="请输入岗位工资"
                  maxlength="10"
                  :disabled="editData.isSyncSalary == 'Y' || isDisabled"
                ></ts-input>
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item label="银行卡号" prop="bankCardNumber">
                <ts-input
                  v-model.number="editData.bankCardNumber"
                  placeholder="请输入银行卡号"
                  maxlength="20"
                  :disabled="isDisabled"
                ></ts-input>
              </ts-form-item>
            </ts-col>
          </ts-row>
          <div
            style="width: 100%;background-color:#efefef;margin-bottom: 15px;"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">入职信息</span>
          </div>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="入职日期"
                prop="joinDate"
                :rules="requiredRow"
              >
                <ts-date-picker
                  style="width: 100%"
                  v-model="editData.joinDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择入职日期"
                  :disabled="isDisabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="生效日期"
                prop="effectiveDate"
                :rules="requiredRow"
              >
                <ts-date-picker
                  style="width: 100%"
                  v-model="editData.effectiveDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择生效日期"
                  :disabled="isDisabled"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <div
            style="width: 100%;background-color:#efefef;margin-bottom: 15px;"
            v-if="editData.tmpEmployeeStatus == 2"
          >
            <el-divider direction="vertical" class="top"></el-divider>
            <span class="fontWeight">离职信息</span>
          </div>
          <ts-row v-if="editData.tmpEmployeeStatus == 2">
            <ts-col :span="12">
              <ts-form-item
                label="离职日期"
                prop="dimissionDate"
                :rules="requiredRow"
              >
                <ts-date-picker
                  style="width: 100%"
                  v-model="editData.dimissionDate"
                  valueFormat="YYYY-MM-DD"
                  placeholder="请选择离职日期"
                  :disabled="isDisabled"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                label="离职原因"
                prop="dimissionReason"
                :rules="requiredRow"
              >
                <ts-input
                  v-model="editData.dimissionReason"
                  placeholder=""
                  :disabled="isDisabled"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
        </ts-form>
        <template>
          <div style="position: fixed;bottom: 20px;right: 20px;z-index: 99999;">
            <ts-button type="primary" @click="handleConfirm">确定</ts-button>
            <ts-button @click="handleCancelModel">取消</ts-button>
          </div>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      title: '新增',
      editType: 'add',
      visible: false,
      loading: false,
      isDisabled: false,

      // numberList: [],
      textList: [],
      treeData: [],
      editData: {},
      requiredRow: { required: true, message: '必填' }
    };
  },
  methods: {
    open({ treeData = [], editType = 'add', editData = {} }) {
      this.treeData = treeData.map((item, i) => {
        return {
          ...item
        };
      });
      this.isDisabled = false;
      if (editType == 'add') {
        this.title = '新增';
      } else if (editType == 'edit') {
        this.title = '编辑';
      } else {
        this.title = '查看';
        this.isDisabled = true;
      }
      this.editType = editType;
      this.editData = editData;
      this.visible = true;
      this.loading = false;
      this.$nextTick(() => {
        this.$refs.treeSelect && this.$refs.treeSelect.$forceUpdate();
      });
    },
    handleChangeTree(e) {
      const { id, name } = e;
      this.editData.orgId = id;
      this.editData.orgName = name;
    },
    handleCancelModel() {
      this.visible = false;
      this.keyList = [];
    },
    async handleConfirm() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate || this.loading) {
        return;
      }
      this.loading = true;
      if (!this.editData.orgId) {
        this.editData.orgId = this.treeData[0].id;
        this.editData.orgName = this.treeData[0].name;
      }

      this.ajax[
        this.editType == 'add'
          ? 'handleAddEmployeeTemporaryTableData'
          : 'handleEditEmployeeTemporaryTableData'
      ](this.editData).then(res => {
        this.loading = false;
        if (!res.success) {
          this.$message.error(
            res.message || (this.editType == 'add' ? '新增' : '编辑') + '失败'
          );
          return;
        }
        this.$message.success('操作成功');
        this.$emit('success');
        this.handleCancelModel();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.month-picker {
  width: 100%;
}
::v-deep {
  .dialog-setting-detail {
    margin-top: 44px;
    overflow: hidden;
    .el-drawer__header {
      .dialog-title {
        // color: #4148ff;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
      }
      .el-drawer__close-btn {
        position: relative;
        top: -9px;
      }
    }
    .el-drawer__body {
      padding: 0 10px;
      overflow: hidden;
      .content {
        .detail {
          .el-form .el-form-item {
            margin: 0;
          }
        }
        .ts-button {
          &.is-disabled {
            color: rgb(204, 204, 204) !important;
            border-color: rgb(231, 235, 240) !important;
            background-color: rgb(250, 250, 250) !important;
            &:hover {
              cursor: not-allowed;
            }
          }
        }
        .fontWeight {
          font-size: 16px;
          font-weight: 600;
        }
        .top {
          width: 4px;
          background-color: #4148ff;
          height: 21px;
          top: -3px;
        }
        .form_item {
          line-height: 36px;
        }
        .record-list {
          .item-list {
            max-height: calc(100vh - 350px);
            overflow-y: auto;
          }
          // 设置滚动条的宽度
          .item-list::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          // 设置滚动条的背景色和圆角
          u.item-listl::-webkit-scrollbar-thumb {
            border-radius: 8px;
            background: rgba(153, 153, 153, 0.4);
            &:hover {
              background: rgba(153, 153, 153, 0.8);
            }
          }
          u.item-listl::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            background: #fff;
          }
          .record-item {
            margin-top: 8px;
            padding: 0 8px;
            .item-title {
              display: flex;
              justify-content: space-between;
              font-weight: 600;
              margin-bottom: 8px;
            }
            .form-table {
              flex: 1;
              transform: scale(1);
              height: auto;
              .el-table__header-wrapper tr th {
                background: #d2def0;
                min-width: 0px;
                box-sizing: border-box;
                text-overflow: ellipsis;
                vertical-align: middle;
                position: relative;
                line-height: 30px;
                padding: 0;
              }
              .el-table__row td {
                padding: 0;
                line-height: 30px;
                height: 30px;
              }
              .details-span {
                color: $primary-blue;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
