import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      actions: [],
      searchForm: {},
      searchList: [
        {
          label: '姓名/工号',
          value: 'employeeNo',
          element: 'TsInput',
          elementProp: {
            placeholder: '请输入姓名/工号'
          }
        },
        {
          label: '员工状态',
          value: 'tmpEmployeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '全部', value: '', element: 'ts-option' },
            { label: '在职', value: '1', element: 'ts-option' },
            { label: '离职', value: '2', element: 'ts-option' }
          ]
        },
        {
          label: '创建时间',
          value: 'createDateList'
        }
      ],

      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center'
        },
        {
          label: '所属组织',
          prop: 'orgName',
          width: 160,
          align: 'center'
        },
        {
          label: '工号',
          prop: 'employeeNo',
          width: 130,
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          width: 120,
          align: 'center'
        },
        {
          label: '性别',
          prop: 'genderName',
          width: 60,
          align: 'center'
        },
        {
          label: '联系电话',
          prop: 'phoneNumber',
          width: 130,
          align: 'center'
        },
        {
          label: '岗位名称',
          prop: 'tmpPositionName',
          width: 130,
          align: 'center'
        },
        {
          label: '年龄',
          prop: 'age',
          width: 60,
          align: 'center'
        },
        {
          label: '岗位工资',
          prop: 'tmpPositionSalary',
          width: 80,
          align: 'center'
        },
        {
          label: '入职时间',
          prop: 'joinDate',
          align: 'center',
          // sortable: true,
          width: 110
        },
        {
          label: '离职时间',
          prop: 'dimissionDate',
          align: 'center',
          // sortable: true,
          width: 110
        },
        {
          label: '员工状态',
          prop: 'tmpEmployeeStatus',
          align: 'center',
          width: 90,
          // sortable: true,
          render: (h, { row }) => {
            let text = row.tmpEmployeeStatus == '1' ? '在职' : '离职';
            let styles = row.tmpEmployeeStatus == '2' ? { color: 'red' } : {};
            return h('div', { style: styles }, text);
          }
        },
        {
          label: '同步状态',
          prop: 'isSyncSalary',
          align: 'center',
          width: 90,
          // sortable: true,
          render: (h, { row }) => {
            let text = row.isSyncSalary == 'Y' ? '已同步' : '未同步';
            let styles = row.isSyncSalary == 'N' ? { color: 'red' } : {};
            return h('div', { style: styles }, text);
          }
        },
        {
          label: '生效时间',
          prop: 'effectiveDate',
          align: 'center',
          width: 120
        },
        {
          label: '更新时间',
          prop: 'updateDate',
          align: 'center',
          width: 160
        },
        {
          label: '操作',
          align: 'center',
          width: 140,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            let arr = [
              {
                label: '修改',
                event: this.handleEdit,
                isStatus: '1'
              },
              {
                label: '详情',
                event: this.handleShow,
                isStatus: ''
              },
              {
                label: '转档',
                event: this.handleTransfer,
                isStatus: '33'
              }
            ];
            let newArr = arr.filter(e => {
              return (
                e.isStatus == '' ||
                e.isStatus.indexOf(row.tmpEmployeeStatus) > -1
              );
            });
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: newArr }
            });
          }
        }
      ]
    };
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let { createDateList = [] } = this.searchForm;
      let [searchStartTime = '', searchEndTime = ''] = createDateList;
      let formData = {
        ...this.searchForm,
        ...this.org,
        searchStartTime,
        searchEndTime,
        pageNo,
        pageSize
      };
      let num = 0;
      searchStartTime && num++;
      searchEndTime && num++;
      if (num == 1) {
        this.$newMessage('warning', '请选择【创建时间】完整的时间区间查询');
        return;
      }
      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        }
      });
      delete formData.createDateList;
      let res = await this.ajax.getHrmsEmployeeTemporaryList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
