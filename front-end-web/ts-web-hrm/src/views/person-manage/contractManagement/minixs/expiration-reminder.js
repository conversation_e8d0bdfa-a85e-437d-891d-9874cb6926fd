export default {
  data() {
    return {
      searchForm: {},
      actions: [],
      dictObject: {},
      searchList: [
        {
          label: '工号/姓名',
          value: 'employeeNo',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入工号/姓名'
          }
        },
        {
          label: '编制类型',
          value: 'establishmentType',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true,
            placeholder: '请选择'
          },
          dictType: 'establishment_type',
          childNodeList: []
        },
        {
          label: '合同状态',
          value: 'signType',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '即将到期',
              value: '',
              element: 'ts-option'
            },
            {
              label: '已到期',
              value: '1',
              element: 'ts-option'
            }
          ]
        },
        {
          label: '员工状态',
          value: 'employeeStatusBox',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true,
            placeholder: '请选择'
          },
          dictType: 'employee_status',
          childNodeList: []
        },
        {
          label: '合同类型',
          value: 'contractTypeBox',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true,
            placeholder: '请选择'
          },
          dictType: 'labor_contract_type',
          childNodeList: []
        },
        {
          label: '身份证号',
          value: 'employeeNo',
          element: 'ts-input',
          elementProp: {
            clearable: true,
            placeholder: '请输入身份证号'
          }
        },
        {
          label: '入职日期',
          value: 'entryDate'
        },
        {
          label: '合共开始日期',
          value: 'startTimeStrSearch'
        },
        {
          label: '合共结束日期',
          value: 'endTimeStrSearch'
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          fixed: 'left',
          width: 70
        },
        {
          label: '操作',
          align: 'center',
          width: 60,
          fixed: 'left',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '编辑',
                icon: 'vxe-icon-edit',
                event: this.handleEmployeeSetting
              }
            ];
            if (row.signType == 1) {
              actionList.push({
                icon: 'vxe-icon-signature',
                label: '续签',
                event: this.handleResetPassword
              });
              actionList.push({
                icon: 'vxe-icon-radio-checked',
                label: '解除',
                event: this.handleUpdateNo
              });
            }
            if (row.signType == 3) {
              actionList.push({
                icon: 'vxe-icon-signature',
                label: '续签',
                event: this.handleResetPassword
              });
            }
            actionList.push({
              icon: 'vxe-icon-delete',
              label: '删除',
              event: this.handleEnableAccount
            });
            if (row.signType == 5) {
              actionList = [];
            }
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          fixed: 'left',
          width: 190,
          sortable: true,
          sortBy: 't1.employee_no'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          align: 'center',
          width: 120,
          fixed: 'left',
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'primary-span',
                on: { click: () => this.handeleDetail(row) }
              },
              row.employeeName
            );
          }
        },
        {
          label: '员工状态',
          prop: 'employeeStatusName',
          align: 'center',
          width: 100
        },
        {
          label: '科室',
          prop: 'empOrgName',
          align: 'center',
          width: 100
        },
        {
          label: '身份证号',
          prop: 'card',
          align: 'center',
          width: 120
        },
        {
          label: '入职日期',
          prop: 'entryDate',
          align: 'center',
          sortable: true,
          sortBy: 't2.entry_date',
          width: 130
        },
        {
          label: '合同编号',
          prop: 'contractNumber',
          align: 'center',
          width: 130
        },
        {
          label: '合同类型',
          prop: 'contractTypeName',
          align: 'center',
          width: 130
        },
        {
          label: '合同状态',
          prop: 'signTypeName',
          align: 'center',
          width: 130
        },
        {
          label: '约定岗位',
          prop: 'contracJobsName',
          align: 'center',
          width: 130
        },
        {
          label: '存档地',
          prop: 'archiveSite',
          align: 'center',
          width: 130
        },
        {
          label: '合同期限',
          prop: 'allotedTime',
          align: 'center',
          width: 100,
          render: (h, { row }) => {
            let label = '';
            if (row.allotedTime == 1) {
              if (row.allotedDate && row.allotedDate != '') {
                let date = row.allotedDate;
                let type = row.allotedType;
                if (type == 'Y') {
                  label = date + '年';
                } else if (type == 'M') {
                  label = date + '月';
                } else if (type == 'D') {
                  label = date + '天';
                }
              }
            }
            return h('span', {}, label);
          }
        },
        {
          label: '合同开始日期',
          prop: 'startTime',
          align: 'center',
          sortable: true,
          sortBy: 't2.start_time',
          width: 100
        },
        {
          label: '合同结束日期',
          prop: 'endTime',
          align: 'center',
          sortable: true,
          sortBy: 't1.end_time',
          width: 100
        },
        {
          label: '合同签订日期',
          prop: 'signTime',
          align: 'center',
          width: 130
        },
        {
          label: '合同签订次数',
          prop: 'frequency',
          align: 'center',
          width: 130
        },
        {
          label: '到期倒计时',
          prop: 'countDown',
          align: 'center',
          width: 130
        },
        {
          label: '到期提醒',
          prop: 'tip',
          align: 'center',
          width: 120,
          render: (h, { row }) => {
            if (row.remind) {
              return h('vxe-switch', {
                attrs: {
                  value: true,
                  'open-label': '是',
                  'close-label': '否'
                },
                on: {
                  change: () => {
                    this.handleUpdateRemind(row);
                  }
                }
              });
            }
          }
        }
      ]
    };
  },
  methods: {
    // 集中处理下拉字典查询条件
    formatSearchList() {
      this.searchList.forEach(item => {
        if (item.dictType) {
          let param = {
            dicTypeId: item.dictType,
            pageNo: 1,
            pageSize: 999
          };
          this.ajax.getDictItemList(param).then(res => {
            item.childNodeList = res.rows.map(e => {
              return {
                label: e.itemName,
                value: e.itemNameValue,
                element: 'ts-option'
              };
            });
          });
        }
        if (item.api) {
          this.ajax[item.api]().then(res => {
            item.childNodeList = res.rows.map(e => {
              return {
                label: e.text,
                value: e.code,
                element: 'ts-option'
              };
            });
          });
        }
      });
    }
  }
};
