export default {
  data() {
    return {
      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '',
          value: 'searchKey'
        },
        {
          label: '状态',
          value: 'auditStatus',
          element: 'ts-select',
          childNodeList: [
            {
              element: 'ts-option',
              label: '全部',
              value: ''
            },
            {
              element: 'ts-option',
              label: '审批中',
              value: '1'
            },
            {
              element: 'ts-option',
              label: '已完结',
              value: '2'
            }
          ]
        },
        {
          label: '时间',
          value: 'date',
          element: 'base-date-range-picker'
          // elementProp: {
          //   format: 'YYYY-MM-DD',
          //   valueFormat: 'YYYY-MM-DD',
          //   rangeSeparator: '至',
          //   startPlaceholder: '开始日期',
          //   endPlaceholder: '结束日期'
          // }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'tableIndex',
          align: 'center',
          width: 50
        },
        {
          label: '事项名称',
          prop: 'operationTitle',
          align: 'center',
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleApproveDetails(row) }
              },
              row.operationTitle
            );
          }
        },
        {
          label: '员工工号',
          prop: 'employeeNo',
          width: '100',
          align: 'center'
        },
        {
          label: '员工姓名',
          prop: 'employeeName',
          align: 'center',
          width: '70'
        },
        {
          label: '性别',
          prop: 'gender',
          align: 'center',
          width: '70',
          render: (h, { row }) => {
            return h('div', {}, row.gender === '0' ? '男' : '女');
          }
        },
        {
          label: '提交时间',
          prop: 'createDate',
          align: 'center',
          width: '150'
        },
        {
          label: '当前审批人',
          prop: 'assigneeNames',
          align: 'center'
        },
        {
          label: '审批状态',
          prop: 'auditStatus',
          align: 'center',
          width: '110',
          render: (h, { row }) => {
            let dir = {
              0: '草稿',
              1: '审批中',
              2: '已完结',
              3: '驳回'
            };

            return h('div', {}, dir[row.auditStatus] || '');
          }
        },
        {
          label: '审批结果',
          prop: 'key1',
          align: 'center',
          render: (h, { row }) => {
            if (row.auditStatus == 0) {
              return '';
            }
            return h('div', {}, [
              h('span', {}, `合格:${row.passNumbers},`),
              h('span', { class: 'red' }, `不合格:${row.unPassNumbers}`)
            ]);
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          headerSlots: 'action',
          render: (h, { row }) => {
            let actionList = [];

            if (this.activeTab == '1') {
              actionList.push({
                label: '审批',
                event: this.handleApprove
              });
            }

            if (this.activeTab == '2') {
              actionList.push({
                label: '查看',
                event: this.handleApproveDetails
              });
            }

            if (this.activeTab == '3') {
              if (row.auditStatus == 0) {
                actionList.push({
                  label: '编辑',
                  event: this.handleInitiateMatters
                });
              } else {
                actionList.push({
                  label: '查看',
                  event: this.handleApproveDetails
                });
              }
            }

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  }
};
