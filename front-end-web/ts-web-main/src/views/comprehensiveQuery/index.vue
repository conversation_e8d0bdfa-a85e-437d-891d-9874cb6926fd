<template>
  <div class="comprehensive-query" v-loading="loading">
    <div class="search-top">
      <el-date-picker
        style="margin-right: 30px;"
        v-model="value"
        type="week"
        format="yyyy 第 WW 周"
        placeholder="选择周"
        @change="handleChangeDate"
      />

      <el-radio
        v-model="radioValue"
        v-for="item in hospitalList"
        :key="item.yqid"
        :label="item.yqid"
        >{{ item.yqmc }}</el-radio
      >

      <el-button class="export" type="primary" size="mini" @click="handleExport"
        >导出</el-button
      >
    </div>

    <div style="height: 30px;">
      <el-table class="table_header" style="width: 100%;" row-key="id" border>
        <el-table-column :resizable="false">
          <!-- 资源 -->
          <template #header>
            长沙市中医医院(长沙市第八医院)临床科室主要业务指标
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="height: 30px;">
      <el-table class="table_header" style="width: 100%;" row-key="id" border>
        <el-table-column :resizable="false">
          <!-- 资源 -->
          <template #header>
            开始日期：{{ datePicker[0] }} 0:00:00 结束日期：{{ datePicker[1] }}
            23:59:59
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 主体 -->
    <div ref="tableBody" class="table_body">
      <div class="table_body_relative">
        <el-table
          style="width: 100%"
          :data="tableData"
          :max-height="tableHeight"
          row-key="id"
          border
        >
          <el-table-column
            align="center"
            prop="ksmc"
            label="科室"
            min-width="200"
            fixed="left"
          />

          <el-table-column align="right" prop="sr" label="本期收入（万元）" />
          <el-table-column
            align="right"
            prop="sr_tq"
            label="同期收入（万元）"
          />
          <el-table-column align="right" prop="sr_tqzf" label="收入同比±%" />

          <el-table-column align="right" prop="ghrc" label="本期门急诊人次" />
          <el-table-column
            align="right"
            prop="ghrc_tq"
            label="同期门急诊人次"
          />
          <el-table-column
            align="right"
            prop="ghrc_tqzf"
            label="门急诊人次同比±%"
          />

          <el-table-column align="right" prop="ryrc" label="本期入院人次" />
          <el-table-column align="right" prop="ryrc_tq" label="同期入院人次" />
          <el-table-column
            align="right"
            prop="ryrc_tqzf"
            label="入院人次同比±%"
          />
          <el-table-column
            align="right"
            prop="mjz_zzybl"
            label="本期门急诊人次转入院比例"
          />
          <el-table-column
            align="right"
            prop="mjz_zzybl_tq"
            label="同期门急诊人次转入院比例"
          />
          <el-table-column align="right" prop="zyrc" label="本期在院人次" />
          <el-table-column align="right" prop="zyrc_tq" label="同期在院人次" />
          <el-table-column
            align="right"
            prop="zyrc_tqzf"
            label="在院人次同比±%"
          />

          <el-table-column
            align="right"
            prop="cyhz_cjfy"
            label="本期出院患者次均费用"
            minWidth="100"
          />
          <el-table-column
            align="right"
            prop="cyhz_cjfy_tq"
            label="同期出院患者次均费用"
            minWidth="100"
          />
          <el-table-column
            prop="cyhz_cjfy_tqzf"
            align="right"
            label="出院患者次均费用同比±%"
          />

          <el-table-column
            align="right"
            prop="mzhz_cjfy"
            label="本期门急诊次均费用"
          />
          <el-table-column
            align="right"
            prop="mzhz_cjfy_tq"
            label="同期门急诊次均费用"
          />
          <el-table-column
            align="right"
            prop="mzhz_cjfy_tqzf"
            label="门急诊次均费用同比±%"
          />

          <el-table-column align="right" prop="cwsyl" label="本期床位使用率%" />
          <el-table-column
            align="right"
            prop="cwsyl_tq"
            label="同期床位使用率%"
          />
          <el-table-column
            align="right"
            prop="cwsyl_tqzf"
            label="床位使用率同比±%"
          />

          <el-table-column align="right" prop="ssrc" label="本期手术人次" />
          <el-table-column align="right" prop="ssrc_tq" label="同期手术人次" />
          <el-table-column
            align="right"
            prop="ssrc_tqzf"
            label="手术人次同比±%"
          />

          <el-table-column align="right" prop="cyrc" label="本期出院人次" />
          <el-table-column align="right" prop="cyrc_tq" label="同期出院人次" />
          <el-table-column
            align="right"
            prop="cyrc_tqzf"
            label="出院人次同比±%"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import deanInquiryApi from '@/api/ajax/deanInquiry.js';
export default {
  data() {
    return {
      value: '',
      radioValue: '',

      loading: false,
      datePicker: [],
      hospitalList: [],
      tableData: [
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' },
        { LEFT_OTHERS: '1' }
      ],
      tableHeight: 500
    };
  },
  watch: {
    radioValue() {
      this.handleSearchTableData();
    }
  },
  mounted() {
    this.value = new Date();
    this.handleChangeDate(this.value);
    this.handleSearchTableData();

    deanInquiryApi.getHospInfoData().then(res => {
      if (res.success && res.statusCode === 200) {
        res.object.unshift({
          yqmc: '全院',
          yqid: ''
        });
        this.hospitalList = res.object || [];
      }
    });
    this.$nextTick(() => {
      this.tableHeight = this.$refs.tableBody.offsetHeight - 8;
    });
  },
  methods: {
    async handleSearchTableData() {
      this.tableData = [];
      this.loading = true;
      const res = await deanInquiryApi.getWeeklyForm({
        begdate: this.datePicker[0],
        enddate: this.datePicker[1],
        yqid: this.radioValue
      });
      this.loading = false;
      if (res.success && res.statusCode === 200) {
        this.tableData = res.object || [];
      }
    },
    handleChangeDate(e) {
      const date = dayjs(e).format('YYYY-MM-DD');
      this.$set(
        this.datePicker,
        0,
        dayjs(date)
          .day(0)
          .format('YYYY-MM-DD')
      );
      this.$set(
        this.datePicker,
        1,
        dayjs(date)
          .day(6)
          .format('YYYY-MM-DD')
      );
      this.handleSearchTableData();
    },
    async handleExport() {
      let url = `/ts-oa/weeklyForm/api/exportWeeklyForm?begdate=${this.datePicker[0]}&enddate=${this.datePicker[1]}&yqid=${this.radioValue}`;
      let link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); //下载完成移除元素
    }
  }
};
</script>

<style lang="scss" scoped>
.comprehensive-query {
  background: #fff;
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow: hidden;
  box-sizing: border-box;

  .search-top {
    margin-bottom: 8px;
    position: relative;
    .export {
      position: absolute;
      right: 8px;
      top: 0;
    }
  }

  /deep/ .table_header {
    .el-table__empty-block {
      display: none;
    }
  }
  /deep/ .table_body {
    height: calc(100% - 102px);
    position: relative;
    overflow: hidden;
    // 去除表头背景颜色
    /deep/ .el-table__row {
      td {
        border-right: 1px solid #e1e1e1 !important;
        border-bottom: 1px solid #e1e1e1 !important;
      }
    }
    .table_body_relative {
      position: relative;
    }
    /deep/.cell {
      padding: 0 !important;
    }
  }
  /deep/ .el-table__fixed-header-wrapper .el-table__header th {
    padding: 0;
    background-color: #d2def0;
    color: #333;
    .cell {
      font-weight: bold;
    }
  }
  /deep/ .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    &::-webkit-scrollbar-thumb {
      -webkit-box-shadow: inset005pxrgba(0, 0, 0, 0.2);
      background: rgba(0, 0, 0, 0.2);
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset005pxrgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
