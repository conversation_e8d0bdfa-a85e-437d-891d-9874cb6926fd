<!-- 首页设置 -->
<template>
  <div class="index">
    <div class="doorList">
      <div class="door defaultImag" @click="add"></div>
      <div class="door" v-for="(item, index) in doorList" :key="index">
        <div class="doorTop">
          <img :src="item.linkImage" />
          <div class="content">
            <p class="title">{{ item.linkName }}</p>
            <p class="link">{{ item.linkUrl }}</p>
          </div>
          <p class="default" v-if="item.status == 2">已失效</p>
        </div>
        <div class="doorBottom">
          <i
            class="fa oaicon oa-icon-unlock  outlink-unlock"
            title="启用"
            @click="updateStatus(item, 1)"
            v-if="item.status == 2"
          ></i>
          <i
            class="fa oaicon oa-icon-lock  outlink-lock"
            title="禁用"
            @click="updateStatus(item, 2)"
            v-if="item.status == 1"
          ></i>
          <i class="fa fa-trash-o" @click="deletePortal(item)"></i>
          <i class="fa fa-pencil-square-o" @click="edit(item)"></i>
        </div>
      </div>
    </div>
    <dialog-add-or-edit ref="dialogAddOrEdit" @ok="selectPortalThemeList" />
  </div>
</template>

<script>
import dialogAddOrEdit from './dialog-addOrEdit.vue';
export default {
  components: { dialogAddOrEdit },
  data() {
    return {
      doorList: []
    };
  },
  mounted() {
    this.selectPortalThemeList();
  },
  methods: {
    async deletePortal(item) {
      try {
        await this.$confirm(
          `<span>删除后将无法恢复，您确认删除吗？</span>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.ajax.friendlyLinkHomeDel({ ...item }).then(res => {
          if (!res.success) this.$message.error('删除失败');
          this.$message.success('删除成功');
          this.selectPortalThemeList();
        });
      } catch (e) {
        console.error(e);
      }
    },
    // 获取门户数据别表
    selectPortalThemeList() {
      this.ajax.friendlyLinkHome().then(res => {
        this.doorList = res.rows || [];
      });
    },
    updateStatus(item, status) {
      item.status = status;
      this.ajax.friendlyLinkHomeUpdate(item, 'update').then(res => {
        if (res.success) {
          let message = status == 1 ? '启用成功' : '禁用成功';
          this.$message.success(message);
          this.selectPortalThemeList();
        }
      });
    },
    add() {
      this.$refs.dialogAddOrEdit.edit();
    },
    edit(item) {
      this.$refs.dialogAddOrEdit.edit(item);
    }
  }
};
</script>

<style lang="scss" scoped>
.index {
  .doorList {
    margin-top: 10px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .door {
      width: 31%;
      padding: 8px;
      border: 1px solid #e4e4e4;
      border-radius: 10px;
      height: 190px;
      margin-right: 8px;
      margin-bottom: 8px;
      position: relative;
      background-color: #fafafa;
      &.defaultImag {
        background-image: url('../../../../assets/img/index/add_circle.png');
        background-repeat: no-repeat;
        background-position: center center;
        cursor: pointer;
      }
      .doorTop {
        display: flex;
        img {
          float: left;
          width: 54px;
          height: 54px;
          margin-right: 16px;
          border-radius: 6px;
          background-color: #fff;
        }
        .content {
          color: #333;
          .title {
            font-size: 18px;
            color: #333333;
            line-height: 24px;
            margin-bottom: 8px;
            margin-right: 60px;
          }
          .link {
            text-align: left;
            font-weight: 400;
            color: #333;
            line-height: 20px;
            font-size: 15px;
            margin-bottom: 6px;
          }
        }
        .default {
          line-height: 32px;
          border-radius: 10px;
          border: 1px solid #ccc;
          padding: 0 16px;
          color: #999;
          position: absolute;
          top: 5px;
          right: 10px;
        }
      }
      .showTitle {
        margin-top: 12px;
        color: rgba(51, 51, 51, 0.7);
        line-height: 20px;
        font-size: 15px;
        word-break: break-all;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .doorBottom {
        position: absolute;
        bottom: 0;
        display: flex;
        flex-direction: row-reverse;
        width: 100%;
        right: 10px;
        bottom: 10px;
        .fa {
          font-size: 20px;
          color: #999;
          cursor: pointer;
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
