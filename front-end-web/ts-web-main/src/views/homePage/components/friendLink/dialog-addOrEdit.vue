<!-- 首页设置-弹窗 -->
<template>
  <el-dialog
    custom-class="homePageSet"
    :title="title"
    width="600px"
    :show-close="true"
    :visible.sync="visible"
    :append-to-body="true"
    @close="cancel"
  >
    <div class="setContent">
      <ts-form ref="ruleForm" :model="form" labelWidth="110px">
        <ts-form-item label="图标" prop="linkImage" :rules="rules.required">
          <base-upload-fileid
            ref="linkImage"
            v-model="form.linkImage"
            :limit="1"
            :moduleName="'global'"
            class="systemSetUpload"
          >
            <div id="global-img-1" class="img-box">
              <img src="@/assets/img/index/defPhoto.png" />
              <span>推荐尺寸60*60</span>
            </div>
          </base-upload-fileid>
        </ts-form-item>
        <ts-form-item label="名称" prop="linkName" :rules="rules.required">
          <ts-input
            v-model="form.linkName"
            :maxlength="10"
            placeholder="请填写10字以内的名称"
          />
        </ts-form-item>
        <ts-form-item label="跳转地址" prop="linkUrl" :rules="rules.required">
          <ts-input
            v-model="form.linkUrl"
            :maxlength="50"
            placeholder="请填写地址"
          />
        </ts-form-item>
      </ts-form>
    </div>
    <div class="footer">
      <el-button class="ts-button" type="primary" @click="submit"
        >保存</el-button
      >
      <el-button class="ts-button cancel" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils/deepClone';
export default {
  data() {
    return {
      visible: false,
      title: '新建友情链接',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async edit(rowData = null) {
      if (rowData) {
        let data = deepClone(rowData);
        data.linkImage = data.linkImage.split('/').pop();
        this.form = data;
      }
      this.visible = true;
    },
    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let submitData = deepClone(this.form);
      submitData.linkImage = this.$refs.linkImage.fileList[0].url;
      let type = submitData.id ? 'update' : 'save';
      if (!submitData.id) {
        submitData.status = 1;
      }
      this.ajax.friendlyLinkHomeUpdate(submitData, type).then(res => {
        if (res.success) {
          this.$message.success(res.object);
          this.cancel();
          this.$emit('ok');
        } else {
          this.$message.error(res.message || '操作失败');
        }
      });
    },
    cancel() {
      this.visible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  height: 300px;
}
/deep/ .systemSetUpload {
  .picture-list,
  .picture-item {
    width: 60px !important;
    height: 60px !important;
  }
}
.homePageSet {
  .ts-button {
    background-color: #5260ff;
    border: 1px #5260ff solid;
    border-radius: 2px;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    &.cancel {
      background: #fff;
      border-color: #ccc;
      color: #333;
    }
  }
  .setContent {
    padding-bottom: 50px;
    /deep/ .ts-form {
      .ts-form-item {
        margin-bottom: 10px;
      }
    }
  }
  .img-box {
    display: flex;
    align-items: center;
    img {
      width: 60px;
      height: 60px;
    }
    span {
      margin-left: 10px;
      color: #999;
    }
  }
  .footer {
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 8px 15px;
    text-align: right;
    border-top: 1px solid #eee;
    background-color: #fff;
  }
}
</style>
