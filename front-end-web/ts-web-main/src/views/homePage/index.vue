<!-- 首页配置 -->
<template>
  <div class="homePage">
    <div class="content">
      <div class="oa-nav">
        <a
          href="javascript:;"
          class="oa-nav_item"
          v-for="(item, index) in tabList"
          :class="activeTab == item.id ? 'active' : ''"
          :key="index"
          @click="chageTab(item)"
        >
          {{ item.label }}
        </a>
      </div>
      <div class="component">
        <el-scrollbar style="height: 100%;">
          <component :is="componentsIndex"></component>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script>
import index from './components/index';
import outLink from './components/outLink';
import friendLink from './components/friendLink';
import systemSet from './components/systemSet';
export default {
  components: { index, outLink, friendLink, systemSet },
  data() {
    return {
      tabList: [
        {
          id: '1',
          label: '内容维护',
          components: 'index'
        },
        {
          id: '2',
          label: '外部系统',
          components: 'outLink'
        },
        {
          id: '3',
          label: '友情链接',
          components: 'friendLink'
        },
        {
          id: '4',
          label: '全局配置',
          components: 'systemSet'
        }
      ],
      activeTab: '1',
      componentsIndex: 'index'
    };
  },
  mounted() {},
  methods: {
    chageTab(item) {
      this.activeTab = item.id;
      this.componentsIndex = item.components;
    }
  }
};
</script>

<style lang="scss" scoped>
.homePage {
  width: 100%;
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  .content {
    width: 100%;
    height: 100%;
    background-color: #fff !important;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    padding: 8px;
    box-sizing: border-box;
  }

  .oa-nav {
    height: 36px;
    /* background-color: #fafafa; */
    padding-bottom: 8px;
    font-size: 0;
    .oa-nav_item {
      color: #333;
      font-size: 14px;
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      display: inline-block;
      min-width: 94px;
      text-align: center;
      background-color: #e8ecf2;
      border: 2px solid #e8ecf2;
      position: relative;
      text-decoration: none;
      &:nth-child(1) {
        border-radius: 4px 0 0 4px;
      }
      &:last-child {
        border-radius: 0 4px 4px 0;
      }
      &.active {
        color: var(--theme-color);
        background-color: #fff;
      }
    }
  }
  .component {
    width: 100%;
    height: calc(100% - 46px);
  }
}
</style>
