<template>
  <div class="content flex-column flex-col-center">
    <div class="title">科室总计建单占比</div>
    <div class="line"></div>
    <div class="flex-grow flex-center" style="width: 380px;">
      <div ref="pie" style="width: 380px; height: 262px;"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    deptCreatePercentData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pie: null
    };
  },
  watch: {
    deptCreatePercentData: function() {
      this.$nextTick(() => {
        this.renderPie();
      });
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.pie = this.$echarts.init(this.$refs.pie);
    });
  },
  methods: {
    renderPie() {
      let options = {
        series: [
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['35%', '55%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
            data: this.deptCreatePercentData,
            minAngle: 30,
            labelLine: {
              length2: 0
            },
            label: {
              formatter: function(params) {
                // let int = params.percent.toFixed(0);
                let int = params.percent;
                return `${params.name}\n${int}%`;
              },
              fontSize: 16,
              textBorderColor: 'transparent',
              textBorderWidth: 0,
              overflow: 'truncate',
              ellipsis: '...',
              fontWeight: 600,
              color: '#FFF',
              lineHeight: 22
            }
          }
        ]
      };

      if (!this.pie) {
        this.pie = this.$echarts.init(this.$refs.pie);
      }
      this.pie.clear();
      this.pie.setOption(options);
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 360px;
  height: 326px;
  padding-top: 12px;
}
.title {
  font-weight: 600;
  color: #ffffff;
  line-height: 32px;
  font-size: 24px;
}
.line {
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  border-radius: 50%;
  margin-top: 4px;
  margin-bottom: 14px;
}
</style>
