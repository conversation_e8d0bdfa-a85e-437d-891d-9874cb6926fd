<template>
  <div class="ts-container">
    <el-tabs type="card" v-model="active">
      <el-tab-pane :label="`未读(${unReadNumber || 0})`" name="0">
        <message-type
          type="0"
          :data="unread"
          @getPageNo="getPageNoHandle"
          @setPageNo="setPageNoHandle"
        ></message-type>
      </el-tab-pane>
      <el-tab-pane :label="`已读(${readedNumber || 0})`" name="1">
        <message-type
          type="1"
          :data="readed"
          @getPageNo="getPageNoHandle"
          @setPageNo="setPageNoHandle"
        ></message-type>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MessageType from './components/message-type';
export default {
  components: {
    MessageType
  },
  data() {
    return {
      active: '0',
      // 未读
      unread: {
        pageNo: 1,
        pageCount: 1,
        totalCount: 0,
        subject: ''
      },
      // 已读
      readed: {
        pageNo: 1,
        pageCount: 1,
        totalCount: 0,
        subject: ''
      },
      subject1: '',
      subject2: ''
    };
  },
  computed: {
    unReadNumber() {
      return this.$store.state.common.unReadNumber;
    },
    readedNumber() {
      return this.$store.state.common.readedNumber;
    }
  },
  methods: {
    getPageNoHandle({ type, pageNo, pageCount, totalCount }) {
      switch (type) {
        case '0':
          this.unread.pageNo = pageNo;
          this.unread.pageCount = pageCount;
          this.unread.totalCount = totalCount;
          break;
        case '1':
          this.readed.pageNo = pageNo;
          this.readed.pageCount = pageCount;
          this.readed.totalCount = totalCount;
          break;
        default:
          break;
      }
    },
    setPageNoHandle({ type, fn }) {
      let pass = true;
      switch (type) {
        case '0':
          this.unread.pageNo++;
          if (this.unread.pageCount < this.unread.pageNo) {
            pass = false;
            this.unread.pageNo--;
          }
          break;
        case '1':
          this.readed.pageNo++;
          if (this.readed.pageCount < this.readed.pageNo) {
            pass = false;
            this.readed.pageCount--;
          }
          break;
        default:
          break;
      }

      pass && this.$nextTick(fn);
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 8px;
  overflow: hidden;
  border-radius: 4px;
  background: #fff;
}
.scrollbar-wrapper {
  overflow-x: hidden !important;
}
/deep/ {
  .el-tabs {
    height: 100%;
  }
  .el-tabs__content {
    height: calc(100% - 48px);
    padding-bottom: 2px;
  }
  .el-tab-pane {
    height: 100%;
  }
}
</style>
