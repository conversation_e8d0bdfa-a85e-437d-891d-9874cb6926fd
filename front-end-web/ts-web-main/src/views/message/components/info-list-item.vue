<template>
  <el-scrollbar :ref="`scrollbar${$attrs.type}`" wrap-class="scrollbar-wrapper">
    <ul v-if="infoList.length > 0">
      <li
        class="list"
        v-for="item in infoList"
        :key="item.id"
        @click="itemClickHandle(item)"
      >
        <div>
          <span
            v-if="item.source"
            :class="{
              'type-styles': true,
              mail: item.source === '电子邮箱',
              workOrder: item.source === '工单管理',
              process: item.source === '流程管理'
            }"
            >{{ item.source }}</span
          >
          <span>{{ item.subject }}</span>
        </div>
        <p class="margin-t-b-4">
          <span class="margin-right-8"
            >操作人：<span class="operator">{{ item.senderName }}</span></span
          >
          <span>消息时间：{{ item.createTime }}</span>
        </p>
        <p>
          {{ item.source | sourceLabel }}<span v-html="item.content"></span>
        </p>
      </li>
    </ul>
    <img
      class="list-no-data-img"
      v-else
      src="@/assets/img/message-search-no-data.png"
      alt=""
    />
  </el-scrollbar>
</template>

<script>
import { debounce } from '@/unit/tool';
export default {
  name: 'info-list-item',
  props: {
    infoList: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.handleScroll();
  },
  filters: {
    sourceLabel: type => {
      let obj = {
        工单管理: '',
        电子邮箱: '邮箱主题:',
        null: ''
      };
      return obj[type];
    }
  },
  methods: {
    handleScroll() {
      const type = this.$attrs.type;
      let ref = `scrollbar${type}`;

      let scrollbarEl = this.$refs[ref].wrap;

      scrollbarEl.onscroll = debounce(e => {
        const { scrollHeight, scrollTop, clientHeight } = scrollbarEl;
        let fromTheBottom = scrollHeight - scrollTop - clientHeight;
        if (fromTheBottom < 5) {
          this.$emit('inTheBottomHandle');
        }
      }, 1000);
    },
    async itemClickHandle(item) {
      this.$emit('haveReadHandle', item);
    }
  }
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100% - 30px);

  .list-no-data-img {
    width: 360px;
    height: 260px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
  ul {
    .list {
      padding: 8px 0;
      border-bottom: 1px solid #e4e4e4;
      font-size: 14px;
      color: #333333;
      line-height: 19px;
      cursor: pointer;
      .type-styles {
        padding: 3px 5px;
        margin-right: 8px;
        border: 1px solid #ff6565;
        color: #ff6565;
        &.mail {
          border: 1px solid #00b578;
          color: #00b578;
        }
        &.workOrder {
          border: 1px solid #00b7f4;
          color: #00b7f4;
        }
        &.process {
          border: 1px solid #c19030;
          color: #c19030;
        }
      }
      &:hover {
        background: rgba(82, 96, 255, 0.08);
      }
      .margin-right-8 {
        margin-right: 8px;
      }
      .margin-t-b-4 {
        margin: 4px 0;
      }
      .operator {
        color: #ec7b25;
      }
    }
  }
}
</style>
