import Vue from 'vue';
import elementuiExtend from '@/extends/index.js';
import ElementUi from '@trasen/trasen-element-ui/lib';
import tsElement from '@trasen-oa/trasen-ui-web';
import BaseUpload from './base-upload/base-uplaod.vue';
import BaseUploadFileid from './base-upload-fileid/base-uplaod-fileid.vue';
Vue.prototype.qiankunParentNode = document.body;

export default {
  /**@desc 这个文件主要用于初始化各种当前项目公用组件**/
  install(Vue) {
    Vue.use(ElementUi, { size: 'small' });
    Vue.use(elementuiExtend); //初始化ElementUi框架
    Vue.component('TsFormItem', tsElement.FormItem);
    Vue.component('BaseUpload', BaseUpload);
    Vue.component('BaseUploadFileid', BaseUploadFileid);
    Vue.component('TsZtreeSelect', tsElement.ZtreeSelect);
  }
};
