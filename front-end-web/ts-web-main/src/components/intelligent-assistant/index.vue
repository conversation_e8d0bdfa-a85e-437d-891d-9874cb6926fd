<template>
  <div v-show="visible" class="draggable-float-window" :style="windowStyle">
    <!-- 窗体主体 -->
    <div class="resize-handle left" @mousedown="startResize"></div>
    <div class="float-header" @mousedown="startDrag">
      <p>DeepSeek智能助手</p>
      <div>
        <i class="el-icon-plus" @click.stop="newSession" title="新建会话"></i>
        <i
          class="el-icon-time history-icon"
          @click.stop="toggleHistoryPanel"
          title="查看历史会话"
        ></i>
        <i
          :class="isMaximized ? 'el-icon-copy-document' : 'el-icon-full-screen'"
          @click.stop="toggleMaximize"
          :title="isMaximized ? '还原窗口' : '最大化'"
        ></i>
        <i class="el-icon-close" @click.stop="close" title="关闭窗口"></i>
      </div>
    </div>
    <div class="resize-handle right" @mousedown="startResize"></div>
    <div class="float-content">
      <div class="chat-area">
        <div class="message-list">
          <el-scrollbar
            ref="scroll"
            style="height: 100%;"
            wrap-style="overflow-x:hidden;"
          >
            <!-- 添加初始欢迎信息 -->
            <div v-if="chatHistory.length === 0" class="welcome-container">
              <div class="welcome-message">
                <div class="Icon">
                  <img
                    src="@/assets/img/home/<USER>"
                    alt="Assistant Icon"
                    class="assistant-icon"
                  />
                </div>
                <div class="welcome-content">
                  我是 DeepSeek智能小助手，很高兴见到你！<br />
                  我可以帮你查资料、智能问题、以及基于通用DeepSeek的能力，<br />
                  请把你的任务交给我吧~
                </div>
              </div>
            </div>
            <div
              v-for="(msg, index) in chatHistory"
              :key="index"
              class="message"
            >
              <div class="user-message" v-if="msg.role === 'files'">
                <div class="files-content">
                  <img src="@/assets/img/doc.png" />
                  <div class="file-name">
                    <p class="name" :title="msg.name">{{ msg.name }}</p>
                    <p class="type">
                      <span>{{ msg.extension }}</span
                      ><span>{{ msg.size | formatFileSize }}</span>
                    </p>
                  </div>
                </div>
              </div>
              <div class="user-message" v-else-if="msg.role === 'user'">
                <div class="content">{{ msg.content }}</div>
              </div>
              <div class="assistant-message" v-else>
                <div class="Icon">
                  <img
                    src="@/assets/img/home/<USER>"
                    alt="Assistant Icon"
                    class="assistant-icon"
                  />
                </div>
                <div class="content">
                  <!-- 将思考中提示放在这里 -->
                  <!-- <div
                    v-if="loading && index === chatHistory.length - 1"
                    class="loading"
                  > -->
                  <div
                    v-if="msg.thinking && index === chatHistory.length - 1"
                    class="loading"
                  >
                    思考中<i class="el-icon-loading"></i>
                  </div>
                  <div class="thinking-time" v-if="msg.thinkingTime">
                    思考时间: {{ msg.thinkingTime }}秒
                  </div>
                  <template v-if="msg.content">
                    <template v-if="msg.content.includes('<think>')">
                      <div
                        v-for="(part, i) in msg.content.split('<think>')"
                        :key="i"
                      >
                        <div
                          v-if="part.includes('</think>')"
                          class="message-content"
                        >
                          <div
                            class="reasoning"
                            v-html="renderMarkdown(part.split('</think>')[0])"
                          ></div>
                          <div
                            v-if="part.split('</think>')[1]"
                            class="reply"
                            v-html="renderMarkdown(part.split('</think>')[1])"
                          ></div>
                        </div>
                        <div
                          v-else
                          class="reply"
                          v-html="renderMarkdown(part)"
                        ></div>
                      </div>
                    </template>
                    <div
                      v-else
                      class="reply"
                      v-html="renderMarkdown(msg.content)"
                    ></div>
                  </template>
                  <!-- 新增参考来源信息 -->
                  <div
                    v-if="msg.referenceLinks && msg.referenceLinks.length > 0"
                    class="reference-links"
                  >
                    <div class="link-title">
                      <i class="el-icon-link"></i>参考来源
                    </div>
                    <div
                      v-for="(link, idx) in msg.referenceLinks"
                      :key="idx"
                      class="link-item"
                    >
                      <i class="el-icon-document link-icon"></i>
                      <span class="link-text">{{
                        link.title || link.url
                      }}</span>
                      <span v-if="link.source_type" class="link-type"
                        >({{ link.source_type }})</span
                      >
                      <span v-if="link.score" class="link-score">{{
                        link.score.toFixed(2)
                      }}</span>
                      <!-- :href="link.url" -->
                      <a
                        target="_blank"
                        class="link-action"
                        @click.stop="handlePreview(link)"
                        title="预览"
                      >
                        <i class="el-icon-view"></i>
                      </a>
                      <!-- :href="link.url" -->
                      <a
                        download
                        class="link-action"
                        @click.stop="handleDownload(link)"
                        title="下载"
                      >
                        <i class="el-icon-download"></i>
                      </a>
                    </div>
                  </div>
                  <div class="tips">
                    <i class="el-icon-warning-outline"></i>本回答由 AI
                    生成，内容仅供参考，请仔细甄别。
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <!-- 添加历史会话面板 -->
        <div class="history-panel" v-show="showHistoryPanel">
          <div class="history-header">
            <span>{{ currentSessionId ? '相关会话' : '历史会话' }}</span>
            <i
              v-if="currentSessionId"
              class="el-icon-back"
              @click.stop="
                currentSessionId = null;
                loadHistorySessions();
              "
              title="返回全部历史"
            ></i>
          </div>
          <el-scrollbar class="history-scroll">
            <div v-for="(group, date) in groupedHistory" :key="date">
              <div class="history-date">{{ date }}</div>
              <div
                v-for="session in group"
                :key="session.id"
                class="history-item"
                @click="loadHistorySession(session)"
              >
                <div class="history-title">
                  {{ session.title || '未命名会话' }}
                  <span
                    v-if="
                      session.relatedSessions &&
                        session.relatedSessions.length > 0
                    "
                    class="related-count"
                  >
                    ({{ session.relatedSessions.length }})
                  </span>
                </div>
                <div class="history-time">{{ session.time }}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <!-- 输入区域 -->
      <div class="input-box">
        <textarea
          ref="textareaRef"
          class="textArea"
          v-model="inputText"
          @input="adjustTextareaHeight"
          placeholder="给AI助手发送消息"
          @keydown.enter.prevent="sendMessage"
        ></textarea>
        <!-- :disabled="loading" -->
        <!-- :class="loading || !inputText ? 'disabled' : ''" -->
        <div class="bottom">
          <el-upload
            v-if="calUploadFile"
            :accept="accept"
            action="/ts-oa/attachment/fileUpload?module=htsc"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :http-request="handleUploadFile"
          >
            <el-button type="primary" class="uploadButton" v-loading="loading"
              >上传合同</el-button
            >
          </el-upload>
          <div
            class="sendMessage"
            @click="loading ? cancelRequest() : sendMessage()"
          >
            <i
              :class="loading ? 'el-icon-video-pause' : 'el-icon-caret-right'"
            ></i>
            <!-- <i class="el-icon-top"></i> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import marked from 'marked';
import 'prismjs';
import 'prismjs/themes/prism.css';
export default {
  name: 'DraggableFloatWindow',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialX: {
      type: Number,
      default: window.innerWidth - 440 - 10
    },
    initialY: {
      type: Number,
      default: 60
    }
  },
  data() {
    return {
      dragging: false,
      resizing: false,
      startWidth: 430,
      minWidth: 400, // 最小宽度
      maxWidth: 800, // 最大宽度
      posX: this.initialX,
      posY: this.initialY,
      startX: 0,
      startY: 0,
      inputText: '',
      chatHistory: [],
      userInfo: {},
      file: null,
      loading: false,
      messages: [],
      apiEndpoint: '/v1/chat/completions',
      askUrl: '/deepseek/ask',
      currentAssistantMessage: '',
      showHistoryPanel: false,
      historySessions: [], // 存储历史会话
      currentSessionId: null,
      sessionRelations: {},
      currentReferenceLinks: [],
      isMaximized: false,
      originalWidth: 430,
      originalHeight: 'calc(100% - 100px)',
      originalX: this.initialX,
      originalY: this.initialY,
      abortController: null,
      accept: '.doc,.docx'
      // originalX: window.innerWidth - 440, // 默认在右侧
      // originalY: 60
    };
  },
  mounted() {
    // 确保初始位置正确
    this.posX = Math.min(this.posX, window.innerWidth - this.$el.offsetWidth);
    this.posY = Math.min(this.posY, window.innerHeight - this.$el.offsetHeight);

    // 初始化original值
    this.originalWidth = this.$el.offsetWidth;
    this.originalHeight = this.$el.style.height;
    this.originalX = this.posX;
    this.originalY = this.posY;
    // 初始化时加载历史会话
    this.adjustTextareaHeight();
  },
  computed: {
    windowStyle() {
      return {
        left: `${this.posX}px`,
        top: `${this.posY}px`
      };
    },
    groupedHistory() {
      const groups = {};
      this.historySessions.forEach(session => {
        const date = new Date(session.time).toLocaleDateString();
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(session);
      });
      return groups;
    },
    calUploadFile() {
      return this.$store.state.common.globalSetting.orgCode == 'yysdsrmyy';
    }
  },
  filters: {
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';

      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return (
        new Intl.NumberFormat('en-US', {
          style: 'decimal',
          minimumFractionDigits: 0,
          maximumFractionDigits: 2
        }).format(bytes / Math.pow(k, i)) +
        ' ' +
        sizes[i]
      );
    }
  },
  methods: {
    startResize(event) {
      event.preventDefault();
      this.resizing = true;
      this.startX = event.clientX;
      this.startWidth = this.$el.offsetWidth;
      this.startLeft = this.$el.offsetLeft; // 记录初始left位置
      this.isLeftResize = event.target.classList.contains('left'); // 判断是否是左侧拖动
      document.addEventListener('mousemove', this.onResize);
      document.addEventListener('mouseup', this.stopResize);
    },
    onResize(event) {
      if (this.resizing) {
        const deltaX = event.clientX - this.startX;
        let newWidth = this.startWidth;
        let newLeft = this.startLeft;

        if (this.isLeftResize) {
          // 左侧拖动时同时调整宽度和位置
          newWidth = this.startWidth - deltaX;
          newLeft = this.startLeft + deltaX;
        } else {
          // 右侧拖动只调整宽度
          newWidth = this.startWidth + deltaX;
        }

        // 应用限制
        newWidth = Math.min(Math.max(newWidth, this.minWidth), this.maxWidth);

        if (this.isLeftResize) {
          // 确保左侧拖动时不会超出屏幕
          newLeft = Math.max(
            0,
            Math.min(newLeft, window.innerWidth - newWidth)
          );
          this.posX = newLeft;
        }

        this.$el.style.width = newWidth + 'px';
      }
    },
    stopResize() {
      this.resizing = false;
      document.removeEventListener('mousemove', this.onResize);
      document.removeEventListener('mouseup', this.stopResize);
    },
    startDrag(event) {
      event.preventDefault();
      this.dragging = true;

      // 获取初始位置
      this.startX = event.clientX - this.posX;
      this.startY = event.clientY - this.posY;

      // 绑定移动事件
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    close() {
      this.$emit('intelligentAssistantShow');
    },
    onDrag(event) {
      if (this.dragging) {
        // 计算新位置
        const newX = event.clientX - this.startX;
        const newY = event.clientY - this.startY;

        // 边界限制
        const maxX = window.innerWidth - this.$el.offsetWidth;
        const maxY = window.innerHeight - this.$el.offsetHeight;
        this.posX = Math.max(0, Math.min(newX, maxX));
        this.posY = Math.max(0, Math.min(newY, maxY));
      }
    },
    stopDrag() {
      this.dragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },
    // 渲染 Markdown
    renderMarkdown(text) {
      // 处理 <think></think> 标签
      // const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
      // text = text.replace(thinkRegex, (match, p1) => {
      //   return `<div class="reasoning">${marked(p1)}</div><hr />`;
      // });
      return marked(text);
    },
    async sendMessage() {
      if (this.loading || !this.inputText) return;
      if (!this.inputText.trim()) return;
      this.loading = true;
      const startTime = Date.now(); // 记录开始时间
      this.abortController = new AbortController();
      const userMessage = {
        role: 'user',
        content: this.inputText
      };
      // 如果是新会话且没有currentSessionId，则创建新会话
      if (!this.currentSessionId) {
        this.currentSessionId = Date.now();
      }
      this.messages.push(userMessage);
      this.chatHistory.push(userMessage);
      // 立即添加"思考中"的AI消息
      const aiMessage = {
        role: 'assistant',
        content: '',
        thinking: true,
        thinkingTime: null,
        referenceLinks: []
      };
      this.chatHistory.push(aiMessage);
      this.inputText = '';
      this.currentAssistantMessage = '';
      this.currentReferenceLinks = [];

      try {
        this.$nextTick(() => {
          const scrollContainer = this.$refs.scroll.wrap;
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        });
        const response = await fetch(this.askUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer app-PrBm0A57ipg81TJAqdr1ijBF'
          },
          body: JSON.stringify({
            query: userMessage.content,
            usercode: this.$store.state.common.userInfo.employeeNo,
            stream: true
          }),
          signal: this.abortController.signal
        });
        if (!response.ok) {
          throw new Error(`请求失败，状态码: ${response.status}`);
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let done = false;

        while (!done) {
          const { value, done: isDone } = await reader.read();
          done = isDone;
          if (value) {
            const chunk = decoder.decode(value, { stream: !done });
            const lines = chunk.split('\n').filter(line => line.trim() !== '');
            for (const line of lines) {
              if (line.startsWith('data:')) {
                const data = line.slice(5).trim();
                if (data === '[DONE]') {
                  const thinkingTime = (
                    (Date.now() - startTime) /
                    1000
                  ).toFixed(2);
                  const lastMessage = this.chatHistory[
                    this.chatHistory.length - 1
                  ];
                  lastMessage.content = this.currentAssistantMessage;
                  lastMessage.thinking = false;
                  lastMessage.thinkingTime = thinkingTime;
                  lastMessage.referenceLinks = [...this.currentReferenceLinks];

                  this.$forceUpdate();
                  // 自动更新当前会话
                  this.updateCurrentSession(lastMessage);
                } else {
                  try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.error) {
                      const lastMessage = this.chatHistory[
                        this.chatHistory.length - 1
                      ];
                      lastMessage.content = `请求失败: ${jsonData.error}`;
                      lastMessage.thinking = false;
                      lastMessage.role = 'system';
                      this.loading = false;
                      this.$forceUpdate();
                      return; // 提前结束处理
                    }
                    if (jsonData.sources) {
                      const sourcesArray = Array.isArray(jsonData.sources)
                        ? jsonData.sources
                        : [jsonData.sources];
                      this.currentReferenceLinks = sourcesArray.map(source => ({
                        title:
                          typeof source === 'string'
                            ? source.split('\\').pop()
                            : source.filename || '',
                        url:
                          typeof source === 'string'
                            ? source.split(/[\\/]/).pop() // 只取最后的文件名
                            : source.sources
                            ? source.sources.split(/[\\/]/).pop()
                            : '',
                        score: source.score || null,
                        fileId: source.fileId || null,
                        source_type:
                          jsonData.source_type ||
                          source.source_type ||
                          '内部知识库'
                      }));
                      // 立即更新当前消息的参考链接
                      if (this.chatHistory.length > 0) {
                        const lastMsg = this.chatHistory[
                          this.chatHistory.length - 1
                        ];
                        if (lastMsg.role === 'assistant') {
                          lastMsg.referenceLinks = [
                            ...this.currentReferenceLinks
                          ];
                        }
                      }
                    }
                    if (jsonData.choices && jsonData.choices[0]) {
                      const delta = jsonData.choices[0].delta?.content;
                      const messageId = jsonData.id;
                      if (delta) {
                        this.currentAssistantMessage += delta;
                        // 更新现有的AI消息内容
                        const lastMsg = this.chatHistory[
                          this.chatHistory.length - 1
                        ];
                        lastMsg.content = this.currentAssistantMessage;
                        this.$forceUpdate();
                        this.$nextTick(() => {
                          const scrollContainer = this.$refs.scroll.wrap;
                          scrollContainer.scrollTop =
                            scrollContainer.scrollHeight;
                        });
                      }
                    }
                  } catch (parseError) {
                    console.error('解析 JSON 数据出错:', parseError);
                    console.error('原始数据:', data);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        const lastMessage = this.chatHistory[this.chatHistory.length - 1];
        if (error.response) {
          lastMessage.content = '请求失败，请稍后重试';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
          console.error('API 请求失败，状态码:', error.response.status);
          console.error('响应数据:', error.response.data);
        } else if (error.name === 'AbortError') {
          lastMessage.content = '请求已取消';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        } else if (error.request) {
          lastMessage.content = '请求失败，请稍后重试';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        } else {
          // 请求设置时出错
          lastMessage.content = '请求失败，未启用';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        }
      } finally {
        this.loading = false;
      }
    },
    toggleHistoryPanel() {
      this.showHistoryPanel = !this.showHistoryPanel;
      if (this.showHistoryPanel && this.historySessions.length === 0) {
        this.loadHistorySessions();
      }

      this.$nextTick(() => {
        const currentWidth = this.$el.offsetWidth;
        const isMaximized = currentWidth >= Math.floor(window.innerWidth * 0.8);
        this.posX = Math.min(
          this.posX,
          window.innerWidth - this.$el.offsetWidth
        );
        this.posY = Math.min(
          this.posY,
          window.innerHeight - this.$el.offsetHeight
        );
        const isRightEdge = this.posX + currentWidth >= window.innerWidth - 20;
        if (this.showHistoryPanel) {
          // 最大化时不改变窗口大小
          if (isMaximized) {
            this.$el.style.width = `${currentWidth}px`;
          }
          // 右侧边缘时向左扩展
          else if (isRightEdge) {
            const newWidth = Math.min(currentWidth + 200, this.maxWidth);
            this.posX = Math.max(0, window.innerWidth - newWidth - 10);
            if (this.posX + newWidth > window.innerWidth) {
              this.posX = window.innerWidth - newWidth - 10;
            }
            this.$el.style.width = `${newWidth}px`;
          }
          // 正常情况向右扩展
          else {
            const newWidth = Math.min(currentWidth + 200, this.maxWidth);
            this.$el.style.width = `${newWidth}px`;
          }

          // 调整滚动容器边距
          const scrollContainer = this.$refs.scroll?.wrap;
          if (scrollContainer) {
            scrollContainer.style.marginRight = '200px';
          }
        } else {
          // 隐藏面板时恢复宽度
          const newWidth = Math.max(currentWidth - 200, this.minWidth);

          // 最大化时不改变窗口大小
          if (!isMaximized) {
            this.$el.style.width = `${newWidth}px`;
            // 如果窗口靠近右侧边缘，保持右侧位置
            if (isRightEdge) {
              this.posX = window.innerWidth - newWidth - 10;
            }
          }

          // 恢复滚动容器边距
          const scrollContainer = this.$refs.scroll?.wrap;
          if (scrollContainer) {
            scrollContainer.style.marginRight = '0';
          }
        }

        this.$forceUpdate();
      });
    },
    newSession() {
      // 保存当前会话
      if (this.chatHistory.length > 0) {
        this.updateCurrentSession();
      }
      // 创建新会话
      this.currentSessionId = null;
      this.chatHistory = [];
      // 确保滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    loadHistorySessions() {
      // 这里可以从localStorage或API加载历史会话
      const savedSessions = localStorage.getItem('ai_chat_history') || '[]';
      console.log('加载的历史会话数据:', savedSessions); // 添加调试日志
      this.historySessions = JSON.parse(savedSessions).reverse();
    },
    saveCurrentSession() {
      if (this.chatHistory.length > 0) {
        const sessionId = Date.now();
        const session = {
          id: sessionId,
          time: new Date().toISOString(),
          title: this.chatHistory[0].content.substring(0, 10) + '...',
          messages: [...this.chatHistory],
          relatedSessions: [] // 新增：关联会话数组
        };
        // 如果有当前会话，建立关联关系
        if (this.currentSessionId) {
          session.relatedSessions.push(this.currentSessionId);
          if (!this.sessionRelations[this.currentSessionId]) {
            this.sessionRelations[this.currentSessionId] = [];
          }
          this.sessionRelations[this.currentSessionId].push(sessionId);
        }

        let sessions = JSON.parse(
          localStorage.getItem('ai_chat_history') || '[]'
        );
        sessions.push(session);
        localStorage.setItem('ai_chat_history', JSON.stringify(sessions));
        this.currentSessionId = sessionId;
      }
    },
    // 新增方法：更新当前会话
    updateCurrentSession(aiMessage) {
      // 更新聊天记录
      this.messages.push(aiMessage);
      this.chatHistory[this.chatHistory.length - 1] = aiMessage;

      // 获取或创建当前会话
      let sessions = JSON.parse(
        localStorage.getItem('ai_chat_history') || '[]'
      );
      let currentSession = sessions.find(s => s.id === this.currentSessionId);

      if (!currentSession) {
        // 创建新会话
        currentSession = {
          id: this.currentSessionId,
          time: new Date().toISOString(),
          title: this.chatHistory[0].content.substring(0, 10) + '...',
          messages: [...this.chatHistory],
          relatedSessions: []
        };
        sessions.push(currentSession);
      } else {
        // 更新现有会话
        currentSession.messages = [...this.chatHistory];
        currentSession.time = new Date().toISOString(); // 更新最后修改时间
      }

      // 保存到localStorage
      localStorage.setItem('ai_chat_history', JSON.stringify(sessions));
    },
    loadHistorySession(session) {
      this.chatHistory = [...session.messages];
      this.showHistoryPanel = false;
      this.currentSessionId = session.id;
      // 加载关联会话
      this.loadRelatedSessions(session.id);
    },
    loadRelatedSessions(sessionId) {
      if (this.sessionRelations[sessionId]) {
        const savedSessions = JSON.parse(
          localStorage.getItem('ai_chat_history') || '[]'
        );
        this.historySessions = savedSessions
          .filter(s => this.sessionRelations[sessionId].includes(s.id))
          .reverse();
      }
    },
    adjustTextareaHeight() {
      const textarea = this.$refs.textareaRef;
      if (!textarea) return;

      // 1. 重置高度为 auto 以正确计算 scrollHeight
      textarea.style.height = 'auto';
      // 2. 计算新高度（1行 ≈ 24px，可根据字体调整）
      const lineHeight = 24; // 根据实际字体大小调整
      const minRows = 2; // 最小行数
      const maxRows = 20; // 最大行数

      // 3. 计算当前行数
      const rows = Math.floor(textarea.scrollHeight / lineHeight);
      const clampedRows = Math.min(Math.max(rows, minRows), maxRows);

      // 4. 设置新高度（平滑扩展）
      textarea.style.height = `${clampedRows * lineHeight}px`;

      // 5. 确保聊天区域滚动到底部
      this.scrollToBottom();
    },
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.scroll?.wrap) {
          this.$refs.scroll.wrap.scrollTop = this.$refs.scroll.wrap.scrollHeight;
        }
      });
    },
    cancelRequest() {
      if (this.abortController) {
        this.abortController.abort();
      }
    },
    toggleMaximize() {
      if (this.isMaximized) {
        // 还原窗口到右侧
        this.$el.style.width = `${this.originalWidth}px`;
        this.$el.style.height = this.originalHeight;
        this.posX = this.originalX;
        this.posY = this.originalY;
      } else {
        // 保存当前状态
        this.originalWidth = this.$el.offsetWidth;
        this.originalHeight = this.$el.style.height;
        this.originalX = this.posX;
        this.originalY = this.posY;
        // 设置最大化状态
        const maxWidth = Math.floor(window.innerWidth * 0.8);
        // const maxWidth = Math.min(800, window.innerWidth - 40); // 保持800px最大宽度，但不超过窗口宽度
        const centerX = (window.innerWidth - maxWidth) / 2;
        this.$el.style.width = `${maxWidth}px`;
        this.$el.style.height = 'calc(100% - 40px)'; // 留出一些边距
        this.posX = centerX;
        this.posY = 40; // 顶部留20px边距
        // 强制重新计算布局
        this.$nextTick(() => {
          this.$forceUpdate();
          this.scrollToBottom();
        });
      }
      this.isMaximized = !this.isMaximized;
    },
    handlePreview(link) {
      let token = this.$cookies.get('OACookie') || this.$cookies.get('token');
      let filenames = link.url.split('.');
      let fileId = link.fileId || filenames[filenames.length - 2];
      const url = `${location.origin}/ts-document/attachment/downloadFile/${fileId}?fullfilename=${link.url}&token=${token}`;
      window.open(
        `${location.origin}/ts-preview/onlinePreview?url=${Base64.encode(url)}`,
        '_blank'
      );
    },

    handleDownload(link) {
      if (link.url.length) {
        let filenames = link.url.split('.');
        let fileId = link.fileId || filenames[filenames.length - 2];
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href =
          `${location.origin}/ts-document/attachment/downloadFile/` + fileId;
        a.download = link.url;
        a.click();
      }
    },

    // 合同设查
    handleBeforeUpload(file) {
      let nameIndex = file.name.lastIndexOf('.');
      let fileExtension = file.name.slice(nameIndex);
      if (this.accept.indexOf(fileExtension) < 0) {
        this.$message.warning(`请上传${this.accept}类型的文件`);
        return false;
      }
    },
    /**@desc 替换原生上传 */
    async handleUploadFile({ file }) {
      try {
        let data = new FormData();
        data.append('file', file);
        data.append('user', this.$store.state.common.userInfo.employeeNo);
        let res1 = await this.ajax.uploadToPing(data);
        this.sendMessageContract(res1);
      } catch (e) {
        this.$message.error('上传失败，请联系管理员!');
      }
    },
    async sendMessageContract(fileObject) {
      if (this.loading) return;
      this.loading = true;
      this.inputText = `合同合理性审查`;
      const startTime = Date.now(); // 记录开始时间
      this.abortController = new AbortController();
      const userMessage1 = {
        role: 'files',
        ...fileObject
      };
      const userMessage = {
        role: 'user',
        content: this.inputText
      };
      // 如果是新会话且没有currentSessionId，则创建新会话
      if (!this.currentSessionId) {
        this.currentSessionId = Date.now();
      }
      this.messages.push(userMessage1);
      this.chatHistory.push(userMessage1);
      this.messages.push(userMessage);
      this.chatHistory.push(userMessage);
      // 立即添加"思考中"的AI消息
      const aiMessage = {
        role: 'assistant',
        content: '',
        thinking: true,
        thinkingTime: null,
        referenceLinks: []
      };
      this.chatHistory.push(aiMessage);
      this.inputText = '';
      this.currentAssistantMessage = '';
      this.currentReferenceLinks = [];

      try {
        this.$nextTick(() => {
          const scrollContainer = this.$refs.scroll.wrap;
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        });
        const response = await fetch('/v1/workflows/run', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer app-vCBFEshThfpasvpsBnNIXpyM'
          },
          body: JSON.stringify({
            Apart: '乙方',
            attention: '合同合理性',
            'sys.files': [],
            inputs: {
              upload: [
                {
                  transfer_method: 'local_file',
                  upload_file_id: fileObject.id,
                  type: 'document'
                }
              ]
            },
            response_mode: 'streaming',
            user: this.$store.state.common.userInfo.employeeNo
          })
        });
        if (!response.ok) {
          throw new Error(`请求失败，状态码: ${response.status}`);
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let done = false;

        while (!done) {
          const { value, done: isDone } = await reader.read();
          done = isDone;
          if (value) {
            const chunk = decoder.decode(value, { stream: !done });
            const lines = chunk.split('\n').filter(line => line.trim() !== '');
            for (const line of lines) {
              if (line.startsWith('data:')) {
                const data = line.slice(5).trim();
                try {
                  const jsonData = JSON.parse(data);
                  if (jsonData.event == 'text_chunk') {
                    let delta = jsonData.data?.text || '';
                    this.currentAssistantMessage += delta;
                    // 更新现有的AI消息内容
                    const lastMsg = this.chatHistory[
                      this.chatHistory.length - 1
                    ];
                    lastMsg.content = this.currentAssistantMessage;
                    this.$forceUpdate();
                    this.$nextTick(() => {
                      const scrollContainer = this.$refs.scroll.wrap;
                      scrollContainer.scrollTop = scrollContainer.scrollHeight;
                    });
                  }
                  if (jsonData.event == 'workflow_finished') {
                    const thinkingTime = (
                      (Date.now() - startTime) /
                      1000
                    ).toFixed(2);
                    const lastMessage = this.chatHistory[
                      this.chatHistory.length - 1
                    ];
                    lastMessage.content = this.currentAssistantMessage;
                    lastMessage.thinking = false;
                    lastMessage.thinkingTime = thinkingTime;
                    lastMessage.referenceLinks = [
                      ...this.currentReferenceLinks
                    ];
                    this.$forceUpdate();
                    // 自动更新当前会话
                    this.updateCurrentSession(lastMessage);
                  }
                } catch (parseError) {
                  console.error('解析 JSON 数据出错:', parseError);
                  console.error('原始数据:', data);
                }
              }
            }
          }
        }
      } catch (error) {
        const lastMessage = this.chatHistory[this.chatHistory.length - 1];
        if (error.response) {
          lastMessage.content = '请求失败，请稍后重试';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
          console.error('API 请求失败，状态码:', error.response.status);
          console.error('响应数据:', error.response.data);
        } else if (error.name === 'AbortError') {
          lastMessage.content = '请求已取消';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        } else if (error.request) {
          lastMessage.content = '请求失败，请稍后重试';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        } else {
          // 请求设置时出错
          lastMessage.content = '请求失败，未启用';
          lastMessage.thinking = false;
          lastMessage.role = 'system';
        }
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.welcome-container {
  width: 100%;
  padding: 20px 0;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0 auto;
  max-width: 80%;
  .Icon {
    margin-bottom: 16px;
  }
  .welcome-content {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    max-width: 100%;
  }
}
.loading {
  color: #666;
  padding: 8px 8px;
  margin-bottom: 4px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-style: italic;
  i {
    margin-left: 8px;
    font-size: 16px;
  }
}

.thinking-time {
  font-size: 12px;
  color: #888;
  margin-bottom: 8px;
  font-style: italic;
}

.draggable-float-window {
  position: fixed;
  // background: white;
  right: 10px;
  border: 1px solid #ddd;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 430px;
  height: calc(100% - 80px);
  z-index: 9999;
  user-select: none;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', system-ui;
  line-height: 1.6;
  margin: 0;
  background-color: #f8f9fa;
  resize: none; /* 禁用默认resize */
  min-width: 430px; /* 最小宽度 */
  max-width: 80%; /* 最大宽度 */
  transition: margin-left 0.3s ease;
  font-family: 'Segoe UI', system-ui;
  font-size: 16px;
  line-height: 1.6;
  * {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }
  &.history-open {
    margin-left: 0;
    width: calc(100% + 200px);
  }
}

.float-header {
  padding: 8px 12px;
  background: #2c7be5;
  border-bottom: 1px solid #eee;
  cursor: move;
  color: white;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  border-radius: 6px 6px 0 0;
  i {
    cursor: pointer;
    font-size: 16px;
    margin-top: 4px;
    margin-right: 15px;
  }
  p {
    font-size: inherit; /* 确保p标签继承父级字体大小 */
    margin: 0; /* 移除默认margin */
  }
}

.float-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
  border-radius: 14px;
  // > * {
  //   flex-shrink: 0;
  // }
}
.chat-area {
  flex: 1;
  display: flex; /* 消息列表和历史面板使用flex布局 */
  overflow: hidden;
  flex-direction: row;
  min-height: 0;
}
/* 拖动时的视觉反馈 */
.draggable-float-window:active .float-header {
  opacity: 0.7;
  cursor: grabbing;
}
.chat-container {
  max-width: 1000px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 8px;
  // background-color: #e3f2fd40;
  border-radius: 20px;
  // display: flex;
  // flex-direction: column;
  width: 100%; // 确保宽度填满
  max-width: 100%; // 防止溢出
  min-height: 0;
  order: 2;
}

.user-message {
  // margin-left: 50px;
  // flex-direction: row-reverse;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  .files-content {
    background-color: #eee;
    padding: 8px;
    margin-right: 40px;
    margin-left: 50px;
    user-select: text;
    display: flex;
    align-items: center;
    max-width: 80%;
    img {
      flex: 1;
      width: 44px;
      height: 44px;
      margin-right: 8px;
    }
    .file-name {
      width: 140px;
      p {
        text-align: left;
        line-height: 25px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        span {
          &:not(:last-child) {
            margin-right: 4px;
          }
        }
      }
    }
  }
}

.assistant-message {
  margin-right: 30px;
  display: flex;
  justify-content: flex-start;
  width: 100%; // 确保宽度填满
  max-width: 100%; // 防止溢出
  .content {
    max-width: calc(100% - 60px); // 新增：限制内容最大宽度
    user-select: text;
  }
}
.Icon {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 18px;
  border: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 1px;
  p {
    line-height: 36px;
  }
  .thinking-loading {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    i {
      font-size: 12px;
      color: #409eff;
      margin: 0;
    }
  }
}
.assistant-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin: 0 10px;
  background-repeat: no-repeat;
}
.assistant-icons {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin: 5px 0px;
  background-repeat: no-repeat;
}
.content {
  padding: 12px;
  border-radius: 8px;
  // background-color: #e3f2fd;
  .tips {
    margin-top: 8px;
    color: #efb5a0;
    background-color: #fef8f5;
    border-radius: 3px;
    width: 320px;
    padding: 2px 4px;
    border: 1px solid #efb5a0;
  }
}

.user-message .content {
  background-color: #e3f2fd;
  margin-right: 40px;
  margin-left: 50px;
  user-select: text;
}
.message {
  margin: 10px 0;
  width: 100%;
  display: flex;
  align-items: flex-end;
  box-sizing: border-box;
  // justify-self: flex-end;
}
// .assistant-message .content {
//   // background-color: #f5f5f5;
//   user-select: text;
// }
.reasoning {
  background-color: #f5f8ff;
  border-left: 4px solid #6e8efb;
  padding: 14px 16px;
  margin: 16px 0;
  border-radius: 0 8px 8px 0;
  font-size: 14px;
  color: #555;
  line-height: 1.6;
  user-select: text;
  * {
    font-family: inherit !important;
    line-height: inherit !important;
  }
}

.reply {
  margin-top: 10px;
  font-family: 'Segoe UI', system-ui;
  font-size: 16px;
  line-height: 1.6;
  ::v-deep {
    font-size: 16px !important;
    // 统一标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 1em 0 0.5em;
      font-weight: 600 !important;
      color: #333;
    }
    h3 {
      font-size: 1.2em;
      border-bottom: 1px solid #eee;
      padding-bottom: 0.3em;
    }
    // 列表样式
    ul,
    ol {
      margin: 0.8em 0;
      padding-left: 2em;
    }

    li {
      margin: 0.4em 0;
      position: relative;

      &::before {
        content: '•';
        color: #6e8efb;
        position: absolute;
        left: -1em;
      }
    }
    // 代码块样式
    pre {
      background: #f5f7fa;
      border-radius: 6px;
      padding: 1em;
      overflow-x: auto;
      margin: 1em 0;
      max-width: 100%;
      white-space: pre-wrap;
      word-wrap: break-word;
      box-sizing: border-box;
    }
    // 链接样式
    a {
      color: #2c7be5;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    // 表格样式
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
      th,
      td {
        padding: 0.6em 1em;
        border: 1px solid #ddd;
      }

      th {
        background: #f5f7fa;
      }
    }
  }
  * {
    font-family: inherit !important;
    line-height: inherit !important;
  }
}

.input-box {
  padding: 12px;
  margin-top: 8px;
  display: flex;
  background-color: #f3f4f6;
  border-radius: 16px;
  flex-direction: column;
  min-height: 60px;
  flex-shrink: 0;
  .bottom {
    display: flex;
    justify-content: flex-end;
    .sendMessage {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background: #265cf9;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      i {
        // margin-top: 5px;
        // margin-left: 4px;
        margin: 0;
        font-size: 20px;
        color: #fff;
      }
      &.disabled {
        background-color: #dfe5ed;
        cursor: not-allowed;
        i {
          color: #2c7be5;
        }
      }
    }
  }
}

textarea {
  min-height: 48px;
  max-height: 480px;
  line-height: 24px;
  // border: none;
  background-color: #f3f4f6;
  width: 100%;
  resize: none;
  // flex: 1;
  overflow-y: auto !important;
  box-sizing: border-box;
  border: 1px solid transparent;
  &:focus {
    outline: 0px;
  }
}

button {
  padding: 8px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  color: #666;
  text-align: center;
  padding: 10px;
}
/* 确保el-scrollbar正确填充空间 */
.el-scrollbar {
  flex: 1;
  min-height: 0;
  // height: 100%;
  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
    max-height: 100%; /* 确保不会无限扩展 */
  }
}
.resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  z-index: 10;
  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
  &.left {
    left: 0;
  }
  &.right {
    right: 0;
  }
}
.history-icon {
  margin-right: 12px;
  font-size: 16px;
  cursor: pointer;
  &:hover {
    color: #409eff;
  }
}
.history-panel {
  width: 240px;
  border-right: 1px solid #e0e0e0;
  border-left: none;
  background: #f5f7fa;
  order: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  .history-header {
    padding: 8px;
    // background: #e0e0e0;
    color: #000;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    span {
      font-weight: 600;
      letter-spacing: 0.5px;
    }
    i {
      cursor: pointer;
      font-size: 14px;
      transition: transform 0.2s;
      &:hover {
        transform: scale(1.1);
      }
    }
  }
  .history-scroll {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    .history-date {
      color: #666;
      font-size: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }
    .history-item {
      padding: 10px;
      margin: 6px 0;
      border-radius: 4px;
      background: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      &:hover {
        background: #e6f7ff;
      }
      .history-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
      }
      .history-time {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
.history-icon,
.el-icon-close {
  position: relative;

  &::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
  }

  &:hover::after {
    opacity: 1;
  }
}
.reference-links {
  margin-top: 10px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;

  .link-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
  }

  .link-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    padding: 4px 0;

    .link-icon {
      margin-right: 6px;
      color: #909399;
    }

    .link-text {
      color: #409eff;
      margin-right: 8px;
    }

    .link-type {
      color: #67c23a;
      margin-right: 8px;
    }

    .link-score {
      color: #e6a23c;
    }
  }
}
.link-action {
  margin-left: 8px;
  font-size: 14px; // 调整图标大小
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #409eff;
    transform: scale(1.2); // 悬停时放大效果
  }

  &.preview:hover::after {
    content: '预览';
    margin-left: 5px;
    font-size: 12px;
  }

  &.download:hover::after {
    content: '下载';
    margin-left: 5px;
    font-size: 12px;
  }
}
.uploadButton {
  background-color: #d2dcfc !important;
  padding: 0 10px;
  color: #295cf9 !important;
  margin-right: 8px;
  height: 25px;
  span {
    line-height: 25px;
  }
}
</style>
