<template>
  <el-dialog
    custom-class="ts-user-dept-select"
    :title="title"
    width="900px"
    :show-close="false"
    :visible.sync="visible"
    :append-to-body="appendToBody"
    @close="cancel"
  >
    <div class="container">
      <div class="tree-container">
        <div class="search-container">
          <el-input placeholder="输入部门名称" v-model="filterText"> </el-input>
        </div>
        <el-scrollbar style="flex-grow: 1; width: 100%">
          <el-tree
            ref="deptTree"
            :data="deptTreeList.data"
            :show-checkbox="showCheckbox"
            node-key="id"
            :default-expanded-keys="deptTreeList.defaultExpandedKeys"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @check-change="handleCheckChange"
            @node-click="nodeClick"
            class="dept-tree"
          >
          </el-tree>
          <!-- systemList -->
          <el-tree
            v-if="systemList.data && systemList.data.length > 0"
            :data="systemList.data"
            :show-checkbox="showCheckbox"
            node-key="id"
            :default-expanded-keys="systemList.defaultExpandedKeys"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="groupNodeClick"
            class="dept-tree"
          >
          </el-tree>
          <!-- personalList -->
          <el-tree
            v-if="systemList.data && systemList.data.length > 0"
            :data="personalList.data"
            :show-checkbox="showCheckbox"
            node-key="id"
            :default-expanded-keys="personalList.defaultExpandedKeys"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="groupNodeClick"
            class="dept-tree"
          >
          </el-tree>
        </el-scrollbar>
      </div>
      <div class="table-container">
        <div class="search-container">
          <el-input
            placeholder="输入姓名"
            v-model="queryParam.empName"
            suffix-icon="el-icon-search"
            clearable
            @input="loadData(1)"
          >
          </el-input>
        </div>
        <el-table
          ref="multipleTable"
          :data="dataSource"
          style="width: 100%"
          height="100%"
          :show-overflow-tooltip="true"
          :stripe="true"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName"
          :row-key="rowKey"
        >
          <el-table-column
            v-if="!isRadio"
            :show-overflow-tooltip="true"
            width="36"
            type="selection"
            :reserve-selection="true"
            prop="empCode"
          ></el-table-column>

          <el-table-column
            :show-overflow-tooltip="true"
            width="60"
            label="工号"
            prop="empCode"
          >
            <template slot-scope="{ row }">
              <span class="text-style">{{ row.empCode }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="姓名"
            :show-overflow-tooltip="true"
            width="75"
            prop="empName"
          >
            <template slot-scope="{ row }">
              <span class="text-style">{{ row.empName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="所属部门"
            :show-overflow-tooltip="true"
            prop="empDeptName"
          >
            <template slot-scope="{ row }">
              <span class="text-style">{{ row.empDeptName }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="ipagination.pageNo"
          :page-sizes="ipagination.pageSizeOptions"
          :page-size="ipagination.pageSize"
          layout="total, prev, next, jumper"
          :total="ipagination.total"
        >
        </el-pagination>
      </div>
      <div class="sel-container">
        <div class="staff-container">
          <div class="title">
            已选人员({{ selectedRowKeys.length || 0 }})
            <a @click="clearSelect('selectedRowKeys')">清空</a>
          </div>
          <div class="content">
            {{ selectedRowKeysStr }}
          </div>
        </div>
        <div class="dept-container">
          <div class="title">
            已选组织({{ treeCheck.nodes.length || 0 }})
            <a @click="clearDeptSelect('selectedRowKeys')">清空</a>
          </div>
          <div class="content">
            {{ selectTreeNames }}
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="footer">
      <span class="ts-button primary" @click="save">
        确 定
      </span>
      <span class="ts-button" @click="cancel">
        取 消
      </span>
    </span>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/hrm.js';
import { commonUtils } from '@/utils/index.js';
import _ from 'lodash';
const folderIcon = require('@/assets/img/icon_folder.png');
const fileIcon = require('@/assets/img/icon_file.svg');
const queryParam = {
  empCode: undefined,
  empName: undefined,
  empDeptCode: undefined,
  deptCodeSeach: undefined,
  groupId: undefined
};
const ipagination = {
  current: 1,
  pageSize: 100,
  pageSizeOptions: [100, 200, 500, 1000, 2000],
  total: 0
};
export default {
  name: 'TsUserDeptSelect',
  data() {
    return {
      ajax: api,
      field: '',
      visible: false,
      folderIcon,
      fileIcon,
      filterText: '',
      title: '选择',
      showCheckbox: true,
      appendToBody: false,
      isRadio: false,
      tableRowCode: '',
      deptTreeList: {
        data: [],
        defaultExpandedKeys: []
      },
      systemList: {
        data: [],
        defaultExpandedKeys: []
      },
      personalList: {
        data: [],
        defaultExpandedKeys: []
      },
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: JSON.parse(JSON.stringify(queryParam)),
      /* 数据源 */
      dataSource: [],
      /* 分页参数 */
      ipagination: JSON.parse(JSON.stringify(ipagination)),
      selectedRowKeys: [], // 选中人员列表
      treeType: 'emp', // 当前选中的树类型
      treeCheck: {
        nodes: [],
        keys: []
      }
    };
  },
  methods: {
    /**@desc **/
    /**
     * showCheckbox 组织结构是否可以勾选
     * title 标题
     * empList 选中人员
     * deptList 选中组织架构
     * isRadio 单选
     */
    open(
      field,
      {
        showCheckbox = true,
        title = '选择',
        empList = [],
        deptList = [],
        appendToBody = false,
        isRadio = false
      }
    ) {
      this.field = field;
      this.showCheckbox = showCheckbox;
      this.title = title;
      this.appendToBody = appendToBody;
      this.isRadio = isRadio;
      this.visible = true;
      this.$nextTick(() => {
        let treeList = [];
        // 回显组织架构勾选
        deptList.forEach(row => {
          treeList.push(row.id);
        });
        this.$refs.deptTree.setCheckedKeys(treeList);
        if (isRadio) {
          this.tableRowCode = empList.length === 1 ? empList[0].empCode : '';
          this.selectedRowKeys = empList;
        } else {
          // 回显人员勾选
          empList.forEach(row => {
            this.$refs.multipleTable.toggleRowSelection(row);
          });
        }
      });
      Promise.all([this.getTreeList(), this.loadData(1)]).then(res => {});
    },
    renderContent(h, { node, data, store }) {
      return (
        <div class="custom-tree-node">
          <img
            class="icon-file"
            src={data.children ? this.folderIcon : this.fileIcon}
          />
          <span>{data.name}</span>
        </div>
      );
    },
    // 获取组织部门列表
    async getTreeList() {
      try {
        let deptTreeList = await this.ajax.getDeptTreeList();
        deptTreeList = deptTreeList.object || [];
        let systemList = await this.ajax.getOrgGroupTree({ groupType: 0 });
        systemList = systemList.object || [];
        this.recursionData(systemList);
        let personalList = await this.ajax.getOrgGroupTree({ groupType: 1 });
        personalList = personalList.object || [];
        this.recursionData(personalList);
        if (deptTreeList.length > 0)
          this.deptTreeList = {
            data: deptTreeList,
            defaultExpandedKeys: [deptTreeList[0].id]
          };
        if (systemList.length > 0)
          this.systemList = {
            data: systemList,
            defaultExpandedKeys: []
          };
        if (personalList.length > 0)
          this.personalList = {
            data: personalList,
            defaultExpandedKeys: []
          };
      } catch (error) {
        throw error;
      }
    },
    // 遍历数据源
    recursionData(list) {
      for (const key in list) {
        list[key].disabled = true;
        if (commonUtils.arrayLength(list[key].children)) {
          this.recursionData(list[key].children);
        }
      }
    },
    // 树搜索
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleCheckChange(data, checked, indeterminate) {
      this.treeCheck.nodes = this.$refs.deptTree.getCheckedNodes();
      this.treeCheck.keys = this.$refs.deptTree.getCheckedKeys();
    },
    // 点击部门事件
    nodeClick(data, checked, indeterminate) {
      this.treeType = 'emp';
      if (this.queryParam.deptCodeSeach !== data.id) {
        this.queryParam.deptCodeSeach = data.id;
        this.loadData(1);
      }
    },
    // 清空组织
    clearDeptSelect() {
      this.$refs.deptTree.setCheckedKeys([]);
    },
    groupNodeClick(data, checked, indeterminate) {
      this.treeType = 'group';
      if (data.dataType === 'group') {
        this.queryParam.groupId = data.id;
      } else {
        this.queryParam.groupId = undefined;
      }
      this.loadData(1, this.treeType);
    },
    // 部门人员查询
    async loadData(arg) {
      let type = arguments[1] || 'emp';
      if (arg === 1) {
        this.ipagination.pageNo = 1;
      }
      const params = this.getQueryParams(); // 查询条件
      const dataParams = this.getDataParams();
      try {
        let res;
        if (type === 'emp') {
          res = await this.ajax.getEmployeeList(params, dataParams);
        } else {
          res = await this.ajax.getOrgGroupUser(dataParams);
        }
        this.dataSource = res.rows || [];
        this.ipagination.total = res.totalCount;
      } catch (error) {
        throw error;
      } finally {
      }
    },

    getQueryParams() {
      // 获取查询条件
      // var param = Object.assign(this.queryParam);
      return { userSel: true };
    },
    getDataParams() {
      // 获取查询条件
      var data = Object.assign(this.defaultSort, this.queryParam);
      data.pageNo = this.ipagination.pageNo;
      data.pageSize = this.ipagination.pageSize;
      return commonUtils.filterObj(data);
    },
    handleSizeChange(val) {},
    handleCurrentChange(val) {
      this.ipagination.pageNo = val;
      this.loadData();
    },
    handleSelectionChange(val) {
      this.selectedRowKeys = val;
    },
    handleRowClick(row, column, event) {
      if (!this.isRadio) return;
      this.tableRowCode = row.empCode;
      this.selectedRowKeys = [row];
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.empCode === this.tableRowCode) {
        return 'highlight-row';
      }
      return '';
    },

    rowKey(row) {
      return row.empCode;
    },
    // 清空选中项
    clearSelect(type) {
      if (type === 'selectedRowKeys') {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 关闭模态窗
    cancel() {
      this.visible = false;
      this.field = '';
      this.showCheckbox = true;
      this.title = '选择';
      this.appendToBody = false;
      this.$refs.deptTree.setCheckedKeys([]);
      this.$refs.multipleTable.clearSelection();
      this.queryParam = JSON.parse(JSON.stringify(queryParam));
      this.ipagination = JSON.parse(JSON.stringify(ipagination));
      this.filterText = '';
      this.treeType = 'emp'; // 当前选中的树类型
      this.treeCheck = {
        nodes: [],
        keys: []
      };
    },
    // 保存
    save() {
      this.$emit('ok', {
        [this.field]: {
          empList: this.selectedRowKeys,
          deptList: this.treeCheck.nodes
        }
      });
      this.cancel();
    }
  },
  computed: {
    selectedRowKeysStr() {
      return this.selectedRowKeys
        .map(item => {
          return item.empName;
        })
        .join(', ');
    },
    selectTreeNames() {
      return this.treeCheck.nodes
        .map(item => {
          return item.name;
        })
        .join(', ');
    },
    /* 排序参数 */
    defaultSort() {
      let sortDatas = this.$store.state.common?.personalSortData ?? {},
        { sidx = 'create_date', sord = 'desc' } = sortDatas;
      return { sord, sidx };
    }
  },
  watch: {
    filterText(val) {
      this.$refs.deptTree.filter(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-user-dept-select {
  /deep/.el-dialog__body {
    padding: 8px 8px;
  }
  .container {
    height: 475px;
    display: flex;
    justify-content: space-between;
    & > div {
      height: 100%;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
      border: 1px solid #e4e4e4;
    }
    .tree-container,
    .table-container,
    .sel-container {
      box-sizing: border-box;
    }
    .tree-container {
      width: 200px;
      display: flex;
      flex-direction: column;
      .search-container {
        padding: 8px;
      }
    }
    .table-container {
      width: 300px;
      display: flex;
      flex-direction: column;
      /deep/.el-loading-spinner {
        margin-top: 0;
      }
      .search-container {
        padding: 8px;
      }
      .el-pagination {
        display: flex;
        justify-content: flex-end;
      }
      .text-style {
        font-size: 12px;
        color: #333333;
      }
    }
    .sel-container {
      width: 352px;
      display: flex;
      flex-direction: column;
      .staff-container {
        width: 100%;
        height: 290px;
      }
      .dept-container {
        height: calc(100% - 290px);
      }
      .title {
        position: relative;
        width: 100%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background: #fafafa;
        font-size: 12px;
        font-weight: bold;
        color: #333333;
        & > a {
          cursor: pointer;
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
          font-weight: 400;
          color: #333333;
        }
      }
      .content {
        height: calc(100% - 30px);
        width: 100%;
        flex-grow: 1;
        padding: 8px 7px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(51, 51, 51, 0.7);
        line-height: 17px;
        overflow-y: auto;
        box-sizing: border-box;
      }
    }
    /deep/::-webkit-scrollbar {
      width: 6px;
      height: 8px;
    }
    /deep/::-webkit-scrollbar-thumb {
      border-radius: 8px;
      height: 50px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }
    /deep/::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  .footer {
    .primary {
      margin-right: 8px;
    }
  }
}
/deep/ {
  .dept-tree {
    .el-tree-node__content:hover .custom-tree-node span {
      color: $theme-color;
    }
    .is-current {
      & > .el-tree-node__content {
        background-color: rgba($theme-color, 0.1) !important;
      }
    }
  }
}
</style>
<style lang="scss">
.ts-user-dept-select .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}
.ts-user-dept-select .custom-tree-node span {
  font-size: 12px;
  color: #333333;
  line-height: 16px;
}
.ts-user-dept-select .custom-tree-node .icon-file {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.ts-user-dept-select .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-dept-select .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $theme-color;
  border-color: rgba(151, 151, 151, 1);
}
.ts-user-dept-select .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
  white-space: nowrap;
  display: inline-block;
}
.ts-user-dept-select .el-table .el-table__header-wrapper tr,
.ts-user-dept-select .el-table .el-table__header-wrapper th {
  background-color: white;
}
.ts-user-dept-select .el-table .el-table__header-wrapper tr:hover,
.ts-user-dept-select .el-table .el-table__header-wrapper th:hover {
  background-color: white;
}
tr.highlight-row td {
  background-color: $theme-color !important;
}
tr.highlight-row td span {
  color: white !important;
}
</style>
