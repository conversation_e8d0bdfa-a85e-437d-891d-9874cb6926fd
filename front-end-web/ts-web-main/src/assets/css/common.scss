* {
  margin: 0;
  padding: 0;
  font: 14px/1.4 'Microsoft Yahei', Arial, Helvetica, sans-serif;
  // font: 'Microsoft Yahei', Arial, Helvetica, sans-serif;
  list-style-type: none;
}
html,
body {
  width: 100%;
  height: 100%;
}
// flex设
.flex {
  display: flex;
}
.flex-grow {
  flex: 1;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-row-center {
  display: flex;
  justify-content: center;
}
.flex-row-between {
  display: flex;
  justify-content: space-between;
}
.flex-col-center {
  display: flex;
  align-items: center;
}
.flex-row-evenly {
  display: flex;
  justify-content: space-evenly;
}
//1行文本信息
.firstText {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

//2行文本信息
.twoText {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.el-scrollbar__wrap {
  overflow-x: hidden;
}
//el统一样式修改
.el-dialog {
  border-radius: 4px;
  .el-dialog__header {
    border-bottom: 1px solid rgba(228, 228, 228, 1);
  }
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 8px 16px;
  }
  .el-dialog__headerbtn {
    top: 8px;
    right: 16px;
  }
}

.el-table--border {
  border-top: none;
}
.el-table .el-table__header-wrapper tr,
.el-table .el-table__header-wrapper th {
  color: #333;
  font-weight: 600;
  background-color: #d2def0;
  height: 30px;
  padding: 0;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f9f9f9;
}
.el-table__row td {
  padding: 0;
  .cell {
    line-height: 30px;
  }
}
.el-table .el-table__header-wrapper th:hover {
  background-color: #dee9f8;
}
.el-table .el-table__header-wrapper .cell {
  font-weight: 600;
  color: #333;
}

.el-table .el-table__header th {
  text-align: center;
  * {
    text-align: center;
  }
}

.el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
}
.ts-button {
  display: inline-block;
  min-width: 60px;
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  text-align: center;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  border: 1px solid $theme-color;
  color: $theme-color;
  cursor: pointer;
}
.ts-button.primary {
  padding: 0 6px;
  background: $theme-color;
  color: #ffffff;
}
//el-tab
.qiankun-layout-container .el-tabs .el-tabs__header {
  margin-bottom: 8px;
}
//el-tree
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: rgba($theme-color, 0.1) !important;
}

//el-date-picker
.el-range-editor {
  width: 206px;
}
.el-range-editor--small.el-input__inner {
  height: 30px;
}
.el-range-editor.el-input__inner {
  padding: 3px 8px !important;
}

.tox-tinymce-aux {
  z-index: 5000 !important;
}
//this.$confirm 按钮样式
.el-message-box__wrapper {
  .el-message-box__btns {
    padding-bottom: 10px;
    direction: rtl;
    .el-button {
      border-radius: 2px;
      line-height: 28px;
      min-width: 60px;
      padding: 0 8px;
      height: 30px;
      font-size: 14px;
    }
    button:nth-child(2) {
      margin-right: 8px;
      margin-left: 0;
    }
    .el-button--primary {
      background-color: $theme-color;
      color: #fff;
      border-color: $theme-color;
    }
  }
}
//el-tab样式修改
.el-tabs:not(.ts-tabs) {
  .el-tabs--card > .el-tabs__header .el-tabs__item,
  .el-tabs--card > .el-tabs__header .el-tabs__nav,
  .el-tabs--card > .el-tabs__header {
    border: none;
  }
  .el-tabs__nav,
  .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border-radius: 4px;
    background-color: #e8ecf2;
    height: 40px;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
    border-left: 2px solid #e8ecf2;
  }
  .el-tabs__item {
    line-height: 40px;
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    padding: 0 5px !important;
    background-color: #e8ecf2;
    border: 2px solid #e8ecf2;
    color: #333;
    border-radius: 4px;
    &.is-active {
      line-height: 36px;
      border: 2px solid #e8ecf2;
      background-color: #fff;
      color: $theme-color;
    }
  }
  .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    line-height: 36px;
    color: $theme-color;
    border: 2px solid #e8ecf2;
    background-color: #fff;
  }
}

.tree-select-box .ztree [treenode_ico] {
  display: none;
}
.tree-select-box .el-scrollbar .el-scrollbar__wrap {
  height: calc(100% + 17px);
  overflow: scroll;
}

.tree-select-box .ztree .curSelectedNode .node_name {
  background-color: rgba(82, 96, 255, 0.15);
}

// base-table组件底部滚动条加粗 改变颜色
.table-content {
  .el-scrollbar__bar {
    &.is-horizontal {
      height: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover {
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
    &.is-vertical {
      width: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover {
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
  }
}
.noBottom.el-select-dropdown .el-select-dropdown__wrap {
  margin-bottom: 0px !important;
}
