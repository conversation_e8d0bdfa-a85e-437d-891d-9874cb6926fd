import { loadMicroApp } from 'qiankun';
import tsFormItem from '@/extends/ts-form-item/index.js';
import component from '@/components/index.js';
import routerChange from '@/unit/routeChange';
import { deepClone } from '@/utils/deepClone.js';

export default {
  data() {
    return {
      qiankunApp: []
    };
  },
  methods: {
    /**@desc 子应用调用父应用的方法
     * @param {Object} obj**/
    devopParentTypeFun() {
      let objType = Object.prototype.toString.call(arguments[0]);
      if (objType == '[object String]') {
        objType ? this.$root.$emit(arguments[0]) : null;
      } else if (objType == '[object Object]') {
        //提供一些基本事件 暂时空置
        if (arguments[0].type == 'changePath') {
          this.$router.push(arguments[0].data);
        } else {
          //自定义事件 通过type传值
          if (
            !arguments[0].type ||
            Object.prototype.toString.call(arguments[0].type) !=
              '[object String]'
          ) {
            return;
          }
          this.$root.$emit(arguments[0].type, arguments[0]);
        }
      } else if (objType == '[object Boolean]') {
        this.$set(this.$store.state.common, 'isFullScreen', arguments[0]);
        const largeScreenBody = document.getElementsByClassName(
          'large-screen-body'
        )[0];

        if (!largeScreenBody) {
          return;
        }

        if (arguments[0]) {
          this.$set(this.$store.state.common, 'sideBarWidth', 0);
          largeScreenBody.style.height = '100vh';
        } else {
          this.$set(this.$store.state.common, 'sideBarWidth', 160);
          largeScreenBody.style.height = '100%';
        }
      }
    },
    goToLogin() {
      let keys = this.$cookies.keys();
      keys.forEach(item => {
        // 不清除 记住密码、配置
        let filterKey = ['remember', 'rememberUsercode', 'rememberPassword'];
        if (!filterKey.includes(item)) this.$cookies.remove(item);
      });
      this.$root.$emit('goLogin');
    },
    /**@desc 子应用获取父应用的消息**/
    getUserInfo() {
      let userInfo = {
        ...global.$userInfo,
        ...this.$store.state.common.userInfo
      };
      return JSON.parse(JSON.stringify(userInfo));
    },
    /**@desc 子应用获取父应用的消息**/
    getCookiesInfo(field) {
      let info = this.$cookies.get(field);
      return JSON.parse(JSON.stringify(info));
    },
    /**@desc 子应用获取父应用的当前激活下转菜单
     * @param {String} url**/
    getChildMenuList(url) {
      let child = [];
      this.$store.state.common.menuList.forEach(item => {
        if (item.alink == url) {
          child = item.children;
        }
      });
      return JSON.parse(JSON.stringify(child));
    },
    /**@desc 子应用获取父应用 信息 */
    getParentStoreInfo(key) {
      let info = null;
      if (key) {
        info = deepClone(this.$store.state.common[key]);
      } else {
        info = deepClone(this.$store.state.common);
      }
      return info;
    },
    /**@desc 获取菜单资源 */
    async getMenuSource() {
      let activeMenu = this.$store.state.common.menuLineList.filter(
        item => item.alink == this.$route.path
      )[0];
      if (!activeMenu) {
        return [];
      }

      let res = await this.ajax.getUserMenuSourceData(activeMenu.id);
      if (res.success == false) {
        return [];
      }

      return res;
    },

    /**@desc 初始化乾坤**/
    initQiankun() {
      let menuList = [].concat(window.qiankuanappsLogin);
      const qiankunApp = menuList.map(item => {
        let app = loadMicroApp(
          {
            container: document.getElementById(item.userData.packageName), //容器节点
            name: item.userData.packageName, //包名
            entry: item.alink,
            activeRule: this.$router.options.base, //激活路由
            props: {
              config: this.$config,
              data: {
                token: this.$store.state.common.token,
                activeRule: `${this.$store.state.common.basePath}${item.alink}`, //激活路由
                hospitalCode: this.$store.state.common.globalSetting.orgCode,
                systemCustomCode: this.$store.state.common.systemCustomCode, //客户定制化参数
                personalSortData: this.$store.state.common.personalSortData, //人员排序参数
                platform:
                  this.$store.state.common.globalSetting.mobilePlatform == 2
                    ? '钉钉'
                    : '' // 移动端平台
              },
              fn: {
                devopParentTypeFun: this.devopParentTypeFun,
                getChildMenuList: () => {},
                getUserInfo: this.getUserInfo,
                jumpOpenDialog: ({ path, type, isWorkOrder }) => {
                  // 我的工单
                  if (isWorkOrder) {
                    this.$routerChange({ path, params: { type } });
                    // this.$router.push(path);
                    window.dispatchEvent(
                      new CustomEvent('jumpOpenDialog', {
                        detail: {
                          path,
                          type
                        }
                      })
                    );
                  } else {
                    //知识库
                    this.$router.push({
                      path,
                      query: {
                        type
                      }
                    });
                  }
                },
                getCookiesInfo: this.getCookiesInfo,
                routerChange: routerChange.bind(this),
                getParentStoreInfo: this.getParentStoreInfo,
                getMenuSource: this.getMenuSource,
                goToLogin: this.goToLogin
              },
              components: tsFormItem
            }
          },
          {
            sandbox: {
              strictStyleIsolation: false, //严格模式
              experimentalStyleIsolation: true
            }, // 开启严格的样式隔离模式
            prefetch: true // 开启预加载关掉
          }
        );
        return app;
      });
      this.qiankunApp = qiankunApp;
    }
  },
  computed: {
    menuList() {
      return this.$store.state.common.menuList;
    }
  },
  created() {},
  mounted() {
    this.initQiankun();
  },
  beforeDestroy() {
    this.qiankunApp.map(item => {
      item.unmount();
    });
  }
};
