<template>
  <div>
    <el-popover
      placement="top-start"
      width="240"
      trigger="click"
      popper-class="workSheetWaitingListPopper"
    >
      <div
        id="workSheetWaitingListBox"
        class="waiting-list"
        style="display:block; cursor: pointer;"
        slot="reference"
      >
        <img ref="waitingBg" class="waiting-bg" :src="bgImage" />
        <div class="waiting-title">等候列表</div>
        <div class="waiting-info">
          <div>正在等候的人数</div>
          <div class="waiting-num">等候{{ workSheetWaitingList.length }}人</div>
        </div>
      </div>
      <div class="waitinglistContent">
        <el-scrollbar style="height: 100%; overflow-x: hidden;">
          <template v-for="(item, index) of workSheetWaitingList">
            <div :key="index">
              <div
                class="title"
                :title="
                  (item.visitUserDeptName ? item.visitUserDeptName + '-' : '') +
                    (item.visitUserName || '未知来电')
                "
              >
                {{
                  (item.visitUserDeptName ? item.visitUserDeptName + '-' : '') +
                    (item.visitUserName || '未知来电')
                }}
              </div>
              <div><span class="title">电话：</span>{{ item.visitPhone }}</div>
              <div>
                <span class="title">来电时间：</span>
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
              </div>
              <div
                name="order_waiting_list_item_visitTime"
                :time="new Date(item.visitTime).getTime()"
              >
                <span class="title">等待时长：</span>
                {{ computedWatingTime(item.visitTime) }}
              </div>
            </div>
          </template>
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  props: {
    workSheetWaitingList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      dayjs: dayjs,
      workSheetWaitingTimer: null,
      workSheetIsBoxMoving: false,
      bgImage: require('@/assets/img/workSheetWebSocket/order_waiting_list.png')
    };
  },
  mounted() {
    this.$refs.waitingBg.onmousedown = e => {
      return false;
    };

    this.bindLimitDrag(document.getElementById('workSheetWaitingListBox'));
    this.addWatingInterval();
  },
  methods: {
    //绑定鼠标拖拽事件
    bindLimitDrag(node) {
      if (!node) {
        setTimeout(() => {
          this.bindLimitDrag(
            document.getElementById('workSheetWaitingListBox')
          );
        });
        return;
      }

      let moveFunc = null;
      node.onmousedown = function(ev) {
        var e = ev || window.event;
        //记录鼠标和被拖拽物体相对位置
        var offsetX = e.clientX - node.offsetLeft;
        var offsetY = e.clientY - node.offsetTop;
        moveFunc = function(ev) {
          if (node.id == 'workSheetWaitingListBox') {
            this.workSheetIsBoxMoving = true;
          }
          window.getSelection
            ? window.getSelection().removeAllRanges()
            : document.selection.empty();
          var e = ev || window.event;
          var l = e.clientX - offsetX;
          var t = e.clientY - offsetY;
          //限制出界
          if (l <= 0) {
            l = 0;
          }
          var windowWidth =
            document.documentElement.clientWidth || document.body.clientWidth;
          if (l >= windowWidth - node.offsetWidth) {
            l = windowWidth - node.offsetWidth;
          }
          if (t <= 0) {
            t = 0;
          }
          var windowHeight =
            document.documentElement.clientHeight || document.body.clientHeight;
          if (t >= windowHeight - node.offsetHeight) {
            t = windowHeight - node.offsetHeight;
          }
          node.style.left = l + 'px';
          node.style.top = t + 'px';
        };
        //被拖拽物体保持相对距离和鼠标移动
        document.addEventListener('mousemove', moveFunc);
      };

      //取消拖拽
      document.addEventListener('mouseup', () => {
        document.removeEventListener('mousemove', moveFunc);
        setTimeout(() => {
          this.workSheetIsBoxMoving = false;
        }, 200);
      });
    },
    computedWatingTime(visitTime) {
      let time = new Date(visitTime).getTime(),
        nowTime = new Date().getTime(),
        waitingSecond = dayjs(nowTime).diff(dayjs(time), 'seconds'),
        wDays = parseInt(waitingSecond / (3600 * 24)),
        wHour = parseInt((waitingSecond % (3600 * 24)) / 3600),
        wMinute = parseInt(((waitingSecond % (3600 * 24)) % 3600) / 60),
        wSecond = waitingSecond % 60;
      return (
        (wDays ? wDays + '天&ensp;' : '') +
        wHour +
        ':' +
        wMinute +
        ':' +
        wSecond
      );
    },
    //添加计时器
    addWatingInterval() {
      this.workSheetWaitingTimer && clearInterval(this.workSheetWaitingTimer);
      this.workSheetWaitingTimer = setInterval(() => {
        let nodeList = document.querySelectorAll(
          '[name="order_waiting_list_item_visitTime"]'
        );

        nodeList.forEach((item, index) => {
          let time = Number(item.getAttribute('time')),
            nowTime = new Date().getTime(),
            waitingSecond = dayjs(nowTime).diff(dayjs(time), 'seconds'),
            wDays = parseInt(waitingSecond / (3600 * 24)),
            wHour = parseInt((waitingSecond % (3600 * 24)) / 3600),
            wMinute = parseInt(((waitingSecond % (3600 * 24)) % 3600) / 60),
            wSecond = waitingSecond % 60;
          item.innerHTML = `
              <span class="title">等待时长：</span>
              ${wDays ? wDays + '天&ensp;' : ''}${wHour}:${wMinute}:${wSecond}
              `;
        });
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
#workSheetWaitingListBox.waiting-list {
  position: absolute;
  z-index: 99999;
  width: 208px;
  height: 88px;
  // bottom: 24px;
  top: calc(100vh - 112px);
  left: 40px;
}
#workSheetWaitingListBox .waiting-bg {
  height: 100%;
  width: 100%;
}
#workSheetWaitingListBox .waiting-title {
  color: #fff;
  position: absolute;
  top: 50%;
  left: 22px;
  transform: translate(0, -50%);
  /* font-weight: 600; */
  width: 44px;
  height: 56px;
  letter-spacing: 2px;
  font-size: 20px;
}
#workSheetWaitingListBox .waiting-info {
  position: absolute;
  top: 50%;
  left: 88px;
  transform: translate(0, -50%);
  font-size: 12px;
  color: #333333;
  line-height: 18px;
}
#workSheetWaitingListBox .waiting-info .waiting-num {
  font-size: 16px;
  font-weight: 600;
  color: #f45555;
  line-height: 22px;
  margin-top: 2px;
}
.waitinglistContent {
  height: 200px;
  overflow: hidden;
  /deep/.el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-scrollbar__view > div {
    padding: 8px 0;
    border-bottom: 1px solid #33333380;
    &:hover {
      background-color: #e7ebf0;
    }
    &:nth-child(n*2) {
      background-color: #f9f9f9;
    }
    div {
      color: #333333b3;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .title {
      color: #333;
      font-weight: 600;
    }
  }
}
</style>
