import { $get, $postJson, $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  // 获取个人信息
  getUserInfo() {
    return $get(`${service.tsBasics()}/employee/getMyEmployeeDetail`);
  },

  /**@desc 修改个人信息**/
  updateUserConfig(datas) {
    return $postJson(`${service.tsBasics()}/employee/updateUserConfig`, datas);
  },

  CommErrorLogsList(params = {}) {
    return $get(`${service.tsMessage()}/api/CommErrorLogs/list`, params);
  },

  DruidMonitoringRecordsList(params = {}) {
    return $get(
      `${service.tsMessage()}/api/DruidMonitoringRecords/list`,
      params
    );
  },

  PisZbCollectionList(params = {}) {
    return $get(`${service.tsMessage()}/PisZbCollection/list`, params);
  },

  // 系统运行情况
  sysUsageList(params = {}) {
    return $get(`${service.tsInformation()}/api/sysUsage/list`, params);
  },

  // 数据字典列表数据
  dictTypeList(data = {}) {
    return $api({
      url: `${service.tsBasics()}/dictType/list`,
      method: 'post',
      data
    });
  },

  dictTypeSave(datas) {
    return $postJson(`${service.tsBasics()}/dictType/save`, datas);
  },

  dictTypeUpdate(datas) {
    return $postJson(`${service.tsBasics()}/dictType/update`, datas);
  },

  dictTypeDictTypeEnable(datas) {
    return $postJson(`${service.tsBasics()}/dictType/dictTypeEnable`, datas);
  },

  dictTypeDeletedById(datas) {
    return $postJson(`${service.tsBasics()}/dictType/deletedById`, datas);
  },

  dictItemList(data) {
    return $api({
      url: `${service.tsBasics()}/dictItem/list`,
      method: 'post',
      data
    });
  },

  dictItemDeletedById(datas) {
    return $postJson(`${service.tsBasics()}/dictItem/deletedById`, datas);
  },

  dictItemDictItemEnable(datas) {
    return $postJson(`${service.tsBasics()}/dictItem/dictItemEnable`, datas);
  },

  dictItemSave(datas) {
    return $postJson(`${service.tsBasics()}/dictItem/save`, datas);
  },

  dictItemUpdate(datas) {
    return $postJson(`${service.tsBasics()}/dictItem/update`, datas);
  },

  dictItemUpdate(datas) {
    return $postJson(`${service.tsBasics()}/dictItem/update`, datas);
  },

  loginLogsList(params = {}) {
    return $get(`${service.tsBasics()}/api/loginLogs/list`, params);
  },

  loginLogsDisableOrEnable(datas, status) {
    return $postJson(
      `${service.tsBasics()}/api/loginLogs/disableOrEnable?status=${status}`,
      datas
    );
  },

  loginLogsBatchUpdatePwd(datas) {
    return $postJson(
      `${service.tsBasics()}/api/loginLogs/batchUpdatePwd`,
      datas
    );
  },

  loginLogsBatchUnlock(datas) {
    return $postJson(`${service.tsBasics()}/api/loginLogs/batchUnlock`, datas);
  },

  globalSettingUpdate(datas) {
    return $postJson(`${service.tsBasics()}/globalSetting/update`, datas);
  },

  PisZbMonitorControllerGetPageList(params = {}) {
    return $get(
      `${service.tsMessage()}/PisZbMonitorController/getPageList`,
      params
    );
  },

  getOfficalDictionList(data) {
    return $api({
      url: `/ts-oa/employee/officaldiction/list`,
      method: 'post',
      data
    });
  },

  updateOfficalDictionDefaultValue(data) {
    return $postJson(`/ts-oa/employee/officaldiction/setDefaultValue`, data);
  },

  updateOfficalDictionHideValue(data) {
    return $postJson(`/ts-oa/employee/officaldiction/setHideValue`, data);
  },

  deleteOfficalDiction(data) {
    return $postJson(`/ts-oa/employee/officaldiction/deletedById`, data);
  },

  addOfficalDiction(data) {
    return $postJson(`/ts-oa/employee/officaldiction/save`, data);
  },

  updateOfficalDiction(data) {
    return $postJson(`/ts-oa/employee/officaldiction/update`, data);
  },

  // 人员映射列表数据
  userMappingList(data = {}) {
    return $api({
      url: `${service.tsBasics()}/api/customEmployeeMapping/list`,
      method: 'post',
      data
    });
  },

  // 删除人员映射列表数据
  deleteUserMappingById(id) {
    return $postJson(
      `${service.tsBasics()}/api/customEmployeeMapping/delete/${id}`
    );
  },

  // 更新人员映射列表数据除
  updateMappingData(data = {}) {
    return $postJson(
      `${service.tsBasics()}/api/customEmployeeMapping/update`,
      data
    );
  },

  // 人员映射列表数据同步
  initUserMappingData(data = {}) {
    return $postJson(
      `${service.tsBasics()}/api/customEmployeeMapping/initMapping`,
      data
    );
  },

  // OA人员人员列表数据
  baseEmployeeNameList(params, data) {
    return $api({
      url: `${service.tsBasics()}/api/customEmployeeBase/getEmployeeBasePageList`,
      method: 'get',
      params: params,
      data: data
    });
  },

  // 科室映射列表数据
  deptMappingList(params, data) {
    return $api({
      url: `/ts-external/api/deptMapping/list`,
      method: 'get',
      params: params,
      data: data
    });
  },
  // 删除科室映射列表数据
  deleteDeptMappingById(id) {
    return $postJson(`/ts-external/api/deptMapping/delete/${id}`);
  },

  // 保存科室映射列表数据除
  saveDeptMappingData(data = {}) {
    return $postJson(`/ts-external/api/deptMapping/save`, data);
  },

  // 更新科室映射列表数据除
  updateDeptMappingData(data = {}) {
    return $postJson(`/ts-external/api/deptMapping/update`, data);
  }
};
