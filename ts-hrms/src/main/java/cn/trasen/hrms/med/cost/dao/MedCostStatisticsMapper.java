package cn.trasen.hrms.med.cost.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.hrms.med.cost.model.MedCostStatistics;
import tk.mybatis.mapper.common.Mapper;

public interface MedCostStatisticsMapper extends Mapper<MedCostStatistics> {

	List<MedCostStatistics> getCostStatisticsList(@Param("month")String month);

	List<MedCostStatistics> getRanking(@Param("type")String type,@Param("month")String month);

	MedCostStatistics getAllMedCostStatistics(@Param("month")String month);

	List<Map<String, Object>> getHczcDetails(@Param("month")String month,
			@Param("outDeptName")String outDeptName,@Param("sidx")String sidx,@Param("sord")String sord);
}