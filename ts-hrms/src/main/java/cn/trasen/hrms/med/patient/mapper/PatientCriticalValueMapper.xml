<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientCriticalValueMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientCriticalValue">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_id" jdbcType="VARCHAR" property="patnId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="critical_value" jdbcType="VARCHAR" property="criticalValue" />
    <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
    <result column="order_item_name" jdbcType="VARCHAR" property="orderItemName" />
    <result column="request_dept_id" jdbcType="VARCHAR" property="requestDeptId" />
    <result column="request_dept_name" jdbcType="VARCHAR" property="requestDeptName" />
    <result column="sign_date" jdbcType="TIMESTAMP" property="signDate" />
    <result column="process_date" jdbcType="TIMESTAMP" property="processDate" />
     <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="selectCriticalValueByShift" parameterType="String" resultType="String">
  		SELECT critical_value FROM med_patient_critical_value 
		WHERE patn_no = #{patnNo} AND is_deleted = 'N' AND report_date BETWEEN #{scheduleStartDate} and #{scheduleEndDate}
  </select>
</mapper>