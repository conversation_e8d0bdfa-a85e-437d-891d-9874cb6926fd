package cn.trasen.hrms.med.risk.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.risk.model.RiskOperationDiscuss;
import cn.trasen.hrms.med.risk.service.RiskOperationDiscussService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RiskOperationDiscussController
 * @Description TODO
 * @date 2025��4��29�� ����10:03:34
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "RiskOperationDiscussController")
public class RiskOperationDiscussController {

	private transient static final Logger logger = LoggerFactory.getLogger(RiskOperationDiscussController.class);

	@Autowired
	private RiskOperationDiscussService riskOperationDiscussService;

	/**
	 * @Title saveRiskOperationDiscuss
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����10:03:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/medRiskOperationDiscuss/save")
	public PlatformResult<String> saveRiskOperationDiscuss(@RequestBody RiskOperationDiscuss record) {
		try {
			riskOperationDiscussService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRiskOperationDiscuss
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����10:03:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/medRiskOperationDiscuss/update")
	public PlatformResult<String> updateRiskOperationDiscuss(@RequestBody RiskOperationDiscuss record) {
		try {
			riskOperationDiscussService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRiskOperationDiscussById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RiskOperationDiscuss>
	 * @date 2025��4��29�� ����10:03:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/medRiskOperationDiscuss/{id}")
	public PlatformResult<RiskOperationDiscuss> selectRiskOperationDiscussById(@PathVariable String id) {
		try {
			RiskOperationDiscuss record = riskOperationDiscussService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRiskOperationDiscussById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��29�� ����10:03:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/medRiskOperationDiscuss/delete/{id}")
	public PlatformResult<String> deleteRiskOperationDiscussById(@PathVariable String id) {
		try {
			riskOperationDiscussService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRiskOperationDiscussList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RiskOperationDiscuss>
	 * @date 2025��4��29�� ����10:03:34
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/medRiskOperationDiscuss/list")
	public DataSet<RiskOperationDiscuss> selectRiskOperationDiscussList(Page page, RiskOperationDiscuss record) {
		return riskOperationDiscussService.getDataSetList(page, record);
	}
}
