<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientOrderrecordMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientOrderrecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_id" jdbcType="VARCHAR" property="patnId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="baby_id" jdbcType="VARCHAR" property="babyId" />
    <result column="dept_br" jdbcType="VARCHAR" property="deptBr" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="order_doc" jdbcType="VARCHAR" property="orderDoc" />
    <result column="order_bdate" jdbcType="TIMESTAMP" property="orderBdate" />
    <result column="order_edate" jdbcType="TIMESTAMP" property="orderEdate" />
    <result column="status_flag" jdbcType="VARCHAR" property="statusFlag" />
    <result column="mngtype" jdbcType="VARCHAR" property="mngtype" />
    <result column="book_date" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="order_update_time" jdbcType="TIMESTAMP" property="orderUpdateTime" />
    <result column="delete_bit" jdbcType="VARCHAR" property="deleteBit" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="hoitem_id" jdbcType="LONGVARCHAR" property="hoitemId" />
    <result column="order_context" jdbcType="LONGVARCHAR" property="orderContext" />
  </resultMap>
</mapper>