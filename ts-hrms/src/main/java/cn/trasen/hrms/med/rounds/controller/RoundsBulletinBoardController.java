package cn.trasen.hrms.med.rounds.controller;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.kpi.model.OfYpZbk;
import cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard;
import cn.trasen.hrms.med.rounds.model.RoundsScheduling;
import cn.trasen.hrms.med.rounds.service.RoundsBulletinBoardService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "查房看板")
public class RoundsBulletinBoardController {

	private transient static final Logger logger = LoggerFactory.getLogger(RoundsGroupController.class);
	
	@Autowired
	private RoundsBulletinBoardService roundsBulletinBoardService;
	
	@Autowired
	private RoundsTaskService roundsTaskService;
	
	@Autowired
	private RoundsTaskGroupService roundsTaskGroupService;
	
	@Autowired
    DictItemFeignService dictItemFeignService;
	
	/**
	 * @Title getRoundsDataCount
	 * @Description 查房看板数据统计
	 * @param record
	 * @date 2025��3��7�� ����3:26:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查房看板数据统计", notes = "查房看板数据统计")
	@GetMapping("/api/roundsBulletinBoard/getRoundsDataCount")
	public PlatformResult<Map<String, Object>> getCompletionRate(RoundsBulletinBoard record) {
		Map<String,Object> resultMap =new HashMap<String, Object>();
		Map<String, Object> map1 = roundsBulletinBoardService.getCompletionRate(record);//查房完成率
		resultMap.putAll(map1);
		Map<String, Object> map2 = roundsBulletinBoardService.getProblemReductionRate(record);//问题环比下降率
		resultMap.putAll(map2);
		Map<String, Object> map3 = roundsBulletinBoardService.getProblemCompletionRate(record);//整改完成率
		resultMap.putAll(map3);
		Map<String, Object> map4 = roundsBulletinBoardService.getProblemRepeatRate(record);//重复问题率
		resultMap.putAll(map4);
		return  PlatformResult.success(resultMap);
	}
	
	/**
	 * @Title getRoundsDataCount
	 * @Description 查房看板数据统计
	 * @param record
	 * @date 2025��3��7�� ����3:26:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查房科室得分扣分情况", notes = "查房科室得分扣分情况")
	@GetMapping("/api/roundsBulletinBoard/getRoundsDeptScoreSituation")
	public PlatformResult<Map<String, Object>> getRoundsDeptScoreSituation(RoundsBulletinBoard record) {
		Map<String,Object> resultMap =new HashMap<String, Object>();
		 if(!StringUtils.isEmpty(record.getOrgIds())) {
				// 使用split方法按逗号分割字符串
		        String[] orgIds = record.getOrgIds().split(",");
		        // 将数组转换为List<String>
		        List<String> orgIdList = new ArrayList<>(Arrays.asList(orgIds));
		        record.setOrgIdList(orgIdList);
			}
		//查询表头
		List<VueTableEntity> headerList = roundsBulletinBoardService.getRoundsDeptScoreSituationHeader(record);
		resultMap.put("headerList",headerList);
		//查询数据
		List<Map<String, Object>> dataList = roundsBulletinBoardService.getRoundsDeptScoreSituationData(record);
		resultMap.put("dataList",dataList);
		
		return  PlatformResult.success(resultMap);
	}
	
	
	/**
	 * @Title getOverdueNotRectified
	 * @Description 超期未整改
	 * @date 2025��3��7�� ����3:26:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "超期未整改", notes = "超期未整改")
	@GetMapping("/api/roundsBulletinBoard/getOverdueNotRectified")
	public PlatformResult<List<Map<String, Object>>> getOverdueNotRectified(RoundsBulletinBoard record) {
		List<Map<String, Object>> resultMap = roundsBulletinBoardService.getOverdueNotRectified(record);//超期未整改
		return  PlatformResult.success(resultMap);
	}
	
	/**
	* @date 2025年06月18日
	* <AUTHOR>
	*/
	@ApiOperation(value = "导出超期未整改列表", notes = "导出超期未整改列表")
	@GetMapping("/api/roundsBulletinBoard/exportOverdueNotRectified")
	public void exportOverdueNotRectified(HttpServletRequest request, HttpServletResponse response,RoundsBulletinBoard record) {
		List<Map<String, Object>> records = roundsBulletinBoardService.getOverdueNotRectified(record);//超期未整改
		// 导出文件名称
		String name = "超期未整改.xls";
		
		// 模板位置
		String templateUrl = "template/exportOverdueNotRectified.xls";
		// 导出数据列表
		try {
				if (CollectionUtils.isNotEmpty(records)) {
					ExportUtil.export(request, response, records, name, templateUrl);
				} else {
					ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
	}
	
	/**
	 * @Title getOverdueNotRectified
	 * @Description 查房问题分析(组问题数量图表)
	 * @date 2025��3��7�� ����3:26:44
	 * <AUTHOR>
	 */
	@ApiOperation(value = "组问题数量图表", notes = "组问题数量图表")
	@GetMapping("/api/roundsBulletinBoard/getGroupProblemChart")
	public PlatformResult<List<Map<String, Object>>> getGroupProblemChart(RoundsBulletinBoard record) {
		List<Map<String, Object>> resultMap = roundsBulletinBoardService.getGroupProblemChart(record);//组问题数量图表
		return  PlatformResult.success(resultMap);
	}
	
	
	@ApiOperation(value = "导出查房科室得分扣分情况", notes = "导出查房科室得分扣分情况")
	@GetMapping("/api/roundsScheduling/exportRoundsDeptScoreSituation")
	public void exportRoundsDeptScoreSituation(HttpServletRequest request, HttpServletResponse response, RoundsBulletinBoard record) {
		// 导出文件名称
		String filename = "查房科室得分扣分情况";
		// 导出数据列表
		try {
			if(!StringUtils.isEmpty(record.getOrgIds())) {
				// 使用split方法按逗号分割字符串
		        String[] orgIds = record.getOrgIds().split(",");
		        // 将数组转换为List<String>
		        List<String> orgIdList = new ArrayList<>(Arrays.asList(orgIds));
		        record.setOrgIdList(orgIdList);
			}
		//查询表头
		List<VueTableEntity> exportHeaderList  = roundsBulletinBoardService.getRoundsDeptScoreSituationHeader(record);
		//查询数据
		List<Map<String, Object>> records = roundsBulletinBoardService.getRoundsDeptScoreSituationData(record);
			
		Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
			 
		//添加序号
		  int index = 1;
		  for(int i = 0;i <records.size();i++) {
			  Map<String,Object> map =records.get(i);
			  map.put("hosp_area", hospAreaMap.get(map.get("hosp_area").toString()));//获取院区对应字典值
			  map.put("no", String.valueOf(index)); 
			  index++; 
			  }
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
			//添加序号
			colList.add(new ExcelExportEntity("序号", "no",6));
			if (exportHeaderList != null && exportHeaderList.size() > 0) {
				for(int i =0 ;i < exportHeaderList.size();i++) {
					if(!"hosp_area".equals(exportHeaderList.get(i).getProp())) {
						colList.add(new ExcelExportEntity(exportHeaderList.get(i).getLabel(), exportHeaderList.get(i).getProp(),exportHeaderList.get(i).getWidth()));
					}
				}
			}
			
			try {
				 ExportParams params = new ExportParams(null, filename, ExcelType.XSSF);
				 params.setStyle(ExcelStyleUtil.class);
		            //params.setAddIndex(true);
	            Workbook workbook = ExcelExportUtil.exportExcel(params, colList, records);
	            response.setContentType("application/vnd.ms-excel");
	            response.setCharacterEncoding("UTF-8");
	            response.setHeader("Content-disposition", "attachment; filename="
	                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

	            OutputStream fos = response.getOutputStream();
	            workbook.write(fos);
	            fos.close();

	        } catch (FileNotFoundException e) {
	        	logger.error(e.getMessage(),e);
	        } catch (IOException e) {
	        	logger.error(e.getMessage(),e);
	        }
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}
	
	private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
	
	
	@ApiOperation(value = "查房整改数量统计", notes = "查房整改数量统计")
	@GetMapping("/api/roundsBulletinBoard/getRectifiedCount")
	public PlatformResult<List<Map<String, Object>>> getRectifiedCount(RoundsBulletinBoard record) {
		List<Map<String, Object>> resultMap = roundsBulletinBoardService.getRectifiedCount(record);
		return  PlatformResult.success(resultMap);
	}
	
}
