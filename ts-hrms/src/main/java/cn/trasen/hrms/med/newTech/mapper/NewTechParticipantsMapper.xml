<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.newTech.dao.NewTechParticipantsMapper">
  
  <select id="getChildDataByWorkFlowId" resultType="map">
    select *
        from ${tableName}
        where is_deleted = 'N' 
        and workflow_id = #{workflowId}
  </select>
  
  <select id="selectListByNewTechId" resultType="cn.trasen.hrms.med.newTech.model.NewTechParticipants">
	  select m.id, m.new_tech_id,
	  m.employee_id, e.employee_name,e.employee_no,
	  e.sso_org_code,e.sso_org_name,e.org_id org_id,o.name org_name,e.identity_number identity_number,e.hosp_code hosp_area,
	  e.phone_number tel,p.position_name duty_title,i.technical jobtitle,a4.education_type_name degree
	  from  med_new_tech_participants m
	 left join cust_emp_base e on e.employee_id=m.employee_id
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	 left join ( SELECT  ed.employee_id,MIN(ed.highest_level) highest_level,  max(dict.ITEM_NAME) as education_type_name  
                    FROM hrms_education_info ed 
                    left join comm_dict_item dict on ed.education_type =  dict.item_code and dict.dic_type_id ='education_type' 
                    WHERE ed.is_deleted = 'N' GROUP BY      ed.employee_id )a4 on a4.employee_id = e.employee_id
     left join comm_dict_item dict on e.gender =  dict.item_code and dict.dic_type_id ='SEX'
     left join comm_position p on p.position_id=e.position_id
	   where m.is_deleted='N' and m.new_tech_id = #{newTechId}
  </select>
  
  <select id="selectByEmployeeNo" resultType="cn.trasen.hrms.med.newTech.vo.EmployeeRespVo">
	  select e.employee_id, e.employee_name,e.employee_no,dict.item_name sex,e.hosp_code hosp_code,
	  e.sso_org_code,e.sso_org_name,e.org_id org_id,o.name org_name,e.identity_number identity_number,
	  e.phone_number tel,p.position_name duty_title,i.technical jobtitle,
	  date_format(e.birthday, '%Y-%m-%d') pro_birth_date,
	  a4.education_type_name as degree
	 from cust_emp_base e
	 left join cust_emp_info i on e.employee_id = i.info_id
	 LEFT JOIN comm_organization o ON e.org_id = o.organization_id AND o.is_deleted = 'N' 
	 left join ( SELECT  ed.employee_id,MIN(ed.highest_level) highest_level,  max(dict.ITEM_NAME) as education_type_name  
                    FROM hrms_education_info ed 
                    left join comm_dict_item dict on ed.education_type =  dict.item_code and dict.dic_type_id ='education_type' 
                    WHERE ed.is_deleted = 'N' GROUP BY      ed.employee_id )a4 on a4.employee_id = e.employee_id
     left join comm_dict_item dict on e.gender =  dict.item_code and dict.dic_type_id ='SEX'
     left join comm_position p on p.position_id=e.position_id
	   where e.is_deleted='N' and e.employee_no = #{employeeNo}
  </select>
</mapper>