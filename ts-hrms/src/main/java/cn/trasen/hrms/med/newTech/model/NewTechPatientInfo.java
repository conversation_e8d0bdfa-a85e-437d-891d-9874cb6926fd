package cn.trasen.hrms.med.newTech.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 新技术开展患者表
 * @date 2025-05-12 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "med_new_tech_patient_info")
@Setter
@Getter
public class NewTechPatientInfo {
	
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
	
    @Column(name = "tech_code")
    @ApiModelProperty(value = "关联新技术编码")
    private String techCode;
	
    @Column(name = "operation_date")
    @ApiModelProperty(value = "手术日期")
    private String operationDate;
	
    @Column(name = "in_patient_no")
    @ApiModelProperty(value = "病例号")
    private String inPatientNo;
    
    @Column(name = "patient_name")
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    
    @Column(name = "sex")
    @ApiModelProperty(value = "性别")
    private String sex;
    
    @Column(name = "main_diagnosis")
    @ApiModelProperty(value = "主要诊断")
    private String mainDiagnosis;
    
    @Column(name = "is_complaint_or_dispute")
    @ApiModelProperty(value = "是否有医疗投诉纠纷：0-否，1-是")
    private String isComplaintOrDispute;
    
    @Column(name = "is_adverse_event")
    @ApiModelProperty(value = "是否有不良事件：0-否，1-是")
    private String isAdverseEvent;
    
    @Column(name = "is_severe_complications")
    @ApiModelProperty(value = "是否有严重并发症：0-否，1-是")
    private String isSevereComplications;
    
    @Column(name = "is_unplanned_reoperation")
    @ApiModelProperty(value = "是否有非计划再次手术：0-否，1-是")
    private String isUnplannedReoperation;
    
    @Column(name = "in_hosp_days")
    @ApiModelProperty(value = "入院天数")
    private String inHospDays;
    
    @Column(name = "sum_fee")
    @ApiModelProperty(value = "总费用")
    private String sumFee;
    
    
    /****************审计信息 *************/

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识：N-未删除，Y-已删除")
    private String isDeleted;
    
}
