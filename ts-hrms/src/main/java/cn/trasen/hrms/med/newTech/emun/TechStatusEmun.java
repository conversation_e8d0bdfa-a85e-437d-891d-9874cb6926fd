package cn.trasen.hrms.med.newTech.emun;

/**
 * 新技术状态
 * <AUTHOR>
 *
 */
public enum TechStatusEmun {
	
	RUNNNING("1", "开展中"),  
	INTERRUPT("2", "已中止"),  
	NORMAL("3", "转常规");
	
	private String name;

	private String code;

	TechStatusEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (TechStatusEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}



}
