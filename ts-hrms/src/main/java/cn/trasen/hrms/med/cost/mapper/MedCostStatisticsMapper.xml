<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.cost.dao.MedCostStatisticsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.cost.model.MedCostStatistics">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="COST_MONTH" jdbcType="VARCHAR" property="costMonth" />
    <result column="BRANCH_NAME" jdbcType="VARCHAR" property="branchName" />
    <result column="OUT_DEPT_NAME" jdbcType="VARCHAR" property="outDeptName" />
    <result column="HZB" jdbcType="VARCHAR" property="hzb" />
    <result column="BKJFHCZB" jdbcType="VARCHAR" property="bkjfhczb" />
    <result column="BKJFSJZB" jdbcType="VARCHAR" property="bkjfsjzb" />
    <result column="BKJFZHCZB" jdbcType="VARCHAR" property="bkjfzhczb" />
    <result column="SJSZZB" jdbcType="VARCHAR" property="sjszzb" />
    <result column="DZHCZB" jdbcType="VARCHAR" property="dzhczb" />
    <result column="GZHCZB" jdbcType="VARCHAR" property="gzhczb" />
    <result column="DLHCZB" jdbcType="VARCHAR" property="dlhczb" />
    <result column="ZHCZC" jdbcType="VARCHAR" property="zhczc" />
    <result column="GZHCZC" jdbcType="VARCHAR" property="gzhczc" />
    <result column="DZHCZC" jdbcType="VARCHAR" property="dzhczc" />
    <result column="HCZC" jdbcType="VARCHAR" property="hczc" />
    <result column="SJZC" jdbcType="VARCHAR" property="sjzc" />
  </resultMap>
  
  <select id="getCostStatisticsList" resultType="cn.trasen.hrms.med.cost.model.MedCostStatistics" parameterType="String">
  		SELECT 
			COST_MONTH AS costMonth,
			BRANCH_NAME AS branchName,
			OUT_DEPT_NAME AS outDeptName,
			CASE 
				WHEN IFNULL(ZJE,0) = 0 THEN 0
				WHEN IFNULL(ZJE,0) > 0 
				THEN ROUND((IFNULL(DL_SETTLE_AMOUNT,0) + IFNULL(FDL_SETTLE_AMOUNT,0) + IFNULL(BG_SETTLE_AMOUNT,0)) / IFNULL(ZJE,0) * 100,2)
			END hzb,
			CASE 
				WHEN (IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) + IFNULL(CHARGING_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) + IFNULL(CHARGING_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND(IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) / (IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) + IFNULL(CHARGING_SETTLE_AMOUNT,0)) * 100,2)
			END bkjfhczb,
			CASE 
				WHEN (IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND(IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0) / (IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)) * 100,2)
			END bkjfsjzb,
			CASE 
				WHEN (IFNULL(CHARGING_SETTLE_AMOUNT,0) + IFNULL(NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(BG_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(CHARGING_SETTLE_AMOUNT,0) + IFNULL(NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(BG_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND((IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)  + IFNULL(BG_SETTLE_AMOUNT,0) ) / (IFNULL(CHARGING_SETTLE_AMOUNT,0) + IFNULL(NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)+ IFNULL(BG_SETTLE_AMOUNT,0)) * 100,2)
			END bkjfzhczb,
			CASE 
				WHEN IFNULL(ZXJE,0) = 0 THEN 0
				WHEN IFNULL(ZXJE,0) > 0 
				THEN ROUND((IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)) / IFNULL(ZXJE,0) * 100,2)
			END sjszzb,
			CASE 
				WHEN (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND(IFNULL(DZ_SETTLE_AMOUNT,0) / (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) * 100,2)
			END dzhczb,
			CASE 
				WHEN (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND(IFNULL(GZ_SETTLE_AMOUNT,0) / (IFNULL(DZ_SETTLE_AMOUNT,0) + IFNULL(GZ_SETTLE_AMOUNT,0)) * 100,2)
			END gzhczb,
			CASE 
				WHEN (IFNULL(DL_SETTLE_AMOUNT,0) + IFNULL(FDL_SETTLE_AMOUNT,0)) = 0 THEN 0
				WHEN (IFNULL(DL_SETTLE_AMOUNT,0) + IFNULL(FDL_SETTLE_AMOUNT,0)) > 0 
				THEN ROUND(IFNULL(DL_SETTLE_AMOUNT,0) / (IFNULL(DL_SETTLE_AMOUNT,0) + IFNULL(FDL_SETTLE_AMOUNT,0)) * 100,2)
			END dlhczb,
			IFNULL(CHARGING_SETTLE_AMOUNT,0) + IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0)  AS zhczc,
			GZ_SETTLE_AMOUNT AS gzhczc,
			DZ_SETTLE_AMOUNT AS dzhczc,
			IFNULL(CHARGING_SETTLE_AMOUNT,0) + IFNULL(NO_CHARGING_SETTLE_AMOUNT,0) AS hczc,
			IFNULL(SJ_CHARGING_SETTLE_AMOUNT,0) + IFNULL(SJ_NO_CHARGING_SETTLE_AMOUNT,0) AS sjzc
		FROM MED_COST_VIEW
		where 1=1
		<if test="month != null and month != ''">
		   and COST_MONTH = #{month}
		</if>
  </select>
  
  <select id="getRanking" resultType="cn.trasen.hrms.med.cost.model.MedCostStatistics" parameterType="String">
  			SELECT OUT_DEPT_NAME,${type} FROM MED_COST_STATISTICS
			WHERE COST_MONTH  = #{month}
			ORDER BY CAST(${type} AS DECIMAL(10,2)) desc
			limit 10
  </select>
  
  <select id="getAllMedCostStatistics" resultType="cn.trasen.hrms.med.cost.model.MedCostStatistics" parameterType="String">
 			SELECT 
			COST_MONTH AS costMonth,
			CASE 
				WHEN IFNULL(sum(ZJE),0) = 0 THEN 0
				WHEN IFNULL(sum(ZJE),0) > 0 
				THEN ROUND((IFNULL(sum(DL_SETTLE_AMOUNT),0) + IFNULL(sum(FDL_SETTLE_AMOUNT),0) + IFNULL(sum(BG_SETTLE_AMOUNT),0)) / IFNULL(sum(ZJE),0) * 100,2)
			END hzb,
			CASE 
				WHEN (IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(CHARGING_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(CHARGING_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND(IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) / (IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(CHARGING_SETTLE_AMOUNT),0)) * 100,2)
			END bkjfhczb,
			CASE 
				WHEN (IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND(IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0) / (IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)) * 100,2)
			END bkjfsjzb,
			CASE 
				WHEN (IFNULL(sum(CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(BG_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(BG_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND((IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)  + IFNULL(sum(BG_SETTLE_AMOUNT),0) ) / (IFNULL(sum(CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)+ IFNULL(sum(BG_SETTLE_AMOUNT),0)) * 100,2)
			END bkjfzhczb,
			CASE 
				WHEN IFNULL(sum(ZXJE),0) = 0 THEN 0
				WHEN IFNULL(sum(ZXJE),0) > 0 
				THEN ROUND((IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0)) / IFNULL(sum(ZXJE),0) * 100,2)
			END sjszzb,
			CASE 
				WHEN (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND(IFNULL(sum(DZ_SETTLE_AMOUNT),0) / (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) * 100,2)
			END dzhczb,
			CASE 
				WHEN (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND(IFNULL(sum(GZ_SETTLE_AMOUNT),0) / (IFNULL(sum(DZ_SETTLE_AMOUNT),0) + IFNULL(sum(GZ_SETTLE_AMOUNT),0)) * 100,2)
			END gzhczb,
			CASE 
				WHEN (IFNULL(sum(DL_SETTLE_AMOUNT),0) + IFNULL(sum(FDL_SETTLE_AMOUNT),0)) = 0 THEN 0
				WHEN (IFNULL(sum(DL_SETTLE_AMOUNT),0) + IFNULL(sum(FDL_SETTLE_AMOUNT),0)) > 0 
				THEN ROUND(IFNULL(sum(DL_SETTLE_AMOUNT),0) / (IFNULL(sum(DL_SETTLE_AMOUNT),0) + IFNULL(sum(FDL_SETTLE_AMOUNT),0)) * 100,2)
			END dlhczb,
			IFNULL(sum(CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0) AS zhczc,
			sum(GZ_SETTLE_AMOUNT) AS gzhczc,
			sum(DZ_SETTLE_AMOUNT) AS dzhczc,
			IFNULL(sum(CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(NO_CHARGING_SETTLE_AMOUNT),0) AS hczc,
			IFNULL(sum(SJ_CHARGING_SETTLE_AMOUNT),0) + IFNULL(sum(SJ_NO_CHARGING_SETTLE_AMOUNT),0) AS sjzc
		FROM MED_COST_VIEW 
		WHERE COST_MONTH  = #{month}
	</select>
	
	<select id="getHczcDetails" resultType="Map" parameterType="String">
			SELECT 
			    curr.BRANCH_NAME,
			    curr.OUT_DEPT_NAME,
			    IFNULL(curr.HCZC,0) AS current_hczc,
			    IFNULL(prev.HCZC,0) AS prev_month_hczc,
			    IFNULL(last_year.HCZC,0) AS last_year_hczc,
			    -- 环比计算
			    CASE 
			        WHEN prev.HCZC IS NULL OR prev.HCZC = 0 THEN 0
			        ELSE curr.HCZC - prev.HCZC
			    END AS mom_growth,
			    -- 同比计算
			    CASE 
			        WHEN last_year.HCZC IS NULL OR last_year.HCZC = 0 THEN 0
			        ELSE curr.HCZC - last_year.HCZC
			    END AS yoy_growth,
			    -- 环比计算
			    CASE 
			        WHEN prev.HCZC IS NULL OR prev.HCZC = 0 THEN 0
			        ELSE ROUND((curr.HCZC - prev.HCZC) / prev.HCZC * 100, 2)
			    END AS mom_growth_rate,
			    -- 同比计算
			    CASE 
			        WHEN last_year.HCZC IS NULL OR last_year.HCZC = 0 THEN 0
			        ELSE ROUND((curr.HCZC - last_year.HCZC) / last_year.HCZC * 100, 2)
			    END AS yoy_growth_rate
			FROM 
			    MED_COST_STATISTICS curr
			LEFT JOIN 
			    MED_COST_STATISTICS prev ON curr.BRANCH_NAME = prev.BRANCH_NAME 
			    AND curr.OUT_DEPT_NAME = prev.OUT_DEPT_NAME 
			    <choose>
				    <when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
						AND prev.COST_MONTH = TO_CHAR(ADD_MONTHS(TO_DATE(#{month}, 'YYYYMM'), -1), 'YYYYMM')
				    </when>
				    <otherwise>
						 AND prev.COST_MONTH = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d'), INTERVAL 1 MONTH), '%Y%m') -- 上个月
				    </otherwise>
			    </choose>
			LEFT JOIN 
			    MED_COST_STATISTICS last_year ON curr.BRANCH_NAME = last_year.BRANCH_NAME 
			    AND curr.OUT_DEPT_NAME = last_year.OUT_DEPT_NAME 
			    <choose>
				    <when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
						AND last_year.COST_MONTH = TO_CHAR(ADD_MONTHS(TO_DATE(#{month}, 'YYYYMM'), -12), 'YYYYMM')
				    </when>
				    <otherwise>
						 AND last_year.COST_MONTH = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d'), INTERVAL 1 YEAR), '%Y%m') -- 去年同月
				    </otherwise>
			     </choose>
			WHERE 
			    curr.COST_MONTH = #{month}
			    AND curr.HCZC IS NOT NULL AND curr.HCZC != '0'
			 <if test="outDeptName != null and outDeptName != ''">
			 	and curr.OUT_DEPT_NAME like concat('%',#{outDeptName},'%')
			 </if>   
			 <if test="sidx == null and sidx == ''">
			 	ORDER BY curr.BRANCH_NAME DESC
			 </if>   
			<if test="sidx != null and sidx != ''">
			 	ORDER BY ${sidx} ${sord}
			 </if>  
	</select>
</mapper>