package cn.trasen.hrms.med.newTech.emun;

/**
 * 是否新技术
 * <AUTHOR>
 *
 */
public enum IsNewTechEmun {
	
	IS("1", "是"),  
	NOT("0", "否");


	private String name;

	private String code;

	IsNewTechEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (IsNewTechEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}
}
