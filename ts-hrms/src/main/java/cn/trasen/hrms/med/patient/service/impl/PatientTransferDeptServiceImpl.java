package cn.trasen.hrms.med.patient.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.patient.dao.PatientTransferDeptMapper;
import cn.trasen.hrms.med.patient.model.PatientBirth;
import cn.trasen.hrms.med.patient.model.PatientTransferDept;
import cn.trasen.hrms.med.patient.service.PatientTransferDeptService;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatientTransferDeptImpl
 * @Description TODO
 * @date 2025��4��8�� ����4:48:25
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatientTransferDeptServiceImpl  implements PatientTransferDeptService {


	@Autowired
	private PatientTransferDeptMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatientTransferDept record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatientTransferDept record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PatientTransferDept record = new PatientTransferDept();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatientTransferDept selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatientTransferDept> getDataSetList(Page page, PatientTransferDept record) {
		Example example = new Example(PatientTransferDept.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatientTransferDept> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSavePatientTransferDept() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT   A.ID,A.INPATIENT_ID AS PATN_ID,C.INPATIENT_NO AS PATN_NO,A.TRANSFER_DATE,A.S_DEPT_ID OUT_DEPT_ID,DEPT.NAME AS OUT_DEPT_NAME,A.D_DEPT_ID AS IN_DEPT_ID,DEPT2.NAME AS IN_DEPT_NAME, A.CANCEL_BIT,A.FINISH_BIT  FROM  ODSZYV10.ZY_TRANSFER_DEPT  A  INNER JOIN ODSZYV10.ZY_INPATIENT B ON A.INPATIENT_ID=B.INPATIENT_ID  "
    				+ "          INNER  JOIN  ODSZYV10.BASE_PATIENT_PROPERTY  C ON B.PATIENT_ID=C.PATIENT_ID   "
    				+ "          LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT ON A.S_DEPT_ID=DEPT.DEPT_ID     "
    				+ "          LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT2 ON A.D_DEPT_ID=DEPT2.DEPT_ID    "
    				+ " ");
    		sb.append("   WHERE  ( A.TRANSFER_DATE >  TRUNC(SYSDATE -1) OR A.BOOK_DATE >  TRUNC(SYSDATE -1) )   "
    				+ " ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<PatientTransferDept> PatientTransferDeptList = HnsrmyyHisJdbcUtil.queryPatientTransferDeptSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========PatientTransferDeptList:"+PatientTransferDeptList.size());
    		if(CollUtil.isNotEmpty(PatientTransferDeptList)){
    			for(PatientTransferDept patientTransferDept : PatientTransferDeptList) {
    				PatientTransferDept record = mapper.selectByPrimaryKey(patientTransferDept.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					patientTransferDept.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(patientTransferDept);
    				}else {
    					patientTransferDept.setIsDeleted("N");
    					patientTransferDept.setCreateDate(DateUtil.date());
    					patientTransferDept.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(patientTransferDept);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库转科信息数据异常：" + e.getMessage());
    	}
	}
}
