<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.rounds.dao.RoundsBulletinBoardMapper">
  
  
  <select id="getCompletionRate" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
  select fq_cfzs,sj_cfcs,case when fq_cfzs=0 then 0 else CAST(sj_cfcs AS DECIMAL(15,2))/fq_cfzs end as cfwcl from(
  		select count(id) as fq_cfzs, sum(case when status=1 then 0 else 1 end) as sj_cfcs  from  med_rounds_scheduling  
  			   where is_deleted='N' and rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
  )z
  </select>
  
    <select id="getProblemReductionRate" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
	select problem_number_bq,problem_number_sq,case when problem_number_sq=0 then 0 else CAST((problem_number_bq-problem_number_sq) AS DECIMAL(15,2))/problem_number_sq end as wthbxjl  from (
		select  sum(problem_number) as problem_number_bq,1 as lj
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             left join (select task_id,group_id,count(1) as problem_number from(
		               select a.task_id,a.group_id,b.rounds_content  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,b.rounds_content
		             )z  group  by  task_id,group_id)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
		          <if test="isClinical !=null and isClinical !=''">
			  		and a.is_clinical = #{isClinical}
			 	 </if>
		)d   inner join 	 	 
	 	(
		select  sum(problem_number) as problem_number_sq,1 as lj
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             left join (select task_id,group_id,count(1) as problem_number from(
		               select a.task_id,a.group_id,b.rounds_content  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,b.rounds_content
		             )z  group  by  task_id,group_id)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             <choose>
						<when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
							and a.rounds_time between   TO_CHAR(ADD_MONTHS(#{roundsTimeBegin}, -1),'YYYY-MM-DD')  and   TO_CHAR(ADD_MONTHS(#{roundsTimeEnd}, -1),'YYYY-MM-DD')
						</when>
						<otherwise>
							and a.rounds_time between   date_add(#{roundsTimeBegin},interval -1 month)  and   date_add(#{roundsTimeEnd},interval -1 month)
						</otherwise>
				    </choose>
		             <if test="isClinical !=null and isClinical !=''">
			  		and a.is_clinical = #{isClinical}
			 	 </if>
		)f  on d.lj=f.lj
  </select>
  
 <select id="getProblemCompletionRate" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
 select problem_number,problem_rectification_number,case when problem_number=0 then 0 else CAST(problem_rectification_number AS DECIMAL(15,2))/problem_number end as zgwcl  from (
	select  sum(problem_number) as problem_number,sum(problem_rectification_number) as problem_rectification_number
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             left join (select task_id,group_id,count(1) as problem_number,sum(case when rectification_results='已整改' then 1 else 0 end) as problem_rectification_number  from(
		               select a.task_id,a.group_id,b.rounds_content,a.rectification_results  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,b.rounds_content,a.rectification_results
		             )z  group  by  task_id,group_id)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
		          <if test="isClinical !=null and isClinical !=''">
			  		and a.is_clinical = #{isClinical}
			 	 </if>
	)z
  </select>
  
  <select id="getProblemRepeatRate" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
 		 select problem_number,problem_repeat_number,case when problem_number=0 then 0 else CAST(problem_repeat_number AS DECIMAL(15,2))/problem_number end as wtcfl   from (
  			    select  sum(problem_number) as problem_number ,  sum(case when problem_number>1 then problem_number else 0  end) as problem_repeat_number  from (
	                      select  rounds_content,sum(problem_number) as problem_number
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             inner join (select task_id,group_id,count(1) as problem_number,rounds_content  from(
		               select a.task_id,a.group_id,b.rounds_content,a.rectification_results  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,b.rounds_content,a.rectification_results
		             )z  group  by  task_id,group_id,rounds_content)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
		             <if test="isClinical !=null and isClinical !=''">
				  		and a.is_clinical = #{isClinical}
				 	 </if>
		             group by  rounds_content
	                     )z
	      )z
  </select>
  
    <select id="getRoundsDeptScoreSituationHeader" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
  		select c.group_id,d.group_name from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
              left join ts_base_oa.med_rounds_task_group c on b.id=c.task_id  
              left join ts_base_oa.med_rounds_group d on c.group_id=d.id
              where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N'  and d.type=1
              and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
              <if test="isClinical !=null and isClinical !=''">
		  		and a.is_clinical = #{isClinical}
		 	 </if>
		 	 <if test="orgIdList != null and orgIdList.size() > 0">
				AND (
				<foreach collection="orgIdList" index="index" item="item" open="" separator="OR" close="">
					FIND_IN_SET(#{item}, a.rounds_org) > 0
				</foreach>
				)
			</if>
              group by c.group_id,d.group_name
  </select>
  
  
   		<select id="getRoundsDeptScoreSituationData" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
			 select  b.id as task_id,a.hosp_area,a.rounds_org_name,
				 	<if test="roundsGroupIdList != null and roundsGroupIdList.size() > 0">
				      <foreach collection="roundsGroupIdList" index="index"
				               item="item" open="" separator="," close="">
				               sum(CASE WHEN  c.group_id=#{item} THEN d.deduct_points else 0 END) as "${item}"
				      </foreach>
				      ,
				    </if>
				    sum(case when e.group_name not like '%护理组%'  then c.score else 0 end ) as  score
				    from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
	              inner join med_rounds_task_group c on b.id=c.task_id
	              left join med_rounds_group e on c.group_id=e.id  
	              left join (select task_id,group_id,sum(deduct_points) as deduct_points from(
	                select a.task_id,a.group_id,b.rounds_content,deduct_points  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
	                        group by a.task_id,a.group_id,b.rounds_content,deduct_points
	              )z  group  by  task_id,group_id)d on c.task_id=d.task_id and c.group_id=d.group_id
	              where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N'
	              		and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
	          <if test="isClinical !=null and isClinical !=''">
		  		and a.is_clinical = #{isClinical}
		 	 </if>
		 	 <if test="orgIdList != null and orgIdList.size() > 0">
				AND (
				<foreach collection="orgIdList" index="index" item="item" open="" separator="OR" close="">
					FIND_IN_SET(#{item}, a.rounds_org) > 0
				</foreach>
				)
			</if>
	       group by b.id,a.hosp_area,a.rounds_org_name  order by score desc
		 </select>
		 
		 
	 <select id="getOverdueNotRectified" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
				select a.id,a.hosp_area,a.rounds_org,a.rounds_org_name,
				<choose>	
					<when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
						DATEDIFF(day,b.rectification_deadline,to_char(SYSDATE , 'YYYY-MM-DD')) as overdue_day,
					</when>
					<otherwise>
						DATEDIFF(CURDATE(), b.rectification_deadline) AS overdue_day,
					</otherwise>
				</choose>
					sum(case when e.group_name not like '%护理组%'  then c.score else 0 end ) as score,
					sum(case when b.status=3 then problem_number else 0 end) as rectification_problem_number,
					sum(case when b.status=4 then problem_number else 0 end) as recheck_problem_number
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             left join med_rounds_group e on c.group_id=e.id
		             left join (select task_id,group_id,count(1) as problem_number,sum(case when rectification_results='已整改' then 1 else 0 end) as problem_rectification_number  from(
		               select a.task_id,a.group_id,b.rounds_content,a.rectification_results  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,b.rounds_content,a.rectification_results
		             )z  group  by  task_id,group_id)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
		              and b.status in (3,4) and 
		              <choose>
			              <when test="_databaseId=='dm' or _databaseId=='oracle' or _databaseId=='kingbase'">
								to_char(SYSDATE , 'YYYY-MM-DD') > b.rectification_deadline 
						  </when>
						  <otherwise>
								CURDATE() > b.rectification_deadline 
						   </otherwise>
					   </choose>
		               <if test="isClinical !=null and isClinical !=''">
				  		and a.is_clinical = #{isClinical}
				 	 </if>
  			   group  by  a.id,a.hosp_area,a.rounds_org,a.rounds_org_name,b.rectification_deadline  order  by overdue_day desc
   </select>
  
   <select id="getGroupProblemChart" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
		select e.group_name,rounds_content,count(problem_number) as problem_number
				from med_rounds_scheduling a inner join med_rounds_task b on a.id=b.scheduling_id  
		             inner join med_rounds_task_group c on b.id=c.task_id  
		             inner join med_rounds_group e on c.group_id=e.id
		             inner join (select task_id,group_id,count(1) as problem_number,rounds_content  from(
		               select a.task_id,a.group_id,rounds_content,a.rectification_results  from   med_rounds_task_group_rules a  inner join  med_rounds_group_rules  b on a.rules_id=b.id
		                       where deduct_points >0
		                       group by a.task_id,a.group_id,rounds_content,a.rectification_results
		             )z  group  by  task_id,group_id,rounds_content)d on c.task_id=d.task_id and c.group_id=d.group_id
		             where a.is_deleted='N' AND B.is_deleted='N' AND C.is_deleted='N' 
		             and a.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
		              <if test="groupName !=null and groupName !=''">
					  	and e.group_name  like concat('%',#{groupName},'%') 
					  </if>
		               <if test="isClinical !=null and isClinical !=''">
				  		and a.is_clinical = #{isClinical}
				 	 </if>
  			   group  by  e.group_name,rounds_content
  </select>
  
  <select id="getRectifiedCount" parameterType="cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard" resultType="Map">
  			SELECT 
			    base.id,
			    base.hosp_area,
			    base.rounds_org_name,
			    base.status,
			    COUNT(DISTINCT CONCAT(rules.group_id, '|', rules.rounds_content)) AS related_rules_count,
			    COUNT(DISTINCT CONCAT(rectified.group_id, '|', rectified.rounds_content)) AS rectified_count,
			    COUNT(DISTINCT CONCAT(partially.group_id, '|', partially.rounds_content)) AS partially_rectified_count,
			    COUNT(DISTINCT CONCAT(notRectified.group_id, '|', notRectified.rounds_content)) AS not_rectified_count
			FROM (
			    SELECT 
			        t1.id,
			        t2.hosp_area,
			        t2.rounds_org_name,
			        t1.status
			    FROM med_rounds_task t1
			    LEFT JOIN med_rounds_scheduling t2 
			        ON t1.scheduling_id = t2.id
			    WHERE 
			        t1.is_deleted = 'N' 
			        AND t2.is_deleted = 'N'  
			        AND t1.status > 2
			        AND t2.rounds_time between #{roundsTimeBegin}  and #{roundsTimeEnd}
			) AS base
			LEFT JOIN (
			    SELECT 
			        tt.task_id,
			        t.group_id,
			        t.rounds_content
			    FROM med_rounds_group_rules t
			    JOIN med_rounds_task_group_rules tt 
			        ON t.id = tt.rules_id
			    WHERE tt.deduct_points > 0
			) AS rules ON base.id = rules.task_id
			LEFT JOIN (
			    SELECT
			        tt.task_id,
			        t.group_id,
			        t.rounds_content
			    FROM med_rounds_task_group_rules tt
			    JOIN med_rounds_group_rules t 
			        ON t.id = tt.rules_id
			    WHERE tt.rectification_results = '已整改'
			) AS rectified ON base.id = rectified.task_id
			LEFT JOIN (
			    SELECT
			        tt.task_id,
			        t.group_id,
			        t.rounds_content
			    FROM med_rounds_task_group_rules tt
			    JOIN med_rounds_group_rules t 
			        ON t.id = tt.rules_id
			    WHERE tt.rectification_results = '部分整改'
			) AS partially ON base.id = partially.task_id
			LEFT JOIN (
			    SELECT
			        tt.task_id,
			        t.group_id,
			        t.rounds_content
			    FROM med_rounds_task_group_rules tt
			    JOIN med_rounds_group_rules t 
			        ON t.id = tt.rules_id
			    WHERE tt.rectification_results = '未整改'
			) AS notRectified ON base.id = notRectified.task_id
			GROUP BY base.id
  </select>
</mapper>