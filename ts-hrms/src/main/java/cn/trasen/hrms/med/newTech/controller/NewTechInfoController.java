package cn.trasen.hrms.med.newTech.controller;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.newTech.model.NewTechEevaluation;
import cn.trasen.hrms.med.newTech.model.NewTechInfo;
import cn.trasen.hrms.med.newTech.model.NewTechPatientInfo;
import cn.trasen.hrms.med.newTech.service.NewTechInfoService;
import cn.trasen.hrms.med.newTech.service.NewTechPatientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * @ClassName NewTechInfoController
 * @Description 
 * @date 2025-05-13 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "新技术新项目管理")
public class NewTechInfoController {

	private transient static final Logger logger = LoggerFactory.getLogger(NewTechInfoController.class);

	@Autowired
	private NewTechInfoService newTechInfoService;

	@Autowired
	private NewTechPatientInfoService newTechPatientInfoService;

	/**
	 * @Title saveNewTechInfo
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/newTechInfo/save")
	public PlatformResult<String> saveNewTechInfo(@RequestBody NewTechInfo record) {
		try {
			newTechInfoService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateNewTechInfo
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/newTechInfo/update")
	public PlatformResult<String> updateNewTechInfo(@RequestBody NewTechInfo record) {
		try {
			newTechInfoService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectNewTechInfoById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<NewTechInfo>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/newTechInfo/detailById/{id}")
	public PlatformResult<NewTechInfo> selectNewTechInfoById(@PathVariable String id) {
		try {
			NewTechInfo record = newTechInfoService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectNewTechInfoByTechCode
	 * @Description 根据伦理号查询详情
	 * @param id
	 * @return PlatformResult<NewTechInfo>
	 * @date 2025-05-19 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据伦理号查询详情", notes = "根据伦理号查询详情")
	@GetMapping("/api/newTechInfo/detailByTechCode/{techCode}")
	public PlatformResult<NewTechInfo> selectNewTechInfoByTechCode(@PathVariable String techCode) {
		try {
			NewTechInfo record = newTechInfoService.selectByNewTechCode(techCode);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteNewTechInfoById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/newTechInfo/delete/{id}")
	public PlatformResult<String> deleteNewTechInfoById(@PathVariable String id) {
		try {
			newTechInfoService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectNewTechInfoList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<NewTechInfo>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/newTechInfo/pageList")
	public DataSet<NewTechInfo> selectNewTechInfoPageList(Page page, NewTechInfo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		return newTechInfoService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @param request
	 * @param response
	 * @param page
	 * @param record
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/newTechInfo/export")
    public void export(HttpServletRequest request, HttpServletResponse response,Page page, NewTechInfo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		page.setPageSize(Integer.MAX_VALUE);
		String name = "新技术管理.xls";
		String templateUrl = "template/newTechInfo.xls";
		try {
			DataSet<NewTechInfo> dataSetList = newTechInfoService.getDataSetList(page, record);
            List<NewTechInfo> list = dataSetList.getRows();
            if (CollectionUtils.isNotEmpty(list)) {
            	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            	for(NewTechInfo tech : list){
            		//是否新技术：0-否，1-是
            		if(!ObjectUtils.isEmpty(tech.getIsNewTech()) && "0".equals(tech.getIsNewTech())){
            			tech.setIsNewTech("否");
            		} else if(!ObjectUtils.isEmpty(tech.getIsNewTech()) && "1".equals(tech.getIsNewTech())){
            			tech.setIsNewTech("是");
            		}
            		//是否限制性类技术：0-否，1-是
            		if(!ObjectUtils.isEmpty(tech.getIsRstdTech()) && "0".equals(tech.getIsRstdTech())){
            			tech.setIsRstdTech("否");
            		} else if(!ObjectUtils.isEmpty(tech.getIsRstdTech()) && "1".equals(tech.getIsRstdTech())){
            			tech.setIsRstdTech("是");
            		}
            		//状态：1-开展中，2-中止，3-转常规
            		if(!ObjectUtils.isEmpty(tech.getTechStatus()) && "1".equals(tech.getTechStatus())){
            			tech.setTechStatus("开展中");
            		} else if(!ObjectUtils.isEmpty(tech.getTechStatus()) && "2".equals(tech.getTechStatus())){
            			tech.setTechStatus("已中止");
            		} else if(!ObjectUtils.isEmpty(tech.getTechStatus()) && "3".equals(tech.getTechStatus())){
            			tech.setTechStatus("转常规");
            		}
            		//项目分类：1-Ⅰ类，2-Ⅱ类，3-Ⅲ类
            		if(!ObjectUtils.isEmpty(tech.getTechClass()) && "1".equals(tech.getTechClass())){
            			tech.setTechClass("I类临床新技术(成熟技术，院内首次开展)");
            		} else if(!ObjectUtils.isEmpty(tech.getTechClass()) && "2".equals(tech.getTechClass())){
            			tech.setTechClass("II类临床新技术(国内首创)");
            		} else if(!ObjectUtils.isEmpty(tech.getTechClass()) && "3".equals(tech.getTechClass())){
            			tech.setTechClass("III类临床新技术(国际首创)");
            		}
            		//技术类型：1-手术类，2-治疗操作类，3-检验检查类，4-其他类
            		if(!ObjectUtils.isEmpty(tech.getTechType()) && "1".equals(tech.getTechType())){
            			tech.setTechType("手术类");
            		} else if(!ObjectUtils.isEmpty(tech.getTechType()) && "2".equals(tech.getTechType())){
            			tech.setTechType("治疗操作类");
            		} else if(!ObjectUtils.isEmpty(tech.getTechType()) && "3".equals(tech.getTechType())){
            			tech.setTechType("检验检查类");
            		} else if(!ObjectUtils.isEmpty(tech.getTechType()) && "4".equals(tech.getTechType())){
            			tech.setTechType("其他类");
            		}
            		//导出日期
            		if(tech.getStartDate() != null){
            			tech.setStartDateStr(sdf.format(tech.getStartDate()));
            		}
            		if(tech.getEndDate() != null){
            			tech.setEndDateStr(sdf.format(tech.getEndDate()));
            		}
            		if(tech.getTransformDate() != null){
            			tech.setTransformDateStr(sdf.format(tech.getTransformDate()));
            		}
            	}
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

    }

	/**
	 * @Title selectNewTechInfoList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<NewTechInfo>
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/newTechInfo/list")
	public PlatformResult<List<NewTechInfo>> selectNewTechInfoList(NewTechInfo record) {
		String orgIds = record.getOrgIds();
		if(!ObjectUtils.isEmpty(orgIds)){
			record.setOrgIdList(Arrays.asList(orgIds.split(",")));
		}
		try {
			return PlatformResult.success(newTechInfoService.selectNewTechInfoList(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title getEevaluationList
	 * @Description 根据伦理号获取评估记录
	 * @date 2025-05-19 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据伦理号获取评估记录", notes = "根据伦理号获取评估记录")
	@GetMapping("/api/newTechInfo/getEevaluationList")
	public PlatformResult<List<NewTechEevaluation>> getEevaluationList(@RequestParam("techCode")@ApiParam(value = "新技术伦理号", required = true) String techCode) {
		Assert.hasLength(techCode, "新技术伦理号不能为空.");
		try {
			return PlatformResult.success(newTechInfoService.selectEevaluationList(techCode));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title getEevaluationList
	 * @Description 根据伦理号获取患者列表
	 * @date 2025-05-20 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据伦理号获取患者列表", notes = "根据伦理号获取患者列表")
	@GetMapping("/api/newTechInfo/getPatientList")
	public PlatformResult<List<NewTechPatientInfo>> getPatientList(@RequestParam("techCode")@ApiParam(value = "新技术伦理号", required = true) String techCode) {
		Assert.hasLength(techCode, "新技术伦理号不能为空.");
		try {
			return PlatformResult.success(newTechPatientInfoService.selectByNewTechCode(techCode));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title insertNewTechData
	 * @Description 新增新技术准入记录
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增新技术准入记录", notes = "新增新技术准入记录")
	@PostMapping("/api/newTechInfo/insertNewTechData")
	public void insertNewTechData(HttpServletRequest request) {
		Map<String, Object> formData = new HashMap<>();
		Enumeration<String> enu = request.getParameterNames();
		while (enu.hasMoreElements()) {
			String key = (String) enu.nextElement();
			formData.put(key, request.getParameter(key));
		}
		try {
			logger.info("==========新技术准入参数 formData: {}", formData.toString());
			newTechInfoService.insertNewTechData(formData);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	/**
	 * @Title transferNormal
	 * @Description 新技术转常规回调
	 * @date 2025-05-13 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新技术转常规回调", notes = "新技术转常规回调")
	@PostMapping("/api/newTechInfo/transferNormal")
	public void transferNormal(HttpServletRequest request) {
		Map<String, Object> formData = new HashMap<>();
		Enumeration<String> enu = request.getParameterNames();
		while (enu.hasMoreElements()) {
			String key = (String) enu.nextElement();
			formData.put(key, request.getParameter(key));
		}
		try {
			logger.info("==========新技术转常规回调参数 formData: {}", formData.toString());
			newTechInfoService.transferNormal(formData);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	/**
	 * @Title evaluation
	 * @Description 新技术评估通过回调
	 * @date 2025-05-14 11:52:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新技术评估回调", notes = "新技术评估回调")
	@PostMapping("/api/newTechInfo/evaluation")
	public void evaluation(HttpServletRequest request) {
		Map<String, Object> formData = new HashMap<>();
		Enumeration<String> enu = request.getParameterNames();
		while (enu.hasMoreElements()) {
			String key = (String) enu.nextElement();
			formData.put(key, request.getParameter(key));
		}
		try {
			logger.info("==========新技术评估回调参数 formData: {}", formData.toString());
			newTechInfoService.evaluation(formData);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
	
}
