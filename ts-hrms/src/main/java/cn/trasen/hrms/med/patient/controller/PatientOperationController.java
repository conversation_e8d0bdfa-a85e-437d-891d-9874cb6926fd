package cn.trasen.hrms.med.patient.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.patient.model.PatientOperation;
import cn.trasen.hrms.med.patient.service.PatientOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName PatientOperationController
 * @Description TODO
 * @date 2025��4��8�� ����4:38:18
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "PatientOperationController")
public class PatientOperationController {

	private transient static final Logger logger = LoggerFactory.getLogger(PatientOperationController.class);

	@Autowired
	private PatientOperationService patientOperationService;

	/**
	 * @Title savePatientOperation
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/patientOperation/save")
	public PlatformResult<String> savePatientOperation(@RequestBody PatientOperation record) {
		try {
			patientOperationService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updatePatientOperation
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/patientOperation/update")
	public PlatformResult<String> updatePatientOperation(@RequestBody PatientOperation record) {
		try {
			patientOperationService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectPatientOperationById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<PatientOperation>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/patientOperation/{id}")
	public PlatformResult<PatientOperation> selectPatientOperationById(@PathVariable String id) {
		try {
			PatientOperation record = patientOperationService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deletePatientOperationById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/patientOperation/delete/{id}")
	public PlatformResult<String> deletePatientOperationById(@PathVariable String id) {
		try {
			patientOperationService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectPatientOperationList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<PatientOperation>
	 * @date 2025��4��8�� ����4:38:18
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/patientOperation/list")
	public DataSet<PatientOperation> selectPatientOperationList(Page page, PatientOperation record) {
		return patientOperationService.getDataSetList(page, record);
	}
}
