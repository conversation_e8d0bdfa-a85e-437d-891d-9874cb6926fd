package cn.trasen.hrms.med.major.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

@Table(name = "med_major_events_patn")
@Setter
@Getter
public class MajorEventsPatn {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 医务大事记ID
     */
    @Column(name = "events_id")
    @ApiModelProperty(value = "医务大事记ID")
    private String eventsId;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    @Column(name = "inp_no")
    @ApiModelProperty(value = "住院号")
    private String inpNo;

    /**
     * 床号
     */
    @Excel(name = "床号")
    @ApiModelProperty(value = "床号")
    private String bedno;

    /**
     * 患者姓名
     */
    @Excel(name = "患者姓名")
    @Column(name = "patn_name")
    @ApiModelProperty(value = "患者姓名")
    private String patnName;

    /**
     * 患者性别
     */
    @Excel(name = "性别")
    @Column(name = "patn_gend")
    @ApiModelProperty(value = "患者性别")
    private String patnGend;

    /**
     * 所属科室
     */
    @Excel(name = "所属科室")
    @Column(name = "dept_name")
    @ApiModelProperty(value = "所属科室")
    private String deptName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
}