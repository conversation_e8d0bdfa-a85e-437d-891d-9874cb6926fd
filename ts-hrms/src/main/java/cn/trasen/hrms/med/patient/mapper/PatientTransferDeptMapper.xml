<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.patient.dao.PatientTransferDeptMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.patient.model.PatientTransferDept">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patn_id" jdbcType="VARCHAR" property="patnId" />
    <result column="patn_no" jdbcType="VARCHAR" property="patnNo" />
    <result column="transfer_date" jdbcType="TIMESTAMP" property="transferDate" />
    <result column="out_dept_id" jdbcType="VARCHAR" property="outDeptId" />
    <result column="out_dept_name" jdbcType="VARCHAR" property="outDeptName" />
    <result column="in_dept_id" jdbcType="VARCHAR" property="inDeptId" />
    <result column="in_dept_name" jdbcType="VARCHAR" property="inDeptName" />
    <result column="transfer_reason" jdbcType="VARCHAR" property="transferReason" />
    <result column="transfer_diagnosis" jdbcType="VARCHAR" property="transferDiagnosis" />
    <result column="cancel_bit" jdbcType="VARCHAR" property="cancelBit" />
    <result column="finish_bit" jdbcType="VARCHAR" property="finishBit" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
</mapper>