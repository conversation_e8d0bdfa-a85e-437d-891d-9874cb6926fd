package cn.trasen.hrms.med.qua.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;

/**
 * @ClassName MedQuaAuthService
 * @Description TODO
 * @date 2024��11��29�� ����11:43:33
 * <AUTHOR>
 * @version 1.0
 */
public interface QuaAuthCfgService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��29�� ����11:43:33
	 * <AUTHOR>
	 */
	Integer save(QuaAuthCfg record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��29�� ����11:43:33
	 * <AUTHOR>
	 */
	Integer update(QuaAuthCfg record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��29�� ����11:43:33
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedQuaAuth
	 * @date 2024��11��29�� ����11:43:33
	 * <AUTHOR>
	 */
	QuaAuthCfg selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedQuaAuth>
	 * @date 2024��11��29�� ����11:43:33
	 * <AUTHOR>
	 */
	DataSet<QuaAuthCfg> getDataSetList(Page page, QuaAuthCfg record);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据项目编码查询状态正常的单条数据
	  -- 作者: GW
	  -- 创建时间: 2024年12月2日
	  -- @param code
	  -- @return
	  -- =============================================
	 */
	QuaAuthCfg getOneNormalByItemCode(String itemCode);
	
	List<QuaAuthCfg> getByItemCode(String itemCode);

	List<QuaAuthCfg> getQuaAuthCfgList(List<String> ids);

	void syncPlatformOperationDict();

	void syncQuaCfgLvHosp();

}
