package cn.trasen.hrms.med.cslt.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.hrms.med.cslt.model.CsltScdu;
import cn.trasen.hrms.med.cslt.model.CsltScduParam;
import cn.trasen.hrms.med.cslt.model.CsltToHisDto;
import tk.mybatis.mapper.common.Mapper;

public interface CsltScduMapper extends Mapper<CsltScdu> {
	int batchInsert(@Param("list")List<CsltScdu> list);
	
	List<CsltToHisDto> getPushHisData();
	
	
	Integer selectCsltScduDeptCount(CsltScduParam record);
	
	List<CsltScdu> selectCsltScduPageList(CsltScduParam record,Page page);
	
	List<Map<String, Object>> selectByParam(Page page,CsltScduParam record);
	
}