package cn.trasen.hrms.med.rounds.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.med.rounds.dao.RoundsGroupMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsSchedulingGroupMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsSchedulingMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsTaskGroupMapper;
import cn.trasen.hrms.med.rounds.model.RoundsScheduling;
import cn.trasen.hrms.med.rounds.model.RoundsSchedulingGroup;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroup;
import cn.trasen.hrms.med.rounds.service.RoundsSchedulingGroupService;
import cn.trasen.hrms.med.rounds.service.RoundsSchedulingService;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.SendMessageUtils;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RoundsSchedulingServiceImpl
 * @Description TODO
 * @date 2025��3��6�� ����11:02:47
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RoundsSchedulingServiceImpl implements RoundsSchedulingService {

	@Autowired
	private RoundsSchedulingMapper mapper;
	
	@Autowired
	private RoundsSchedulingGroupService schedulingGroupService;
	
	@Autowired
	private RoundsSchedulingGroupMapper schedulingGroupMapper;
	
	@Autowired
    DictItemFeignService dictItemFeignService;
	
	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;
	
	@Autowired
    private RoundsGroupMapper  roundsGroupMapper;
	
	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	@Autowired
	private RoundsTaskGroupMapper roundsTaskGroupMapper;
	
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RoundsScheduling record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		if(CollectionUtils.isNotEmpty(record.getSchedulingGroupList())) {//新增排班小组信息
			for(RoundsSchedulingGroup schedulingGroup :  record.getSchedulingGroupList()) {
				schedulingGroup.setSchedulingId(record.getId());
				if(!StringUtils.isEmpty(schedulingGroup.getGroupUserCode())) {//过滤排班组用户,没有人的情况
					schedulingGroupService.save(schedulingGroup);	
				}
			}
		}
		record.setStatus("0");//默认状态0
	    
		//消息推送:查询消息推送排班负责人,暂时不需要推送排版负责人
		/*
		Example example = new Example(RoundsGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("isEnable", "1");//启用的小组
		criteria.andEqualTo("type", "1");//统计得分的小组,院领导可能不需要
		List<RoundsGroup> roundsGroupList = roundsGroupMapper.selectByExample(example);
		
		String content = "新增"+ DateUtil.format(record.getRoundsTime(), "yyyy-MM-dd")+"日"+record.getRoundsOrgName()+"查房,请在此之前及时完成查房排班";
		if(CollectionUtils.isNotEmpty(roundsGroupList)) {
			for(RoundsGroup roundsGroup : roundsGroupList) {
				if(!StringUtils.isEmpty(roundsGroup.getSchedulingCode())) {
					//EmployeeResp employeeResp = hrmsEmployeeFeignService.getEmployeeDetailByCode(roundsGroup.getSchedulingCode()).getObject();//根据id获取人员信息
					String employeePhoneNumber =  mapper.selectEmployeePhoneNumberById(roundsGroup.getSchedulingCode());//获取该用户手机号码
					if(!StringUtils.isEmpty(employeePhoneNumber)) {
						SendMessageUtils.sendMessageHnsrmyy(employeePhoneNumber,content,"系统管理员","1");
					}
				}
			}
		}
		*/
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RoundsScheduling record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(CollectionUtils.isNotEmpty(record.getSchedulingGroupList())) {//新增排班小组信息
			schedulingGroupService.deleteBySchedulingId(record.getId());//先删除原有排版小组信息，再新增
			for(RoundsSchedulingGroup schedulingGroup :  record.getSchedulingGroupList()) {
				schedulingGroup.setSchedulingId(record.getId());
				if(!StringUtils.isEmpty(schedulingGroup.getGroupUserCode())) {//过滤排班组用户,没有人的情况
					schedulingGroupService.save(schedulingGroup);
				}
			}
		}
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	@Transactional(readOnly = false)
	@Override
	public Integer updateSchedulingGroupUserCode(RoundsScheduling record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(CollectionUtils.isNotEmpty(record.getSchedulingGroupList())) {//更新排版小组用户信息
			for(RoundsSchedulingGroup schedulingGroup :  record.getSchedulingGroupList()) {
				if(!StringUtils.isEmpty(schedulingGroup.getId())) {
					schedulingGroupService.update(schedulingGroup);//更新组用户
					
					RoundsTaskGroup roundsTaskGroup = new RoundsTaskGroup();
					roundsTaskGroup.setSchedulingId(schedulingGroup.getSchedulingId());//排班id
					roundsTaskGroup.setGroupId(schedulingGroup.getGroupId());//小组id
					roundsTaskGroup.setGroupUserCode(schedulingGroup.getGroupUserCode());
					roundsTaskGroup.setGroupUserName(schedulingGroup.getGroupUserName());
					roundsTaskGroupMapper.updateGroupUserCodeByScheduling(roundsTaskGroup);//更新任务组用户信息
				}
			}
		}
		return mapper.updateByPrimaryKeySelective(record);
	}
	

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RoundsScheduling record = new RoundsScheduling();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RoundsScheduling selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RoundsScheduling> getDataSetList(Page page, RoundsScheduling record) {
		Example example = new Example(RoundsScheduling.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RoundsScheduling> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<RoundsScheduling> getPageList(Page page, RoundsScheduling record){
		// TODO Auto-generated method stub
		List<RoundsScheduling> records = mapper.getPageList(page, record);
		Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
		if(CollectionUtils.isNotEmpty(records)) {
			for(RoundsScheduling roundsScheduling : records) {
				roundsScheduling.setHospAreaText(hospAreaMap.get(roundsScheduling.getHospArea()));//获取院区字典值
				if("0".equals(roundsScheduling.getStatus())) {
					if (roundsScheduling.getRoundsTime().after(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd")))){//查房日期在当天之后
						roundsScheduling.setSchedulingStatus("0");
					}else {
						roundsScheduling.setSchedulingStatus("2");
					}
				}else{
					roundsScheduling.setSchedulingStatus("1");//查房状态0待查房,1取消,2已查房
				}
				
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public List<RoundsScheduling> getByCondition(RoundsScheduling record) {
		// TODO Auto-generated method stub
		return mapper.getByCondition(record);
	}

	@Override
	public List<VueTableEntity> selectExportRoundsSchedulingHeader(RoundsScheduling record) {
		// TODO Auto-generated method stub
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		retVueTableEntity.add(new VueTableEntity("周次","weekNumber",null,10,null));
		retVueTableEntity.add(new VueTableEntity("查房日期","weekValue",null,15,null));//用周几做字段
		retVueTableEntity.add(new VueTableEntity("院区","hospAreaText",null,15,null));
		retVueTableEntity.add(new VueTableEntity("查房科室","roundsOrgName",null,15,null));
		List<String>  schedulingGroupNameList =   mapper.getSchedulingGroupHeaderList(record);//查询数据所有的组名称
		for(int i=0;i<schedulingGroupNameList.size();i++) {
			retVueTableEntity.add(new VueTableEntity(schedulingGroupNameList.get(i),schedulingGroupNameList.get(i),null,20,null));//添加商品表头
		}
		return retVueTableEntity;
	}

	@Override
	public List<Map<String, Object>> getExportRoundsScheduling(RoundsScheduling record) {
		// TODO Auto-generated method stub
		List<String> schedulingGroupUserName = mapper.getSchedulingGroupHeaderList(record);//查询组名称
		record.setSchedulingGroupNameList(schedulingGroupUserName);
		return mapper.getExportRoundsScheduling(record);
	}
	
	
	private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
	
	
	 private void sendMessage(String content,List<String> receiverList,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)//通知类型   1 短信   2邮件   3微信推送
	                //.receiver(receiver)  //接收人
	                .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-oa/civil-affairs-objects/civil-affairs-objects").source("行政查房")//跳转前端地址
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }

	@Override
	public void reminderRoundsSchedulingMessage() {
		 // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        // 加1天
        calendar.add(Calendar.DATE, 1);
        // 转换为Date类型
        Date tomorrow = calendar.getTime();
		// TODO Auto-generated method stub
		Example example = new Example(RoundsScheduling.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("roundsTime", DateUtil.format(tomorrow, "yyyy-MM-dd"));//明天查房
		criteria.andEqualTo("status", "0");//未取消
		List<RoundsScheduling> schedulingList = mapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(schedulingList)) {
			for(RoundsScheduling scheduling : schedulingList) {
				Example example2 = new Example(RoundsSchedulingGroup.class);
				Example.Criteria criteria2 = example2.createCriteria();
				criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
				criteria2.andEqualTo("schedulingId",scheduling.getId());//排班id
				List<RoundsSchedulingGroup> schedulingGroupList = schedulingGroupMapper.selectByExample(example2);
				Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
				//String content = "：您明日【"+DateUtil.format(scheduling.getRoundsTime(), "yyyy年MM月dd日")+"】需要前往【"+hospAreaMap.get(scheduling.getHospArea())+"/"+scheduling.getRoundsOrgName()+"】进行医疗行政查房。";
				if(CollectionUtils.isNotEmpty(schedulingGroupList)) {
					for(RoundsSchedulingGroup schedulingGroup : schedulingGroupList) {
						if(!StringUtils.isEmpty(schedulingGroup.getGroupUserCode())) {
							// 使用 split 方法按逗号分隔字符串
					        String[] groupUserCodeArray = schedulingGroup.getGroupUserCode().split(",");
					        // 将数组转换为 List
					        List<String> groupUserCodeList = Arrays.asList(groupUserCodeArray);
					        // 遍历 List 
					        for (String groupUserCode : groupUserCodeList) {
					        	//EmployeeResp employeeResp = hrmsEmployeeFeignService.getEmployeeDetailByCode(schedulingGroup.getGroupUserCode()).getObject();//根据id获取人员信息,定时任务缺少token
					        	Map<String,Object> employeeMap =  mapper.selectEmployeePhoneNumberById(groupUserCode);//获取用户手机号码
								if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
									String content = "";
									if(employeeMap.containsKey("employeeName") && !StringUtils.isEmpty(employeeMap.get("employeeName").toString())) {
										 content = "尊敬的【"+employeeMap.get("employeeName").toString()+"】"+"：您明日【"+DateUtil.format(scheduling.getRoundsTime(), "yyyy年M月d日")+"】需要前往【"+hospAreaMap.get(scheduling.getHospArea())+"/"+scheduling.getRoundsOrgName()+"】进行医疗行政查房。";
									}else {
										 content = "您明日【"+DateUtil.format(scheduling.getRoundsTime(), "yyyy年M月d日")+"】需要前往【"+hospAreaMap.get(scheduling.getHospArea())+"/"+scheduling.getRoundsOrgName()+"】进行医疗行政查房。";
									}
									SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
								}
					        }
						}
					}
				}
			}
		}
	}

	@Override
	public DataSet<Map<String, Object>> getSchedulingOrganizationList(Page page, Map<String, Object> parameMap) {
		// TODO Auto-generated method stub
				List<Map<String, Object>> records = mapper.getSchedulingOrganizationList(page, parameMap);
				return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	
}
