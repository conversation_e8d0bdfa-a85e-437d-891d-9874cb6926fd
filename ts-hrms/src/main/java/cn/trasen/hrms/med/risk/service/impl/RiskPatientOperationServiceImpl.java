package cn.trasen.hrms.med.risk.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.comm.model.FieldMapping;
import cn.trasen.hrms.med.comm.service.FieldMappingService;
import cn.trasen.hrms.med.risk.dao.RiskPatientOperationMapper;
import cn.trasen.hrms.med.risk.model.RiskPatientOperation;
import cn.trasen.hrms.med.risk.service.RiskPatientOperationService;
import cn.trasen.hrms.med.rounds.dao.RoundsSchedulingMapper;
import cn.trasen.hrms.med.rounds.model.RoundsScheduling;
import cn.trasen.hrms.med.rounds.model.RoundsTask;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import cn.trasen.hrms.utils.SendMessageUtils;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RiskPatientOperationServiceImpl
 * @Description TODO
 * @date 2025��4��29�� ����9:43:03
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RiskPatientOperationServiceImpl implements RiskPatientOperationService {

	@Autowired
	private RiskPatientOperationMapper mapper;

	@Autowired
	private RoundsSchedulingMapper schedulingMapper;
	
	@Autowired
    DictItemFeignService dictItemFeignService;
	
	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
	FieldMappingService fieldMappingService;
	
	@Value("${optSendMessage:}")
	private String optSendMessage; //消息推送账号
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(RiskPatientOperation record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RiskPatientOperation record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RiskPatientOperation record = new RiskPatientOperation();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RiskPatientOperation selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RiskPatientOperation> getDataSetList(Page page, RiskPatientOperation record) {
		Example example = new Example(RiskPatientOperation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RiskPatientOperation> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void updateOrSaveFourOperation() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT  A.ID,A.JZSS,A.SNO,A.INPATIENT_ID AS PATN_ID,A.INPATIENT_NO AS PATN_NO,D.NAME, BED.BED_NO,D.BIRTHDAY,CASE WHEN D.SEX=1 THEN '男'  WHEN D.SEX=2 THEN '女' ELSE '未知' END AS SEX,A.SQDJCZY AS APP_DOC_ID,EMP.NAME AS APP_DOC_NAME, "
    				+ "         A.DEPTID AS DEPT_ID,DEPT.NAME AS  DEPT_NAME,DEPT.ZXKSMLID AS HOSP_AREA,A.YSSSRQ AS  OPERATION_DATE,A.YSSS AS OPERATION_NAME,A.YSSS_ICD9 AS   OPERATION_CODE,'四级'   AS INTERNAL_LEVEL,   "
    				+ "        A.SHZD AS OPERATION_AFTER_DIAGNOSIS,A.ZDYS AS ZDYS_CODE,EMP2.NAME AS ZDYS_NAME,EMP2.TECHNICAL_NAME AS ZDYS_TITLE,A.MZYS,A.BDELETE,BM.SJSS AS NATIONAL_LEVEL   "
    				+ "         FROM  ODSZYV10.SS_APPRECORD A  LEFT JOIN ODSMZV10.BASE_SS_CZBMB BM ON A.YSSS_ICD9=BM.SSCODE AND BM.DELETE_BIT=0   "
    				+ "         LEFT JOIN ODSZYV10.ZY_INPATIENT C ON A.INPATIENT_ID=C.INPATIENT_ID  "
    				+ "         LEFT JOIN ODSZYV10.BASE_PATIENT_PROPERTY D ON C.PATIENT_ID=D.PATIENT_ID  "
    				+ "         LEFT JOIN  ODSZYV10.ZY_BEDDICTION  BED ON C.BED_ID=BED.BED_ID  "
    				+ "         LEFT JOIN  ODSZYV10.BASE_DEPT_PROPERTY DEPT ON A.DEPTID=DEPT.DEPT_ID   "
    				+ "          LEFT JOIN  ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP ON A.SQDJCZY=EMP.EMPLOYEE_ID  "
    				+ "          LEFT JOIN  ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP2 ON A.ZDYS=EMP2.EMPLOYEE_ID   "
    				+ " ");
    		sb.append("  WHERE A.APBJ=1 AND BM.BY2 =4 AND A.YSSSRQ > TRUNC(SYSDATE -1)       "
    				+ " ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<RiskPatientOperation> operationList = HnsrmyyHisJdbcUtil.queryFourOperationSyncHis(sb.toString());//执行语句返回结果
    		log.info("===========operationList:"+operationList.size());
    		if(CollUtil.isNotEmpty(operationList)){
    			for(RiskPatientOperation riskPatientOperation : operationList) {
    				RiskPatientOperation record = mapper.selectByPrimaryKey(riskPatientOperation.getId());//根据患者id查询是否已经存在患者数据，存在则更新;
    				if(record != null) {
    					riskPatientOperation.setUpdateDate(DateUtil.date());
    					mapper.updateByPrimaryKeySelective(riskPatientOperation);
    				}else {
    					riskPatientOperation.setIsDeleted("N");
    					riskPatientOperation.setCreateDate(DateUtil.date());
    					riskPatientOperation.setUpdateDate(DateUtil.date());
    					mapper.insertSelective(riskPatientOperation);
    				}
    			}
    		}
    		
    		//手术存在取消  需要更新状态
    		Example example = new Example(RiskPatientOperation.class);
    		Example.Criteria criteria = example.createCriteria();
    		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
    		criteria.andBetween("operationDate", DateUtil.format(DateUtil.offsetDay(new Date(), -31), "yyyy-MM-dd"), DateUtil.format(DateUtil.offsetDay(new Date(), 7), "yyyy-MM-dd") );
    		List<RiskPatientOperation> list = mapper.selectByExample(example);
    		
    		String[] ids = new String[list.size()];
    		
    		for (int j = 0; j < list.size(); j++) {
    			ids[j] = list.get(j).getId();
			}
    		
    		String idsStr = Arrays.stream(ids).map(s -> "'" + s.replace("\\", "\\\\").replace("'", "\\'") + "'")
					.collect(Collectors.joining(","));
    		
    		StringBuilder sb2 = new StringBuilder();
    		sb2.append(" SELECT A.ID,A.BDELETE FROM  ODSZYV10.SS_APPRECORD A");
    		sb2.append(" WHERE A.ID in (").append(idsStr).append(")");
    		
    		log.info("===========sql2:"+sb2.toString());
    		
    		List<RiskPatientOperation> operationList2 = HnsrmyyHisJdbcUtil.queryFourOperationSyncHis2(sb2.toString());
    		
    		if(CollUtil.isNotEmpty(operationList2)){
    			for(RiskPatientOperation riskPatientOperation : operationList2) {
    				mapper.updateByPrimaryKeySelective(riskPatientOperation);
    			}
    		}
    		
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库四级手术数据异常：" + e.getMessage());
    	}
	}
	
	
	@Override
	public void riskOperationMessage() {
		// TODO Auto-generated method stub
		List<RiskPatientOperation> patientOperationList   =  mapper.selectOperationMessageData();
		if(CollectionUtils.isNotEmpty(patientOperationList)) {
			for(RiskPatientOperation operation : patientOperationList) {
				if(!StringUtils.isEmpty(operation.getAppDocId()) && !StringUtils.isEmpty(operation.getZdysCode()) && operation.getZdysCode().equals(operation.getAppDocId())) {//申请人=主刀医生,只需要发生主刀医生一次
					//String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",operation.getAppDocId()));//his用户id，转换为医务用户id
					Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(operation.getAppDocId());//根据用户信息获取用户手机号码
					if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
						String content =employeeMap.get("employeeName").toString() +"：你好，计划于"+DateUtil.format(operation.getOperationDate(), "yyyy年MM月dd日HH时")+"，由"+operation.getZdysName()+"医师为患者"+operation.getName()+"("+operation.getSex()+","+operation.getAge()+","+operation.getPatnNo()+")"
										+"实施【"+operation.getOperationName()+"】为院内四级手术，尚未完成四级手术多学科讨论，请您务必于术前完善多学科讨论，做好相应术前准备，确保手术顺利开展！";
						SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
						//SendMessageUtils.sendMessageHnsrmyy("13319560606",content,"系统管理员","1");//测试，发给王大志
						//SendMessageUtils.sendMessageHnsrmyy("17388950700",content,"系统管理员","1");//测试
					}
				}else {
					if(!StringUtils.isEmpty(operation.getAppDocId())) {//申请人
						//String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",operation.getAppDocId()));//his用户id，转换为医务用户id
						Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(operation.getAppDocId());//获取用户手机号码
						if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
							String content =employeeMap.get("employeeName").toString() +"：你好，计划于"+DateUtil.format(operation.getOperationDate(), "yyyy年MM月dd日HH时")+"，由"+operation.getZdysName()+"医师为患者"+operation.getName()+"("+operation.getSex()+","+operation.getAge()+","+operation.getPatnNo()+")"
									+"实施【"+operation.getOperationName()+"】为院内四级手术，尚未完成四级手术多学科讨论，请您务必于术前完善多学科讨论，做好相应术前准备，确保手术顺利开展！";
							SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
							//SendMessageUtils.sendMessageHnsrmyy("13319560606",content,"系统管理员","1");//测试，发给王大志
							//SendMessageUtils.sendMessageHnsrmyy("17388950700",content,"系统管理员","1");//测试
						}
					}
					
					if(!StringUtils.isEmpty(operation.getZdysCode())) {//主刀医生
						//String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",operation.getZdysCode()));//his用户id，转换为医务用户id
						Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(operation.getZdysCode());//获取用户手机号码
						if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
							String content =employeeMap.get("employeeName").toString() +"：你好，计划于"+DateUtil.format(operation.getOperationDate(), "yyyy年MM月dd日HH时")+"，由"+operation.getZdysName()+"医师为患者"+operation.getName()+"("+operation.getSex()+","+operation.getAge()+","+operation.getPatnNo()+")"
									+"实施【"+operation.getOperationName()+"】为院内四级手术，尚未完成四级手术多学科讨论，请您务必于术前完善多学科讨论，做好相应术前准备，确保手术顺利开展！";
							SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
							//SendMessageUtils.sendMessageHnsrmyy("13319560606",content,"系统管理员","1");//测试，发给王大志
							//SendMessageUtils.sendMessageHnsrmyy("17388950700",content,"系统管理员","1");//测试
						}
					}
				}
			}
		}
	}
	
	@Override
	public void sendSsMessage() {
		
		List<RiskPatientOperation> patientOperationList  =  mapper.selectOperationMessageData();
		
		if(CollectionUtils.isNotEmpty(patientOperationList)) {
			
			//计算台数
			Map<String, Long> collect = patientOperationList.stream()
            .collect(Collectors.groupingBy(
                RiskPatientOperation::getZdysCode, // 按 zdysCode 分组
                Collectors.counting()              // 统计每组数量
            ));
			
			for(RiskPatientOperation operation : patientOperationList) {
				if(!StringUtils.isEmpty(operation.getZdysCode())) {//主刀医生
					//String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",operation.getZdysCode()));//his用户id，转换为医务用户id
					Map<String,Object> employeeMap = schedulingMapper.selectEmployeePhoneNumberById(operation.getZdysCode());//获取用户手机号码
					if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
						
						StringBuffer content = new StringBuffer();
						content.append(employeeMap.get("employeeName").toString()).append("主刀医生，");
						content.append("您已申请").append(DateUtil.format(operation.getOperationDate(), "yyyy年MM月dd日"));
						content.append("开展【院内四级手术】").append(collect.get(operation.getZdysCode())).append("台");
						content.append("，请您及时完成”四级手术术前多学科讨论结论记录”，21点将再次统计完成情况，未按时完成者将予以通报和记分处罚，敬请知晓！");
						
						SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content.toString(),"系统管理员","1");
						
						//SendMessageUtils.sendMessageHnsrmyy("13319560606",content.toString(),"系统管理员","1");//测试，发给王大志
						//SendMessageUtils.sendMessageHnsrmyy("13874809285",content.toString(),"系统管理员","1");//测试
						//SendMessageUtils.sendMessageHnsrmyy("13787088641",content.toString(),"系统管理员","1");//测试 李倩
					}
				}
			}
		}
	}

	@Override
	public void riskOperationLeaderMessage() {
		// TODO Auto-generated method stub
		Map<String,Object> resultMap   =  mapper.selectOperationLeaderMessageData();
		//optSendMessage = "admin";
		// 使用 split 方法按逗号分隔字符串
        String[] optSendMessageCode = optSendMessage.split(",");
        // 将数组转换为 List
        List<String> optSendMessageCodeList = Arrays.asList(optSendMessageCode);
        // 遍历 List 
        if(!"0".equals(resultMap.get("hj").toString())) {
        	if(CollectionUtils.isNotEmpty(optSendMessageCodeList)) {
		        for (String userCode : optSendMessageCodeList) {//账号
					Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(userCode);//获取用户手机号码
					if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
						String  rq =  DateUtil.format(DateUtil.parse(resultMap.get("rq").toString()), "yyyy年MM月dd日");
						String content =employeeMap.get("employeeName").toString() +"：截止至"+DateUtil.format(new Date(), "HH时")+"，经统计，"+rq+"计划开展【院内四级手术】有"+resultMap.get("hj")+"台，已完成术前多学科讨论"+resultMap.get("ytl")+"台，仍有"+resultMap.get("wtl")+"台未完成。详细情况登录医务管理系统进行查看，请您督导！";
						SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
					}
				}
        	}
        }
	}

	@Override
	public DataSet<RiskPatientOperation> selectPageList(Page page, RiskPatientOperation record) {
		// TODO Auto-generated method stub
		List<RiskPatientOperation> records = mapper.selectPageList(page, record);
		Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
		if(CollectionUtils.isNotEmpty(records)) {
			for(RiskPatientOperation riskPatientOperation : records) {
				riskPatientOperation.setHospAreaText(hospAreaMap.get(riskPatientOperation.getHospArea()));//获取院区字典值
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }
}
