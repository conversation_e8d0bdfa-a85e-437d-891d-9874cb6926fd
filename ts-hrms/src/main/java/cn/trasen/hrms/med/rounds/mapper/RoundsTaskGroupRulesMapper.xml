<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.rounds.dao.RoundsTaskGroupRulesMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="scheduling_id" jdbcType="VARCHAR" property="schedulingId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="rules_id" jdbcType="VARCHAR" property="rulesId" />
    <result column="existing_problems" jdbcType="VARCHAR" property="existingProblems" />
    <result column="deduct_points" jdbcType="DECIMAL" property="deductPoints" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="getByTaskGroupId" resultType="cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules" parameterType="cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules">
		 select a.*,b.group_name as groupName,c.rounds_project as roundsProject,c.rounds_content as roundsContent,c.score_value as scoreValue,
		 		 c.scoring_rubric as scoringRubric,c.deduct_points_limit as deductPointsLimit,c.source
		 		from  med_rounds_task_group_rules a 
				inner join   med_rounds_group b on a.group_id=b.id
				inner join  med_rounds_group_rules c  on a.rules_id=c.id
 			   where a.is_deleted='N' 
 			    <if test="taskId !=null and taskId !=''">
			  		and a.task_id=#{taskId}
			 	 </if>
 			   <if test="schedulingId !=null and schedulingId !=''">
			  		 and a.scheduling_id=#{schedulingId}
			 	 </if>
			 	 <if test="groupId !=null and groupId !=''">
			  		and a.group_id=#{groupId}
			 	 </if>
		 			     
	  </select>
  
</mapper>