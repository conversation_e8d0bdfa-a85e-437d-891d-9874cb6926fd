package cn.trasen.hrms.med.qua.service;

import java.util.List;
import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.comm.model.CategoryDict;
import cn.trasen.hrms.med.qua.model.MgtAuditParam;
import cn.trasen.hrms.med.qua.model.QuaAuthMgt;
import cn.trasen.hrms.med.qua.model.QuaAuthMgtParam;
import cn.trasen.hrms.med.qua.model.QuaMgtAndDetl;
import cn.trasen.hrms.med.qua.model.QuaMgtSchedule;

/**
 * @ClassName MedQuaMgtService
 * @Description TODO
 * @date 2024��12��4�� ����4:27:51
 * <AUTHOR>
 * @version 1.0
 */
public interface QuaAuthMgtService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��12��4�� ����4:27:51
	 * <AUTHOR>
	 */
	Integer save(QuaAuthMgt record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��12��4�� ����4:27:51
	 * <AUTHOR>
	 */
	Integer update(QuaAuthMgt record);
	void quaMgtAudit(MgtAuditParam param);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��12��4�� ����4:27:51
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedQuaMgt
	 * @date 2024��12��4�� ����4:27:51
	 * <AUTHOR>
	 */
	QuaAuthMgt selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedQuaMgt>
	 * @date 2024��12��4�� ����4:27:51
	 * <AUTHOR>
	 */
	DataSet<QuaAuthMgt> getDataSetList(Page page, QuaAuthMgt record);
	DataSet<QuaMgtSchedule> getSchedule(Page page, QuaAuthMgt record);
	DataSet<QuaMgtAndDetl> getQuaMgtAndDetl(Page page, QuaAuthMgtParam record);
	Integer deleteByQuaAuthType(String quaAuthType);
	Integer addCategoryDict(CategoryDict record);
	CategoryDict selectCategoryDictById(String categoryDictId);
	
	List<Map<String,Object>> getQuaAuthLvAndCnt();
	List<CategoryDict> getQuaAuthtypeAndCnt();
	List<Map<String,Object>> selectProcessByMgtid(String mgtId);
}
