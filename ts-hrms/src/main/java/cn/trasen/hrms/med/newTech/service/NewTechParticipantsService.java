package cn.trasen.hrms.med.newTech.service;

import java.util.List;

import cn.trasen.hrms.med.newTech.model.NewTechParticipants;

public interface NewTechParticipantsService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025-05-13 14:23:40
	 * <AUTHOR>
	 */
	Integer save(NewTechParticipants record);

	/**
	 * @Title selectByNewTechId
	 * @Description 根据新技术ID查询参与人员
	 * @param newTechId
	 * @date 2025-05-14 14:23:40
	 * <AUTHOR>
	 */
	List<NewTechParticipants> selectByNewTechId(String newTechId);

}
