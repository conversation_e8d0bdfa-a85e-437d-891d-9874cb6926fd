package cn.trasen.hrms.med.sped.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "med_sped_item")
@Setter
@Getter
public class SpedItem {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 项目编号
     */
    @Column(name = "item_code")
    @ApiModelProperty(value = "项目编号")
    private String itemCode;

    /**
     * 项目名称
     */
    @Column(name = "item_name")
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * 项目目标
     */
    @Column(name = "item_tagt")
    @ApiModelProperty(value = "项目目标")
    private String itemTagt;

    /**
     * 项目背景
     */
    @Column(name = "item_context")
    @ApiModelProperty(value = "项目背景")
    private String itemContext;

    /**
     * 项目负责人
     */
    @Column(name = "item_resper")
    @ApiModelProperty(value = "项目负责人")
    private String itemResper;
    
    /**
     * 项目负责人姓名
     */
    @Column(name = "item_resper_name")
    @ApiModelProperty(value = "项目负责人姓名")
    private String itemResperName;
    /**
     * 负责人科室
     */
    @Column(name = "resper_org")
    @ApiModelProperty(value = "负责人科室")
    private String resperOrg;
    /**
     * 负责人科室名称
     */
    @Column(name = "resper_org_name")
    @ApiModelProperty(value = "负责人科室名称")
    private String resperOrgName;

    /**
     * 资金来源
     */
    @Column(name = "capt_souc")
    @ApiModelProperty(value = "资金来源")
    private String captSouc;

    /**
     * 预算总金额
     */
    @Column(name = "budg_amt")
    @ApiModelProperty(value = "预算总金额")
    private BigDecimal budgAmt;

    /**
     * 实施单位
     */
    @Column(name = "impe_org_name")
    @ApiModelProperty(value = "实施单位")
    private String impeOrgName;

    /**
     * 开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "start_date")
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "end_date")
    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 评估指标
     */
    @Column(name = "eval_kpi")
    @ApiModelProperty(value = "评估指标")
    private String evalKpi;

    /**
     * 伦理审批号
     */
    @Column(name = "ethic_appr_no")
    @ApiModelProperty(value = "伦理审批号")
    private String ethicApprNo;

    /**
     * 伦理委员会名称
     */
    @Column(name = "ethic_council_name")
    @ApiModelProperty(value = "伦理委员会名称")
    private String ethicCouncilName;

    /**
     * 合作单位
     */
    @Column(name = "coop_org_name")
    @ApiModelProperty(value = "合作单位")
    private String coopOrgName;

    /**
     * 附件KEY
     */
    @Column(name = "file_key")
    @ApiModelProperty(value = "附件KEY")
    private String fileKey;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    private String startDateSearch;
    
    @Transient
    private String endDateSearch;
}