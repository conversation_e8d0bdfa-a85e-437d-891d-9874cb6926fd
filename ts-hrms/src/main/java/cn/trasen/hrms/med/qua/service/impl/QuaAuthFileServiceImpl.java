package cn.trasen.hrms.med.qua.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.qua.dao.QuaAuthFileMapper;
import cn.trasen.hrms.med.qua.model.QuaAuthFile;
import cn.trasen.hrms.med.qua.service.QuaAuthFileService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedQuaAuthFileServiceImpl
 * @Description TODO
 * @date 2024��12��13�� ����9:26:48
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class QuaAuthFileServiceImpl implements QuaAuthFileService {

	@Autowired
	private QuaAuthFileMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(QuaAuthFile record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setCreateDept(user.getDeptcode());
			record.setCreateDeptName(user.getDeptname());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getDeptcode());
			record.setSsoOrgName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(QuaAuthFile record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		QuaAuthFile record = new QuaAuthFile();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public QuaAuthFile selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<QuaAuthFile> getDataSetList(Page page, QuaAuthFile record) {
		Example example = new Example(QuaAuthFile.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StrUtil.isNotEmpty(record.getMgtId())){
			criteria.andEqualTo("mgtId", record.getMgtId());
		}
		List<QuaAuthFile> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public QuaAuthFile getByFileKey(String fileKey) {
		if(StrUtil.isNotEmpty(fileKey)){
			Example example = new Example(QuaAuthFile.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("fileKey", fileKey);
			List<QuaAuthFile> list = mapper.selectByExample(example);
			if(CollUtil.isNotEmpty(list)){
				return list.get(0);
			}
		}
		return null;
	}
}
