package cn.trasen.hrms.med.cslt.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltScduParam {
	
    private String id;
    private String csltType;
    private String csltMold;
    private String csltLv;
    private String hospArea;
    private String orgId;
    private String searchType;//查询类别: 0 7日1本周2上周3下周4自定义时间范围
    private String startDate;//开始日期 yyyy-mm-dd
    private String endDate;//结束日期 yyyy-mm-dd
    private String csltStatus;
    private String copyType; //复制类型  1复制上周2复制上月
    
    private String appyOrgId;//申请科室id,多个用,隔开
    private String appyOrgName;//申请科室名称
    private String patnName;//患者姓名
    private String patnInpNo;//患者住院号
    
    private List<String> orgIdList;
    private String scduResult;//排班结果1已排班,0未排班
    
    private String sord;
    
    private String pageSize;
    
    private String index;
    
    private String searchKey;
    
    private String maxJslRate;
    
    private String minJslRate;
    
    private String maxAvgScore;
    
    private String minAvgScore;
    
    private String empId;
    
    private Boolean isAll;

    private List<String> appyOrgIdList;
    private List<String> headlersDateList;
    
    private String csltStatusNew;//
    private String isCsltOt;//
    private String isCsltAbn;//
    private String conDocType;//会诊人员类别

}