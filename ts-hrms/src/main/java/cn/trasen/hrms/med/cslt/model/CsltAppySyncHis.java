package cn.trasen.hrms.med.cslt.model;

import javax.persistence.Column;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CsltAppySyncHis {
	@ApiModelProperty(value = "申请单ID")
    private String csltAppyId;
	
	@ApiModelProperty(value = "申请单号")
	private String appyNo;

    @ApiModelProperty(value = "会诊类别")
    private String csltType;

    @ApiModelProperty(value = "会诊类型")
    private String csltMold;

    @ApiModelProperty(value = "会诊级别")
    private String csltLv;

    @ApiModelProperty(value = "院区")
    private String hospArea;

    @ApiModelProperty(value = "被邀科室ID")
    private String csltOrgId;
    
    @ApiModelProperty(value = "被邀科室名称")
    private String csltOrgName;

    @ApiModelProperty(value = "申请会诊时间")
    private String csltTime;

    @ApiModelProperty(value = "患者ID")
    private String patnId;
    
    @ApiModelProperty(value = "患者姓名")
    private String patnName;

    @ApiModelProperty(value = "性别")
    private String patnGend;

    @ApiModelProperty(value = "年龄")
    private String patnAge;

    @ApiModelProperty(value = "床号")
    private String patnBedno;

    @ApiModelProperty(value = "住院号")
    private String patnInpNo;

   
    @ApiModelProperty(value = "科室")
    private String patnOrgName;

    @ApiModelProperty(value = "诊断")
    private String patnIcdName;

    @ApiModelProperty(value = "申请时间")
    private String appyTime;

    @ApiModelProperty(value = "申请科室")
    private String appyOrgName;

    @ApiModelProperty(value = "申请人")
    private String appyEmpName;

    @ApiModelProperty(value = "申请人联系电话")
    private String appyTel;

    @ApiModelProperty(value = "病史及检查")
    private String illhis;

    @ApiModelProperty(value = "会诊目的")
    private String pup;

    @ApiModelProperty(value = "安排状态0未安排1已安排")
    private String actStatus;
   
    @ApiModelProperty(value = "是否安排超时0否1是")
    private String isActOt;
    
    @ApiModelProperty(value = "是否安排异常0否1是")
    private String isActAbn;
   
    @ApiModelProperty(value = "会诊状态0未会诊1已会诊")
    private String csltStatus;
    
    @ApiModelProperty(value = "是否会诊超时0否1是")
    private String isCsltOt;
    
    @ApiModelProperty(value = "是否会诊异常0否1是")
    private String isCsltAbn;
    
    @ApiModelProperty(value = "安排时间")
    private String actTime;
    
    @ApiModelProperty(value = "安排人账号")
    private String actUser;

    @ApiModelProperty(value = "安排人名称")
    private String actUserName;
    
    @ApiModelProperty(value = "安排会诊医生ID")
    private String actEmployeeId;

    @ApiModelProperty(value = "安排会诊医生名称")
    private String actEmployeeName;

    @ApiModelProperty(value = "安排医生工号")
    private String actEmployeeNo;

    @ApiModelProperty(value = "安排医生技术职称")
    private String actJobtitle;

    @ApiModelProperty(value = "安排医生手机号码")
    private String actTel;

    @ApiModelProperty(value = "计划会诊开始时间")
    private String actStartTime;
    
    @ApiModelProperty(value = "计划会诊结束时间")
    private String actEndTime;

    @ApiModelProperty(value = "是否发送通知")
    private String notcFlag;

    @ApiModelProperty(value = "完成会诊时间")
    private String fnsTime;

    @ApiModelProperty(value = "完成会诊医生ID")
    private String fnsEmployeeId;

    @ApiModelProperty(value = "完成会诊医生名称")
    private String fnsEmployeeName;

    @ApiModelProperty(value = "完成医生工号")
    private String fnsEmployeeNo;
    
    @ApiModelProperty(value = "完成医生技术职称")
    private String fnsJobtitle;
   
    @ApiModelProperty(value = " 完成医生手机号码")
    private String fnsTel;
    
    @ApiModelProperty(value = "会诊意见(申请科室)")
    private String appyOrgDscr;
    
    @ApiModelProperty(value = "会诊意见(会诊科室)")
    private String csltOrgDscr;
    
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;
    
    @ApiModelProperty(value = "申请科室id")
    private String appyOrgId;
    
    @ApiModelProperty(value = "申请科室所属院区")
    private String appyHospArea;
    
    @ApiModelProperty(value = "申请人id")
    private String appyEmpId;
    
    @ApiModelProperty(value = "会诊目的评分")
    private String hzmdScore;
    
    @ApiModelProperty(value = "会诊资料评分")
    private String hzzlScore;
    
    @ApiModelProperty(value = "会诊必要性评分")
    private String hzbyxScore;
    
    @ApiModelProperty(value = "会诊目的答复评分")
    private String hzmddfScore;
    
    @ApiModelProperty(value = "会诊意见评分")
    private String hzyjScore;
    
    @ApiModelProperty(value = "会诊医师人员类别")
    private String conDocType;
}
