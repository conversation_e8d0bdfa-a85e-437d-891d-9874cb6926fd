<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.major.dao.MajorEventsMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.major.model.MajorEvents">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="events_date" jdbcType="TIMESTAMP" property="eventsDate" />
    <result column="events_type" jdbcType="VARCHAR" property="eventsType" />
    <result column="events_name" jdbcType="VARCHAR" property="eventsName" />
    <result column="events_dscr" jdbcType="VARCHAR" property="eventsDscr" />
    <result column="events_infl" jdbcType="VARCHAR" property="eventsInfl" />
    <result column="dspo_mes" jdbcType="VARCHAR" property="dspoMes" />
    <result column="srvy_ana" jdbcType="VARCHAR" property="srvyAna" />
    <result column="impr_adv" jdbcType="VARCHAR" property="imprAdv" />
    <result column="approval_status" jdbcType="CHAR" property="approvalStatus" />
    <result column="approval_time" jdbcType="TIMESTAMP" property="approvalTime" />
    <result column="approval_user" jdbcType="VARCHAR" property="approvalUser" />
    <result column="approval_user_name" jdbcType="VARCHAR" property="approvalUserName" />
    <result column="approval_dscr" jdbcType="VARCHAR" property="approvalDscr" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_dept" jdbcType="VARCHAR" property="createDept" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
  </resultMap>
</mapper>