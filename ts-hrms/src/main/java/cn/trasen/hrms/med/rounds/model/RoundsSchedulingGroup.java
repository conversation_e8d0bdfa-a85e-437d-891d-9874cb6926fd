package cn.trasen.hrms.med.rounds.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_rounds_scheduling_group")
@Setter
@Getter
public class RoundsSchedulingGroup {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 排班id
     */
    @Column(name = "scheduling_id")
    @ApiModelProperty(value = "排班id")
    private String schedulingId;

    /**
     * 小组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "小组id")
    private String groupId;

    /**
     * 选择的小组用户
     */
    @Column(name = "group_user_code")
    @ApiModelProperty(value = "选择的小组用户")
    private String groupUserCode;

    /**
     * 选择的小组用户名称
     */
    @Column(name = "group_user_name")
    @ApiModelProperty(value = "选择的小组用户名称")
    private String groupUserName;

    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}