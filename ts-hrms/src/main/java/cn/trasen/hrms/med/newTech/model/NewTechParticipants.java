package cn.trasen.hrms.med.newTech.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 新技术参与人员表
 * @date 2025-05-12 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "med_new_tech_participants")
@Setter
@Getter
public class NewTechParticipants {
	
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
	
    @Column(name = "new_tech_id")
    @ApiModelProperty(value = "关联新技术ID")
    private String newTechId;
	
    @Column(name = "employee_id")
    @ApiModelProperty(value = "参与人员ID")
    private String employeeId;
	
    @Column(name = "employee_no")
    @ApiModelProperty(value = "参与人员编码")
    private String employeeNo;
	
    @Column(name = "employee_name")
    @ApiModelProperty(value = "参与人员姓名")
    private String employeeName;

    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    @Column(name = "hosp_area")
    @ApiModelProperty(value = "院区")
    private String hospArea;

//    @Column(name = "part_hosp_name")
//    @ApiModelProperty(value = "参与人员所属院区名称")
//    private String partHospName;

    @Column(name = "org_id")
    @ApiModelProperty(value = "科室ID")
    private String orgId;

    @Column(name = "org_name")
    @ApiModelProperty(value = "科室名称")
    private String orgName;

    @Column(name = "degree")
    @ApiModelProperty(value = "学历")
    private String degree;

    @Column(name = "jobtitle")
    @ApiModelProperty(value = "职称")
    private String jobtitle;

    @Column(name = "tel")
    @ApiModelProperty(value = "手机号")
    private String tel;

    @Column(name = "job_description")
    @ApiModelProperty(value = "工作描述")
    private String jobDescription;
    
    
    /****************审计信息 *************/

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识：N-未删除，Y-已删除")
    private String isDeleted;

    @Transient
    @ApiModelProperty(value = "职务")
    private String dutyTitle;

}
