package cn.trasen.hrms.med.rounds.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.med.rounds.dao.RoundsGroupRulesMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsSchedulingGroupMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsSchedulingMapper;
import cn.trasen.hrms.med.rounds.dao.RoundsTaskMapper;
import cn.trasen.hrms.med.rounds.model.RoundsGroupRules;
import cn.trasen.hrms.med.rounds.model.RoundsScheduling;
import cn.trasen.hrms.med.rounds.model.RoundsSchedulingGroup;
import cn.trasen.hrms.med.rounds.model.RoundsTask;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroup;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules;
import cn.trasen.hrms.med.rounds.service.RoundsSchedulingService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupRulesService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskService;
import cn.trasen.hrms.utils.SendMessageUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RoundsTaskServiceImpl
 * @Description TODO
 * @date 2025��3��7�� ����3:26:44
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RoundsTaskServiceImpl implements RoundsTaskService {

	@Autowired
	private RoundsTaskMapper mapper;
	
	@Autowired
	private RoundsSchedulingMapper schedulingMapper;
	
	@Autowired
	private RoundsSchedulingGroupMapper schedulingGroupMapper;
	
	@Autowired
	private RoundsGroupRulesMapper groupRulesMapper;
	
	@Autowired
	private RoundsTaskGroupService taskGroupService;
	
	@Autowired
	private RoundsSchedulingService schedulingService;
	
	@Autowired
	private RoundsTaskGroupRulesService taskGroupRulesService;

	@Autowired
	private HrmsEmployeeFeignService hrmsEmployeeFeignService;

	@Autowired
	private DictItemFeignService dictItemFeignService;
	@Transactional(readOnly = false)
	@Override
	public Integer save(RoundsTask record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setStatus("1");//待扣分
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RoundsTask record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RoundsTask record = new RoundsTask();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RoundsTask selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RoundsTask> getDataSetList(Page page, RoundsTask record) {
		Example example = new Example(RoundsTask.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RoundsTask> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public DataSet<RoundsTask> getPageList(Page page, RoundsTask record) {
		// TODO Auto-generated method stub
		List<RoundsTask> records = mapper.getPageList(page, record);
		if(CollectionUtils.isNotEmpty(records)) {
			Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
			for(RoundsTask task : records) {
				task.setHospAreaText(hospAreaMap.get(task.getHospArea()));
				if(!StringUtils.isEmpty(task.getStatus()) &&  "2".equals(task.getStatus())) {
					RoundsTaskGroup taskGroup  = new RoundsTaskGroup();
					taskGroup.setTaskId(task.getId());
					taskGroup.setStatusParam("0");//查询打回数据
					List<RoundsTaskGroup> taskGroupList =  taskGroupService.getRoundsTaskGroupByParam(taskGroup);
					if(CollectionUtils.isNotEmpty(taskGroupList)) {
						//当前登录人是查房秘书，则是打回状态,
						if(!StringUtils.isEmpty(task.getSecretaryUser()) && task.getSecretaryUser().equals(UserInfoHolder.getCurrentUserCode())) {
							task.setStatus("0");//打回状态
						}
						for(RoundsTaskGroup roundsTaskGroup : taskGroupList) {
							if(!StringUtils.isEmpty(roundsTaskGroup.getGroupUserCode())) {
								if(roundsTaskGroup.getGroupUserCode().contains(UserInfoHolder.getCurrentUserCode())) {//打回数据组包含当前登录人，则是打回状态，其它则是2待审核状态
									task.setStatus("0");//打回状态
								}
							}
						}
					}
				}
				
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public void addRoundsTask(Integer number) {
		// TODO Auto-generated method stub
		// 获取当前时间
        Date currentDate = new Date();
        // 使用Calendar类加一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DATE, number); // 加N天
        // 获取加N天后的时间
        Date nextDayDate = calendar.getTime();
        
		//查询当天会诊的科室、生成查房任务
		Example example = new Example(RoundsScheduling.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("status","0");
		criteria.andEqualTo("roundsTime",DateUtil.format(nextDayDate, "yyyy-MM-dd"));
		List<RoundsScheduling> SchedulingList = schedulingMapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(SchedulingList)) {
			for(RoundsScheduling roundsScheduling : SchedulingList) {
				insertTask(roundsScheduling);
			}
		}
	}
	
	@Override
	@Transactional(readOnly = false)
	public void handAddRoundsTask() {
		
		List<RoundsScheduling> schedulingList = schedulingMapper.getNoTaskData();
		
		if(CollectionUtils.isNotEmpty(schedulingList)) {
			for(RoundsScheduling roundsScheduling : schedulingList) {
				insertTask(roundsScheduling);
			}
		}
	}

	public void insertTask(RoundsScheduling roundsScheduling) {
		//根据排班id，查询排班组
		Example example99 = new Example(RoundsTask.class);
		Example.Criteria criteria99 = example99.createCriteria();
		criteria99.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria99.andEqualTo("schedulingId",roundsScheduling.getId());//根据排班id查询是否已经存在对应任务
		List<RoundsTask> taskList = mapper.selectByExample(example99);
		if(CollectionUtils.isEmpty(taskList)) {//不存在，则新增任务
			RoundsTask roundsTask = new RoundsTask();
			roundsTask.setSchedulingId(roundsScheduling.getId());
			
			Map<String, Object> mapParam =  new HashMap<String,Object>();
			mapParam.put("orgId", roundsScheduling.getRoundsOrg());//科室id
			mapParam.put("roleId", "3");//科主任
			List<String> LeaderStringList = mapper.selectDeptLeader(mapParam);
			if(CollectionUtils.isNotEmpty(LeaderStringList)){
				String  leaderEmployeeIds = LeaderStringList.get(0);
				if(!StringUtils.isEmpty(leaderEmployeeIds)) {
					String leaderEmployeeId = leaderEmployeeIds.split(",")[0];//根据,隔开后取第一个
					//EmployeeResp employeeResp = hrmsEmployeeFeignService.findByEmployeeId(leaderEmployeeId).getObject();//根据id获取人员信息
					String employeeName   = mapper.selectEmployeeNameById(leaderEmployeeId);
					roundsTask.setDirectorUserCode(leaderEmployeeId);
					roundsTask.setDirectorUserName(employeeName);
				}
			}
			mapParam.put("roleId", "2");//护士长
			List<String> LeaderStringList2 = mapper.selectDeptLeader(mapParam);
			if(CollectionUtils.isNotEmpty(LeaderStringList2)){
				String  leaderEmployeeIds   = LeaderStringList2.get(0);
				if(!StringUtils.isEmpty(leaderEmployeeIds)) {
					String leaderEmployeeId = leaderEmployeeIds.split(",")[0];//根据,隔开后取第一个
					//EmployeeResp employeeResp = hrmsEmployeeFeignService.findByEmployeeId(leaderEmployeeId).getObject();//根据id获取人员信息
					String employeeName   = mapper.selectEmployeeNameById(leaderEmployeeId);
					roundsTask.setNurseUserCode(leaderEmployeeId);
					roundsTask.setNurseUserName(employeeName);
				}
			}
			roundsTask.setCreateUser(roundsScheduling.getSecretaryUser());//默认查房秘书
			roundsTask.setCreateUserName(roundsScheduling.getSecretaryUserName());
			roundsTask.setUpdateUser(roundsScheduling.getSecretaryUser());
			roundsTask.setUpdateUserName(roundsScheduling.getSecretaryUserName());
			save(roundsTask);//插入任务
			
			//根据排班id，查询排班组;只查询需要评分的
			Example example2 = new Example(RoundsSchedulingGroup.class);
			Example.Criteria criteria2 = example2.createCriteria();
			criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria2.andEqualTo("schedulingId",roundsScheduling.getId());
			List<RoundsSchedulingGroup> SchedulingGroupList = schedulingGroupMapper.selectByExample(example2);
			if(CollectionUtils.isNotEmpty(SchedulingGroupList)) {
				for(RoundsSchedulingGroup schedulingGroup : SchedulingGroupList) {
					RoundsTaskGroup roundsTaskGroup = new RoundsTaskGroup();
					roundsTaskGroup.setTaskId(roundsTask.getId());
					roundsTaskGroup.setSchedulingId(schedulingGroup.getSchedulingId());
					roundsTaskGroup.setGroupId(schedulingGroup.getGroupId());
					roundsTaskGroup.setGroupUserCode(schedulingGroup.getGroupUserCode());
					roundsTaskGroup.setGroupUserName(schedulingGroup.getGroupUserName());
					taskGroupService.save(roundsTaskGroup);//插入任务组
					
					//根据排班id，查询排班组
					Example example3 = new Example(RoundsGroupRules.class);
					Example.Criteria criteria3 = example3.createCriteria();
					criteria3.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria3.andEqualTo("groupId",schedulingGroup.getGroupId());//查房小组id
					List<RoundsGroupRules> groupRulesList = groupRulesMapper.selectByExample(example3);
					for(RoundsGroupRules roundsGroupRules : groupRulesList) {
						RoundsTaskGroupRules roundsTaskGroupRules = new RoundsTaskGroupRules();
						roundsTaskGroupRules.setTaskId(roundsTask.getId());
						roundsTaskGroupRules.setSchedulingId(schedulingGroup.getSchedulingId());
						roundsTaskGroupRules.setGroupId(roundsGroupRules.getGroupId());
						roundsTaskGroupRules.setRulesId(roundsGroupRules.getId());
						roundsTaskGroupRules.setDeductPoints(BigDecimal.ZERO);//扣分默认赋值0
						roundsTaskGroupRules.setScore(roundsGroupRules.getScoreValue());//得分默认赋值满分
						taskGroupRulesService.save(roundsTaskGroupRules);//插入任务组评分细则
					}
				}
			}
		}
	}

	@Override
	public RoundsTask getExamineApproveById(String id) {
		// TODO Auto-generated method stub
		RoundsTask task = mapper.getExamineApproveById(id);
		Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
		task.setHospAreaText(hospAreaMap.get(task.getHospArea()));
		return task;
	}

	@Override
	public DataSet<RoundsTask> getScoreOverview(Page page, RoundsTask record) {
		// TODO Auto-generated method stub
		List<RoundsTask> records = mapper.getScoreOverview(page, record);//获取得分一览
		if(CollectionUtils.isNotEmpty(records)) {
			Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
			for(RoundsTask task : records) {
				task.setHospAreaText(hospAreaMap.get(task.getHospArea()));
			}
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<Map<String, Object>> getQualityMonthlyReport(Page page, RoundsTask record) {
		// TODO Auto-generated method stub
		List<Map<String, Object>> records = mapper.getQualityMonthlyReport(page, record);//查房质量月度报表
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer approveUpdate(RoundsTask record) {
		// TODO Auto-generated method stub
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		if(!StringUtils.isEmpty(record.getRoundsTaskGroupList())) {
			for(RoundsTaskGroup roundsTaskGroup : record.getRoundsTaskGroupList()) {
				if(!StringUtils.isEmpty(roundsTaskGroup.getRoundsTaskGroupRulesList())) {
					for(RoundsTaskGroupRules roundsTaskGroupRules : roundsTaskGroup.getRoundsTaskGroupRulesList()) {
						taskGroupRulesService.update(roundsTaskGroupRules);
					}
				}
				taskGroupService.update(roundsTaskGroup);
			}
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public List<Map<String, Object>> getHospAreaDataList(RoundsTask record) {
		// TODO Auto-generated method stub
		List<Map<String, Object>> hospAreaDataList = mapper.getHospAreaDataList(record);
		if(CollectionUtils.isNotEmpty(hospAreaDataList)) {
			for (Map<String, Object> map : hospAreaDataList) {
				Map<String, String>  hospAreaMap  = convertDictMap("hosp_area");//获取院区字典map
				String hospArea = map.get("hospArea").toString();
				map.put("hospAreaText",hospAreaMap.get(hospArea));
			}
		}
		return hospAreaDataList;
	}

	@Override
	public List<RoundsTask> getTaskDeptQuestionData(RoundsTask record) {
		// TODO Auto-generated method stub
		List<RoundsTask> records = mapper.getTaskDeptQuestionData(record);
		if(CollectionUtils.isNotEmpty(records)) {
			for(RoundsTask task : records) {
				RoundsTaskGroup taskGroup = new RoundsTaskGroup();
				taskGroup.setTaskId(task.getId());
				List<RoundsTaskGroup>  roundsTaskGroupList = taskGroupService.getRoundsTaskGroupByParam(taskGroup);
				task.setRoundsTaskGroupList(roundsTaskGroupList);
			}
		}
		return records;
	}
	
	
	private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

	@Transactional(readOnly = false)
	@Override
	public void correctionOverdueMessage(Integer number) {
			// TODO Auto-generated method stub
			// 获取当前时间
	        Date currentDate = new Date();
	        // 使用Calendar类加一天
	        Calendar calendar = Calendar.getInstance();
	        calendar.setTime(currentDate);
	        calendar.add(Calendar.DATE, number); // 加N天
	        // 获取加N天后的时间
	        Date nextDayDate = calendar.getTime();
	        
			//查询明天整改到期的数据
			Example example = new Example(RoundsTask.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("status","3");
			criteria.andEqualTo("rectificationDeadline",DateUtil.format(nextDayDate, "yyyy-MM-dd"));
			List<RoundsTask> roundsTaskList = mapper.selectByExample(example);
			if(CollectionUtils.isNotEmpty(roundsTaskList)) {
				for(RoundsTask task : roundsTaskList) {
					if(!StringUtils.isEmpty(task.getDirectorUserCode())) {//科主任
						Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(task.getDirectorUserCode());//获取用户手机号码
						if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
							String content =employeeMap.get("employeeName").toString() +"：请于【明日18:00之前】在智慧医务完成医疗行政查房的整改，近期将组织整改督查。";
							SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
						}
					}
					
					if(!StringUtils.isEmpty(task.getNurseUserCode())) {//护士长
						Map<String,Object> employeeMap =  schedulingMapper.selectEmployeePhoneNumberById(task.getNurseUserCode());//获取用户手机号码
						if(employeeMap !=null && !employeeMap.isEmpty() && employeeMap.containsKey("phoneNumber") && !StringUtils.isEmpty(employeeMap.get("phoneNumber").toString())) {
							String content =employeeMap.get("employeeName").toString() +"：请于【明日18:00之前】在智慧医务完成医疗行政查房的整改，近期将组织整改督查。";
							SendMessageUtils.sendMessageHnsrmyy(employeeMap.get("phoneNumber").toString(),content,"系统管理员","1");
						}
					}
				}
			}
		
	}
}
