package cn.trasen.hrms.med.rounds.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupRulesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName RoundsTaskGroupRulesController
 * @Description TODO
 * @date 2025��3��7�� ����4:40:12
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "查房任务分组细则表")
public class RoundsTaskGroupRulesController {

	private transient static final Logger logger = LoggerFactory.getLogger(RoundsTaskGroupRulesController.class);

	@Autowired
	private RoundsTaskGroupRulesService roundsTaskGroupRulesService;

	/**
	 * @Title saveRoundsTaskGroupRules
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/roundsTaskGroupRules/save")
	public PlatformResult<String> saveRoundsTaskGroupRules(@RequestBody RoundsTaskGroupRules record) {
		try {
			roundsTaskGroupRulesService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateRoundsTaskGroupRules
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/roundsTaskGroupRules/update")
	public PlatformResult<String> updateRoundsTaskGroupRules(@RequestBody RoundsTaskGroupRules record) {
		try {
			roundsTaskGroupRulesService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectRoundsTaskGroupRulesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RoundsTaskGroupRules>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/roundsTaskGroupRules/{id}")
	public PlatformResult<RoundsTaskGroupRules> selectRoundsTaskGroupRulesById(@PathVariable String id) {
		try {
			RoundsTaskGroupRules record = roundsTaskGroupRulesService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteRoundsTaskGroupRulesById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/roundsTaskGroupRules/delete/{id}")
	public PlatformResult<String> deleteRoundsTaskGroupRulesById(@PathVariable String id) {
		try {
			roundsTaskGroupRulesService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectRoundsTaskGroupRulesList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<RoundsTaskGroupRules>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/roundsTaskGroupRules/list")
	public DataSet<RoundsTaskGroupRules> selectRoundsTaskGroupRulesList(Page page, RoundsTaskGroupRules record) {
		return roundsTaskGroupRulesService.getDataSetList(page, record);
	}
	
	
	/**
	 * 
	 * @Title selectRoundsTaskGroupRulesById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<RoundsTaskGroupRules>
	 * @date 2025��3��7�� ����4:40:12
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据任务和小组id获取评分细则", notes = "详情")
	@GetMapping("/api/roundsTaskGroupRules/getByTaskGroupId")
	public PlatformResult<List<RoundsTaskGroupRules>> getByTaskGroupId(RoundsTaskGroupRules record) {
		try {
			List<RoundsTaskGroupRules> records = roundsTaskGroupRulesService.getByTaskGroupId(record);
			return PlatformResult.success(records);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
