package cn.trasen.hrms.med.patient.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Table(name = "med_patient_birth")
@Setter
@Getter
public class PatientBirth {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 患者主键
     */
    @Column(name = "patn_id")
    @ApiModelProperty(value = "患者主键")
    private String patnId;

    /**
     * 患者住院号
     */
    @Column(name = "patn_no")
    @ApiModelProperty(value = "患者住院号")
    private String patnNo;

    /**
     * 分娩日期
     */
    @Column(name = "birth_date")
    @ApiModelProperty(value = "分娩日期")
    private Date birthDate;

    /**
     * 孕周
     */
    @Column(name = "gestational_weeks")
    @ApiModelProperty(value = "孕周")
    private String gestationalWeeks;

    /**
     * 诊断
     */
    @ApiModelProperty(value = "诊断")
    private String diagnosis;

    /**
     * 特殊情况
     */
    @Column(name = "exceptional_case")
    @ApiModelProperty(value = "特殊情况")
    private String exceptionalCase;
    
    /**
     * 操作日期
     */
    @Column(name = "book_date")
    @ApiModelProperty(value = "操作日期")
    private Date bookDate;
    
    /**
     * 创建日期
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建日期")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新日期
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新日期")
    private Date updateDate;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;
}