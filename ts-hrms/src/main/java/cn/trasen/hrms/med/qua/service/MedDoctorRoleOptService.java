package cn.trasen.hrms.med.qua.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.qua.model.MedDoctorRoleOpt;

/**
 * @ClassName MedDoctorRoleOptService
 * @Description TODO
 * @date 2025��3��6�� ����4:34:12
 * <AUTHOR>
 * @version 1.0
 */
public interface MedDoctorRoleOptService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��3��6�� ����4:34:12
	 * <AUTHOR>
	 */
	Integer save(MedDoctorRoleOpt record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��3��6�� ����4:34:12
	 * <AUTHOR>
	 */
	Integer update(MedDoctorRoleOpt record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��3��6�� ����4:34:12
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedDoctorRoleOpt
	 * @date 2025��3��6�� ����4:34:12
	 * <AUTHOR>
	 */
	MedDoctorRoleOpt selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedDoctorRoleOpt>
	 * @date 2025��3��6�� ����4:34:12
	 * <AUTHOR>
	 */
	DataSet<MedDoctorRoleOpt> getDataSetList(Page page, MedDoctorRoleOpt record);

	DataSet<MedDoctorRoleOpt> listKanban(Page page, MedDoctorRoleOpt record);
}
