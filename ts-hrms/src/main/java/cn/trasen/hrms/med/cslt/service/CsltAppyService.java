package cn.trasen.hrms.med.cslt.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.cslt.model.CsltAppy;

/**
 * @ClassName MedCsltAppyService
 * @Description TODO
 * @date 2024��11��17�� ����10:28:07
 * <AUTHOR>
 * @version 1.0
 */
public interface CsltAppyService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��17�� ����10:28:07
	 * <AUTHOR>
	 */
	Integer save(CsltAppy record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��17�� ����10:28:07
	 * <AUTHOR>
	 */
	Integer update(CsltAppy record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��17�� ����10:28:07
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return MedCsltAppy
	 * @date 2024��11��17�� ����10:28:07
	 * <AUTHOR>
	 */
	CsltAppy selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<MedCsltAppy>
	 * @date 2024��11��17�� ����10:28:07
	 * <AUTHOR>
	 */
	DataSet<CsltAppy> getDataSetList(Page page, CsltAppy record);
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时执行会诊状态
	  -- 作者: GW
	  -- 创建时间: 2024年11月18日
	  -- 
	  -- =============================================
	 */
	void updtIsOtStatus();
	
	List<CsltAppy> fillDict(List<CsltAppy> appys);
	
	CsltAppy fillDictAppy(CsltAppy appy);
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时拉取HIS影子库会诊信息
	  -- 作者: wch
	  -- 创建时间: 2025年01月15日
	  -- 
	  -- =============================================
	 */
	void updateOrSaveCsltAppyByHis();
	
	void updateOrSaveCsltAppyByHis2();

	void syncHisEmployee();

	void syncHrmEmployee();
	
}
