<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.rounds.dao.RoundsGroupMemberMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.rounds.model.RoundsGroupMember">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="hosp_area" jdbcType="VARCHAR" property="hospArea" />
    <result column="group_member_code" jdbcType="VARCHAR" property="groupMemberCode" />
    <result column="group_member_name" jdbcType="VARCHAR" property="groupMemberName" />
    <result column="scheduling_code" jdbcType="VARCHAR" property="schedulingCode" />
    <result column="scheduling_name" jdbcType="VARCHAR" property="schedulingName" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
</mapper>