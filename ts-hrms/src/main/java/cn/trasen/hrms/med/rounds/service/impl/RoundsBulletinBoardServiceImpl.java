package cn.trasen.hrms.med.rounds.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.nacos.client.config.utils.ContentUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.rounds.dao.RoundsBulletinBoardMapper;
import cn.trasen.hrms.med.rounds.model.RoundsBulletinBoard;
import cn.trasen.hrms.med.rounds.service.RoundsBulletinBoardService;
import cn.trasen.hrms.salary.utils.VueTableEntity;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RoundsBulletinBoardServiceImpl implements RoundsBulletinBoardService{

	@Autowired
	private RoundsBulletinBoardMapper mapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Override
	public Map<String, Object> getCompletionRate(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getCompletionRate(record);
	}
	
	@Override
	public Map<String, Object> getProblemReductionRate(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getProblemReductionRate(record);
	}
	
	@Override
	public Map<String, Object> getProblemCompletionRate(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getProblemCompletionRate(record);
	}
	
	@Override
	public Map<String, Object> getProblemRepeatRate(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getProblemRepeatRate(record);
	}

	@Override
	public List<VueTableEntity> getRoundsDeptScoreSituationHeader(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		retVueTableEntity.add(new VueTableEntity("院区","hosp_area",null,15,null));
		retVueTableEntity.add(new VueTableEntity("查房科室","rounds_org_name",null,10,null));
		retVueTableEntity.add(new VueTableEntity("得分","score",null,15,null));//用周几做字段
		List<Map<String,Object>>  groupList =   mapper.getRoundsDeptScoreSituationHeader(record);//查询科室数据所有的组名称和id
		if(CollectionUtils.isNotEmpty(groupList)) {
			// 遍历List
	        for (Map<String, Object> map : groupList) {
	        	retVueTableEntity.add(new VueTableEntity(map.get("group_name").toString(),map.get("group_id").toString(),null,20,null));
	        }
		}
		return retVueTableEntity;
	}

	@Override
	public List<Map<String, Object>> getRoundsDeptScoreSituationData(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		List<Map<String,Object>>  groupList =   mapper.getRoundsDeptScoreSituationHeader(record);//查询科室数据所有的组名称和id
		// 创建一个空的List<String>对象
        List<String> roundsGroupIdList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(groupList)) {
			// 遍历List
	        for (Map<String, Object> map : groupList) {
	        	if (map.containsKey("group_id")) {
	        		roundsGroupIdList.add(map.get("group_id").toString());
	        	}
	        }
		}
		record.setRoundsGroupIdList(roundsGroupIdList);
		return mapper.getRoundsDeptScoreSituationData(record);
	}

	@Override
	public List<Map<String, Object>> getOverdueNotRectified(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getOverdueNotRectified(record);
	}

	@Override
	public List<Map<String, Object>> getGroupProblemChart(RoundsBulletinBoard record) {
		// TODO Auto-generated method stub
		return mapper.getGroupProblemChart(record);
	}

	@Override
	public List<Map<String, Object>> getRectifiedCount(RoundsBulletinBoard record) {
		
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		
		List<Map<String, Object>> rectifiedCount = mapper.getRectifiedCount(record);
		
		if(CollUtil.isNotEmpty(rectifiedCount)){
			rectifiedCount.forEach(i -> {
				if(null != i.get("hosp_area")) {
					DictItemResp area = areas.stream().filter(j -> StrUtil.equals((String)i.get("hosp_area"), j.getItemCode())).findFirst().orElse(null);
					i.put("hosp_area_name", null == area ? (String)i.get("hosp_area") : area.getItemName());
				}
			});
		}
		
		return rectifiedCount;
	}
	
	
}
