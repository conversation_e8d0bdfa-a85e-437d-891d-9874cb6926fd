package cn.trasen.hrms.med.newTech.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 新技术新项目评估表
 * @date 2025-05-19 11:52:53
 * <AUTHOR>
 * @version 1.0
 */
@Table(name = "med_new_tech_evaluation")
@Setter
@Getter
public class NewTechEevaluation {

    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    
    @Column(name = "workflow_inst_id")
    @ApiModelProperty(value = "流程实例ID")
    private String workflowInstId;
    
    @Column(name = "business_id")
    @ApiModelProperty(value = "流程业务ID")
    private String businessId;
    
    @Column(name = "process_by")
    @ApiModelProperty(value = "流程发起人")
    private String processBy ;
    
    @Column(name = "process_dept")
    @ApiModelProperty(value = "发起科室")
    private String processDept;

    @Column(name = "tech_code")
    @ApiModelProperty(value = "伦理号")
    private String techCode;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "evaluation_date")
    @ApiModelProperty(value = "评估日期")
    private Date evaluationDate;

    @Column(name = "evaluation_result")
    @ApiModelProperty(value = "评估结果")
    private String evaluationResult;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "stop_date_begin")
    @ApiModelProperty(value = "中止开始时间")
    private Date stopDateBegin;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Column(name = "stop_date_end")
    @ApiModelProperty(value = "中止结束时间")
    private Date stopDateEnd;
    
    /****************审计信息 *************/

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识：N-未删除，Y-已删除")
    private String isDeleted;

}
