package cn.trasen.hrms.med.newTech.emun;

/**
 * 技术类型
 * <AUTHOR>
 *
 */
public enum TechTypeEmun {
	
	OPERATION("1", "手术类"),  
	TREATMENT ("2", "治疗操作类"),  
	EXAMINATION("3", "检验检查类"),  
	OTHER("4", "其他类");
	
	private String name;

	private String code;

	TechTypeEmun(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	

	/**
	 * 根据编码获取name
	 * 
	 * @param code
	 * @return
	 */
	public static String convert(String code) {
		for (TechTypeEmun emun : values()) {
			if (emun.getCode().equals(code))
				return emun.name;
		}
		return null;
	}

}
