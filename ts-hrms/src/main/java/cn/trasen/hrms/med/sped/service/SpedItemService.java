package cn.trasen.hrms.med.sped.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.sped.model.SpedItem;

/**
 * @ClassName CustMedSpedItemService
 * @Description TODO
 * @date 2024��11��11�� ����3:00:15
 * <AUTHOR>
 * @version 1.0
 */
public interface SpedItemService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��11�� ����3:00:15
	 * <AUTHOR>
	 */
	Integer save(SpedItem record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��11�� ����3:00:15
	 * <AUTHOR>
	 */
	Integer update(SpedItem record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��11�� ����3:00:15
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CustMedSpedItem
	 * @date 2024��11��11�� ����3:00:15
	 * <AUTHOR>
	 */
	SpedItem selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CustMedSpedItem>
	 * @date 2024��11��11�� ����3:00:15
	 * <AUTHOR>
	 */
	DataSet<SpedItem> getDataSetList(Page page, SpedItem record);
}
