package cn.trasen.hrms.med.patient.service;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.patient.model.PatientTransferDept;

public interface PatientTransferDeptService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer save(PatientTransferDept record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer update(PatientTransferDept record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return PatientBirth
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	PatientTransferDept selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<PatientBirth>
	 * @date 2025��4��8�� ����4:48:25
	 * <AUTHOR>
	 */
	DataSet<PatientTransferDept> getDataSetList(Page page, PatientTransferDept record);
	
	void updateOrSavePatientTransferDept();
}
